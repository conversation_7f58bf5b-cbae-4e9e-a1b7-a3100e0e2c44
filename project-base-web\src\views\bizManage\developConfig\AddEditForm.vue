<!--
 * @Description: 新增编辑参数弹窗
 * @Version: 1.0
 * @Autor: lcm
 * @Date: 2023-03-16 10:13:31
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 13:51:51
-->
<template>
  <a-modal
    :width="700"
    :title="handelType === 'add' ? '新增参数' : '编辑参数'"
    :body-style="{ maxHeight: '600px', overflow: 'auto' }"
    wrap-class-name="cus-modal"
    :open="visible"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    ok-text="确认"
    cancel-text="取消"
    @ok="handleSubmit()"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="form" :rules="rules" label-align="left">
        <a-row :gutter="24">
          <a-col :md="12" :sm="24">
            <a-form-item name="name" label="参数名称" has-feedback>
              <a-input v-model:value="form.name" placeholder="请输入参数名称" :maxlength="30" @keyup="toPinyin" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item name="code" label="唯一编码" has-feedback>
              <a-input v-model:value="form.code" placeholder="请输入唯一编码" :maxlength="30" :disabled="handelType === 'edit'" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item name="sysFlag" class="sys-flag" label="系统参数" has-feedback>
              <a-radio-group v-model:value="form.sysFlag" placeholder="请选择系统参数" :disabled="handelType === 'edit' && form.sysFlag === 'Y'">
                <a-radio-button value="Y">是</a-radio-button>
                <a-radio-button value="N">否</a-radio-button>
              </a-radio-group>
              <span class="icon_open">
                <a-tooltip title="选择“系统参数”后，此配置无法删除" placement="right">
                  <question-circle-outlined style="color: #ef7b1a" />
                </a-tooltip>
              </span>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item name="groupCode" class="group-code" label="所属分类" has-feedback>
              <a-select v-model:value="form.groupCode" :tree-data="dropList" placeholder="请选择所属分类" tree-default-expand-all>
                <a-select-option v-for="item in dropList" :key="item.value" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
              <span class="icon_open">
                <a-tooltip placement="bottom" :auto-adjust-overflow="true" :overlay-style="{ maxWidth: '800px' }">
                  <template #title>
                    <div class="custom-tooltip">
                      <p>所属分类的数据源在【系统管理】>【字典管理】页面下的”开发配置中的分类“中维护</p>
                      <img src="@/assets/img/sysconfig/belongType.png" class="img" alt="" width="100%" />
                    </div>
                  </template>
                  <question-circle-outlined style="color: #ef7b1a" />
                </a-tooltip>
              </span>
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item name="sort" label="排序" has-feedback>
              <a-input-number v-model:value="form.sort" style="width: 100%" placeholder="请输入排序" :maxlength="10" :min="1" :max="1000" />
            </a-form-item>
          </a-col>
          <a-col :md="12" :sm="24">
            <a-form-item name="diceType" class="group-cs" label="参数值设置字典" has-feedback>
              <a-select v-model:value="form.diceType" :tree-data="dictTypeList" placeholder="请选择参数值所属字典" tree-default-expand-all @change="selDictType">
                <a-select-option v-for="item in dictTypeList" :key="item.code" :value="item.code">{{ item.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item name="value" label="参数值" has-feedback>
              <a-select :allowClear="true" v-model:value="form.value" v-if="form.diceType && form.diceType != ''" :tree-data="dictList" placeholder="请选择参数值" tree-default-expand-all>
                <a-select-option v-for="item in dictList" :key="item.code" :value="item.code">{{ item.value }}</a-select-option>
              </a-select>
              <a-input :allowClear="true" v-model:value="form.value" v-else style="width: 100%" placeholder="请输入参数值" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item name="remark" label="备注">
              <a-textarea v-model:value="form.remark" :rows="4" placeholder="请输入备注" :maxlength="300" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { chineseToChar, handelInputChar, codeCheckFunc } from '@/utils/util';
import { getDropdown, addSysConfig, editSysConfig, getSysDictList } from '@/api/develop/developConfig';
import { useSettingStore } from '@/store/setting';
import axios from 'axios';
const emit = defineEmits(['ok']);
const settingStore = useSettingStore();
const tenant = axios.defaults.headers.common['Tenant'];
// --------------------------------------- 初始化 ---------------------------------------
// 是编辑还是添加
const handelType = ref<'edit' | 'add' | ''>('');
const editDisabled = ref(false); // 是否能编辑
// 初始化
const init = (type: 'add' | 'edit' | '', record: any) => {
  visible.value = true;
  handelType.value = type;
  nextTick(() => {
    getDropList();
    getDictTypeData();
    formRef.value.resetFields();
    if (type === 'edit' && record) {
      form.id = record.id;
      form.name = record.name;
      form.code = record.code;
      form.sysFlag = record.sysFlag;
      form.groupCode = record.groupCode;
      form.value = record.value;
      form.remark = record.remark;
      form.diceType = record.diceType ? record.diceType : '';
      form.sort = record.sort ? record.sort : 100;
      if (record.sysFlag === 'Y') {
        editDisabled.value = true;
      }
    }
  });
};

const dropList = ref<Array<{ code: string; value: string }>>([]);

// 获取下拉列表
const getDropList = async () => {
  try {
    const res = await getDropdown({ code: 'system_constant' });
    if (res.code === 200) {
      dropList.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};
//参数字典类型
const dictTypeList = ref([]);
const getDictTypeData = () => {
  dictTypeList.value = [];
  getSysDictList()
    .then((res: any) => {
      if (res.code === 200) {
        dictTypeList.value = res.data;
        dictTypeList.value.unshift({ code: '', name: '无' });
        if (!form.diceType || form.diceType === '') {
          form.diceType = dictTypeList.value[0].code;
        } else {
          selDictType();
        }
      }
    })
    .catch(() => {
      dictTypeList.value = [{ code: '', name: '无' }];
      if (!form.diceType || form.diceType === '') {
        form.diceType = dictTypeList.value[0].code;
      }
    });
};
const selDictType = (select?: any, opt?: any, first: Boolean = true): any => {
  dictList.value = [];
  if (!first) {
    form.value = '';
  }

  if (form.diceType) {
    getDictList(form.diceType);
  }
};
//获取字典值下拉框
const dictList = ref([]);
const getDictList = (code: string) => {
  getDropdown({ code: code })
    .then((res: any) => {
      const { code, data, message } = res;
      if (code === 200) {
        if (data.length === 0) {
          form.diceType = dictTypeList.value[0].code;
          useGlobalMessage('warning', '该字典未设置字典值');
        } else {
          dictList.value = data;
        }
      } else {
        form.diceType = dictTypeList.value[0].code;
        useGlobalMessage('error', message);
      }
    })
    .catch((error: any) => {
      form.diceType = dictTypeList.value[0].code;
    });
};
// --------------------------------------- 初始化 end ---------------------------------------

defineExpose({ init });

// --------------------------------------- 表单 ---------------------------------------
type FormType = {
  id: string;
  name: string;
  code: string;
  sysFlag: string | null;
  groupCode: string | null;
  value: string;
  remark: string;
  diceType: string;
  sort: number;
};

const formRef = ref();
const form = reactive<FormType>({
  id: '', // 编辑时比传，添加不传
  name: '',
  code: '',
  sysFlag: null,
  groupCode: null,
  value: '',
  remark: '',
  diceType: '',
  sort: 100,
});

const rules: any = {
  name: [
    { required: true, message: '请输入参数名称！', trigger: 'blur' },
    { max: 30, message: '名字长度不能超过30', trigger: 'blur' },
  ],
  code: [{ required: true, validator: codeCheckFunc }],
  sysFlag: [{ required: true, message: '请选择系统参数！' }],
  groupCode: [{ required: true, message: '请选择所属分类' }],
  // value: [{ required: true, message: '请输入参数值！' }],
  sort: [{ required: true, message: '请输入序号' }],
};

// 编码拼音
const toPinyin = () => {
  form.name = handelInputChar(form.name); // 去除火星文和空格
  if (handelType.value === 'edit') return;
  const codeVal = chineseToChar(form.name);
  if (codeVal.charAt(codeVal.length - 1) === '_') {
    const res = codeVal.slice(0, codeVal.length - 1);
    form.code = res.length > 30 ? res.substring(0, 30) : res;
  } else {
    form.code = codeVal.length > 30 ? codeVal.substring(0, 30) : codeVal;
  }
};
// --------------------------------------- 表单 end ---------------------------------------

// --------------------------------------- 弹窗 ---------------------------------------
const visible = ref(false);
const confirmLoading = ref(false);

// 提交
const handleSubmit = () => {
  if (confirmLoading.value) return;
  confirmLoading.value = true;
  formRef.value
    .validate()
    .then(async () => {
      let submitApi;
      const params = {};
      if (handelType.value === 'add') {
        submitApi = addSysConfig;
        Object.assign(params, {
          name: form.name,
          code: form.code,
          sysFlag: form.sysFlag,
          groupCode: form.groupCode,
          value: form.value,
          remark: form.remark,
          diceType: form.diceType,
          sort: form.sort,
        });
      } else {
        submitApi = editSysConfig;
        Object.assign(params, { ...form });
      }
      try {
        const res = await submitApi(params);
        if (res.code === 200) {
          useGlobalMessage('success', `${handelType.value === 'add' ? '开发配置添加' : '开发配置编辑'}成功！`);
          // 更新系统标题和logo
          if (form.code === `${tenant}_XI_TONG_BIAO_TI`) {
            sessionStorage.setItem(`${tenant}_XI_TONG_BIAO_TI`, form.value);
            // 项目名称
            const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
            if (projectName.value) {
              document.title = `${form.value} - ${projectName.value}`;
            } else {
              document.title = `${form.value}`;
            }
            settingStore.setUpdateSetting(1);
          }
          if (form.code === 'DENG_LU_YE_LOGO') {
            sessionStorage.setItem(`${tenant}_XI_TONG_LOGO`, form.value);
            // 修改link-icon
            let favicon = document.querySelector('link[rel="icon"]');
            if (favicon) {
              // @ts-ignore
              favicon.href = form.value;
            }
            settingStore.setUpdateSetting(1);
          }
          formRef.value.resetFields();
          visible.value = false;

          // 编辑或添加成功
          emit('ok');
        }
      } catch (error) {
        confirmLoading.value = false;
        console.error(error);
      } finally {
        confirmLoading.value = false;
      }
    })
    .catch((error: any) => {
      confirmLoading.value = false;
    });
};

// 取消
const handleCancel = () => {
  if (confirmLoading.value) return;
  formRef.value.resetFields();
  visible.value = false;
  editDisabled.value = false;
  confirmLoading.value = false;
};
// --------------------------------------- 弹窗 end ---------------------------------------
</script>
<style lang="scss" scoped>
// 设置form表单的label宽度
:deep(.ant-form-item-label) {
  width: 110px;
  min-width: 110px;
  max-width: 110px;
}

.group-code {
  .icon_open {
    position: absolute;
    top: 50%;
    transform: translateY(-50%) translateX(3px);
  }
}

.sys-flag {
  ::v-deep .ant-form-item-control-input-content {
    display: flex;
    align-items: center;
  }

  .icon_open {
    margin-left: 10px;

    span {
      margin-right: 20px;
    }
  }
}

.custom-tooltip {
  // width: 500px;

  .img {
    width: 100%;
  }
}
</style>
