package com.uino.x.common.sql.maker;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.uino.x.common.sql.metadata.*;
import com.uino.x.common.tool.base.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽象数据库表SQL制造者
 * <p>
 * 提供数据库表相关SQL操作的通用功能实现，包括：
 * <ul>
 *   <li>表结构的CREATE和DROP语句生成</li>
 *   <li>表数据的INSERT语句批量生成</li>
 *   <li>跨数据库类型的表结构转换</li>
 *   <li>基于元数据的表SQL构建</li>
 *   <li>分页数据查询和导出</li>
 * </ul>
 * </p>
 *
 * <p><strong>核心特性：</strong>
 * <ul>
 *   <li>支持基于JDBC元数据的表结构提取</li>
 *   <li>支持跨数据库类型转换（MySQL ↔ 达梦）</li>
 *   <li>提供高效的分页数据导出机制</li>
 *   <li>支持数据类型的自动映射和转换</li>
 *   <li>提供灵活的SQL忽略和过滤机制</li>
 * </ul>
 * </p>
 *
 * <p><strong>使用场景：</strong>
 * <ul>
 *   <li>数据库表结构迁移</li>
 *   <li>跨数据库数据同步</li>
 *   <li>表数据的批量导出导入</li>
 *   <li>数据库结构对比和同步</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 0.0.20
 * @date 2022/6/30 10:47
 */
@Slf4j
public abstract class AbstractTableSql extends AbstractSqlMaker implements TableSql {

    // ==================== SQL模板常量 ====================

    /**
     * INSERT语句模板
     * <p>格式：INSERT INTO {表名} VALUES({值列表});</p>
     */
    protected static final String INSERT_SQL_TEMPLATE = "insert into %s values(%s);";

    /**
     * 带列名的INSERT语句模板
     * <p>格式：INSERT INTO {表名} ({列名列表}) VALUES({值列表});</p>
     * <p>用于有自增列的表，必须指定列名才能使用IDENTITY_INSERT</p>
     */
    private static final String INSERT_WITH_COLUMNS_SQL_TEMPLATE = "insert into %s (%s) values(%s);";

    /**
     * 查询所有数据的SQL模板
     * <p>格式：SELECT * FROM {表名}</p>
     */
    protected static final String SELECT_ALL_SQL_TEMPLATE = "select * from %s";

    /**
     * 表元数据提取失败时的错误消息模板
     */
    protected static final String METADATA_EXTRACTION_ERROR = "无法提取表元数据: %s";

    /**
     * 跨数据库转换时连接和元数据都为空的错误消息
     */
    protected static final String NO_CONNECTION_AND_METADATA_ERROR = "无法获取表元数据，连接和元数据都为空";

    // ==================== 列信息存储 ====================
    /**
     * NULL值在SQL中的表示
     */
    private static final String SQL_NULL = "NULL";
    /**
     * SQL字符串值的引号字符
     */
    private static final String SQL_STRING_QUOTE = "'";
    /**
     * 转义字符
     */
    private static final String ESCAPE_CHAR = "\\";
    /**
     * JSON类型名称常量
     */
    private static final String JSON_TYPE_NAME = "JSON";

    // ==================== 状态管理 ====================
    /**
     * CLOB类型名称常量
     */
    private static final String CLOB_TYPE_NAME = "CLOB";

    // ==================== 元数据管理 ====================
    /**
     * 表的列名列表
     * <p>按照在表中的顺序存储，用于生成INSERT语句时的字段顺序</p>
     */
    protected final List<String> columnNameList = new ArrayList<>();
    /**
     * 列的JDBC类型列表
     * <p>与columnNameList对应，存储每列的JDBC数据类型</p>
     */
    protected final List<JDBCType> columnTypeList = new ArrayList<>();

    // ==================== 构造方法 ====================
    /**
     * 列的JDBC类型名称列表
     * <p>与columnNameList对应，存储每列的数据库特定类型名称</p>
     */
    protected final List<String> columnTypeNameList = new ArrayList<>();
    /**
     * 列的Java类型列表
     * <p>与columnNameList对应，存储每列对应的Java类型</p>
     */
    protected final List<Class<?>> columnTypeClassList = new ArrayList<>();

    // ==================== TableSql接口实现 ====================
    /**
     * 表元数据信息
     * <p>
     * 存储从JDBC获取的表结构化元数据信息，包括：
     * <ul>
     *   <li>表名和注释</li>
     *   <li>列定义（名称、类型、约束等）</li>
     *   <li>主键信息</li>
     *   <li>索引信息</li>
     * </ul>
     * 用于跨数据库转换时的SQL构建。
     * </p>
     */
    protected final TableMetadata tableMetadata;
    /**
     * 源数据TableSql实例（用于跨数据库转换）
     * <p>
     * 在跨数据库转换场景中，目标TableSql实例不直接连接数据库，
     * 而是委托给源TableSql来获取数据，然后进行格式转换。
     * 这种设计模式支持：
     * <ul>
     *   <li>MySQL → 达梦的数据转换</li>
     *   <li>达梦 → MySQL的数据转换</li>
     *   <li>保持数据一致性的同时进行格式适配</li>
     * </ul>
     * </p>
     */
    protected final TableSql source;
    /**
     * 已处理的INSERT语句数量
     * <p>用于分页查询时的偏移量计算，避免重复处理相同数据</p>
     */
    protected int insertCount = 0;

    /**
     * 主构造方法
     * <p>
     * 支持多种初始化方式：
     * <ul>
     *   <li>基于数据库连接自动提取元数据</li>
     *   <li>基于预提供的表元数据（跨数据库转换）</li>
     *   <li>委托模式（通过source获取数据）</li>
     * </ul>
     * </p>
     *
     * @param parent        父制造者，不能为null
     * @param connection    数据库连接，可以为null（跨数据库转换时）
     * @param name          表名称，不能为null
     * @param tableMetadata 表元数据，为null时将自动从连接中提取
     * @param source        源TableSql实例，用于跨数据库转换时的数据委托
     * @throws SQLException 当无法获取表元数据或初始化失败时
     * <AUTHOR>
     * @date 2025/1/30 重构优化
     */
    protected AbstractTableSql(SqlMaker parent, Connection connection, String name,
                               TableMetadata tableMetadata, TableSql source) throws SQLException {
        super(parent, connection, name);
        this.source = source;

        log.debug("创建AbstractTableSql: table={}, hasConnection={}, hasMetadata={}, hasSource={}, sourceType={}",
                name, connection != null, tableMetadata != null, source != null,
                source != null ? source.getClass().getSimpleName() : "null");

        // 元数据获取策略：优先使用传入的元数据，其次从连接提取
        if (tableMetadata != null) {
            this.tableMetadata = tableMetadata;
            log.debug("使用预提供的表元数据: table={}, columnCount={}", name,
                    tableMetadata.getColumns() != null ? tableMetadata.getColumns().size() : 0);
        } else if (connection != null) {
            // 从数据库连接提取表元数据
            String databaseName = parent.getOriginalName();
            this.tableMetadata = MetadataExtractor.extractSingleTable(
                    connection.getMetaData(), databaseName, name);

            if (this.tableMetadata == null) {
                throw new SQLException(String.format(METADATA_EXTRACTION_ERROR, name));
            }
            log.debug("从数据库连接提取表元数据: table={}, columnCount={}", name,
                    this.tableMetadata.getColumns() != null ? this.tableMetadata.getColumns().size() : 0);
        } else {
            throw new SQLException(NO_CONNECTION_AND_METADATA_ERROR);
        }

        // 初始化列信息
        initColumnInfo();
    }

    /**
     * 简化构造方法（基于数据库连接自动提取元数据）
     * <p>
     * 适用于直接从数据库连接创建TableSql的场景，
     * 会自动提取表元数据并初始化列信息。
     * </p>
     *
     * @param parent     父制造者，不能为null
     * @param connection 数据库连接，不能为null
     * @param name       表名称，不能为null
     * @throws SQLException 当无法获取表元数据或初始化失败时
     * <AUTHOR>
     * @date 2022/7/6 14:30
     */
    protected AbstractTableSql(SqlMaker parent, Connection connection, String name) throws SQLException {
        this(parent, connection, name, null, null);
    }

    // ==================== 数据处理方法 ====================

    @Override
    public String getName() {
        final String name = super.getName();
        if (StringUtils.isNotBlank(this.output.getName())) {
            final String parentName = this.parent.output().getName();
            if (StringUtils.isNotBlank(parentName)) {
                return wrapIdentifier(parentName) + "." + name;
            }
        }
        return name;
    }


    // ==================== 数据类型处理常量 ====================

    /**
     * 获取INSERT语句列表（无限制版本）
     * <p>
     * 获取表中所有数据的INSERT语句。这是一个便捷方法，
     * 内部调用带count参数的版本，count设置为-1表示获取所有数据。
     * </p>
     *
     * @param ignoreSql 需要忽略的SQL模式数组
     * @return INSERT语句列表
     * @throws SQLException 查询过程中发生的异常
     */
    @Override
    public List<String> getInsertList(String... ignoreSql) throws SQLException {
        return getInsertList(-1, ignoreSql);
    }

    /**
     * 获取INSERT语句列表（分页版本）
     * <p>
     * 支持两种查询模式：
     * <ul>
     *   <li>分页查询：count > 0时，返回指定数量的INSERT语句</li>
     *   <li>一次性查询：count = -1时，返回所有剩余数据的INSERT语句</li>
     * </ul>
     * <p>
     * 对于跨数据库转换场景，会委托给源TableSql获取数据并进行格式转换。
     * </p>
     *
     * @param count     要获取的INSERT语句数量，-1表示获取所有剩余数据
     * @param ignoreSql 需要忽略的SQL模式数组
     * @return INSERT语句列表，如果没有更多数据则返回空列表
     * @throws SQLException 查询过程中发生的异常
     */
    @Override
    public List<String> getInsertList(int count, String... ignoreSql) throws SQLException {

        // 跨数据库转换场景：委托给源TableSql获取数据
        if (this.connection == null && this.source != null) {
            return getInsertListFromSource(count, ignoreSql);
        }

        // 一次性查询优化：避免重复处理相同数据
        if (count == -1 && this.insertCount > 0) {
            log.debug("一次性查询已完成，返回空列表: table={}, insertCount={}",
                    getOriginalName(), this.insertCount);
            return new ArrayList<>();
        }

        // 从数据库查询数据并生成INSERT语句
        log.debug("开始查询表数据: table={}, count={}, offset={}",
                getOriginalName(), count, this.insertCount);

        return getInsertListFromDatabase(getName(), count, getQuote(), ignoreSql);
    }

    private List<String> getInsertListFromDatabase(String name, int count, String quote, String[] ignoreSql) throws SQLException {
        final List<String> insertList = new ArrayList<>();
        try (final PreparedStatement ps = prepareGetInsert(count); final ResultSet rs = ps.executeQuery()) {
            int processedRows = 0;

            // 获取实际的列数
            final int expectedColumnCount = this.columnNameList.size();

            validateColumn(rs, expectedColumnCount);

            // 逐行处理查询结果
            while (count == -1 || processedRows < count) {
                if (rs.next()) {
                    // 构建单行的INSERT语句（使用预期列数）
                    String rowValues = getRowValues(expectedColumnCount, rs);
                    String insertSql = buildInsertSql(name, rowValues, quote);

                    // 检查是否需要忽略此INSERT语句
                    if (StringUtils.fuzzyMatch(insertSql, ignoreSql)) {
                        log.debug("INSERT语句被忽略: {}", insertSql);
                        return Collections.emptyList();
                    }

                    // 确保INSERT语句以分隔符结尾
                    if (!insertSql.trim().endsWith(getDelimiter())) {
                        insertSql += getDelimiter();
                    }

                    insertList.add(insertSql);
                    processedRows++;
                } else {
                    // 没有更多数据
                    break;
                }
            }

            // 更新已处理的记录数
            this.insertCount += processedRows;

            log.debug("表数据查询完成: table={}, processedRows={}, totalInsertCount={}",
                    getOriginalName(), processedRows, this.insertCount);

            // 记录生成的INSERT语句（调试用）
            if (log.isDebugEnabled()) {
                insertList.forEach(insert ->
                        log.debug("生成INSERT语句 [{}]: {}", getOriginalName(), insert));
            }

            return insertList;
        }
    }

    private void validateColumn(ResultSet rs, int expectedColumnCount) throws SQLException {
        // 严格验证列数一致性，不匹配时直接抛出异常
        final int actualColumnCount = rs.getMetaData().getColumnCount();
        if (actualColumnCount != expectedColumnCount) {
            throw new SQLException(String.format(
                    "列数不匹配错误: table=%s, 预期列数=%d, 实际列数=%d. " +
                            "请检查表结构是否一致或重新初始化列信息。",
                    getOriginalName(), expectedColumnCount, actualColumnCount));
        }
    }

    /**
     * 基于源TableSql委托获取INSERT语句列表（跨数据库转换专用）
     * <p>
     * 在跨数据库转换场景中，目标TableSql实例通过此方法委托给源TableSql
     * 获取数据，然后进行格式转换以适配目标数据库。
     * </p>
     *
     * <p><strong>转换流程：</strong>
     * <ol>
     *   <li>委托源TableSql获取原始INSERT语句</li>
     *   <li>解析并转换INSERT语句格式</li>
     *   <li>应用目标数据库的语法规则</li>
     *   <li>返回转换后的INSERT语句列表</li>
     * </ol>
     * </p>
     *
     * @param count     要获取的INSERT语句数量，-1表示获取所有数据
     * @param ignoreSql 需要忽略的SQL模式数组
     * @return 转换后的INSERT语句列表
     * @throws SQLException 委托或转换过程中发生的异常
     */
    private List<String> getInsertListFromSource(int count, String... ignoreSql) throws SQLException {

        // 验证源TableSql的有效性
        if (this.source == null) {
            log.warn("源TableSql为空，无法进行跨数据库转换: table={}", getOriginalName());
            return new ArrayList<>();
        }

        log.debug("开始跨数据库转换: source={}, target={}, count={}",
                this.source.getOriginalName(), getOriginalName(), count);

        // 委托源TableSql获取原始INSERT语句
        List<String> sourceInsertList = ((AbstractTableSql) this.source).getInsertListFromDatabase(getName(), count, getQuote(), ignoreSql);

        // 更新已处理的记录数
        this.insertCount += sourceInsertList.size();

        log.debug("跨数据库转换完成: source={}, target={}, convertedCount={}",
                this.source.getOriginalName(), getOriginalName(), sourceInsertList.size());

        // 记录转换后的INSERT语句（调试用）
        if (log.isDebugEnabled()) {
            sourceInsertList.forEach(insert ->
                    log.debug("跨数据库转换INSERT [{}]: {}", getOriginalName(), insert));
        }

        return sourceInsertList;
    }

    /**
     * 检查表是否包含自增列
     *
     * @return true如果表包含自增列，false否则
     */
    protected boolean hasAutoIncrementColumn() {
        if (this.tableMetadata == null || this.tableMetadata.getColumns() == null) {
            return false;
        }

        return this.tableMetadata.getColumns().stream()
                .anyMatch(column -> Boolean.TRUE.equals(column.getAutoIncrement()));
    }

    /**
     * 生成列名列表字符串
     * <p>
     * 将表的所有列名拼接成用逗号分隔的字符串，用于INSERT语句的列名部分。
     * 格式："column1", "column2", "column3"
     * </p>
     *
     * @param quote 引号
     * @return 列名列表字符串，如果没有列则返回空字符串
     */
    private String buildColumnNamesString(String quote) {
        if (this.columnNameList.isEmpty()) {
            log.warn("列名列表为空，无法生成列名字符串: table={}", getOriginalName());
            return "";
        }

        return this.columnNameList.stream()
                .map(columnName -> quote + columnName + quote)
                .collect(Collectors.joining(", "));
    }

    /**
     * 构建INSERT语句
     * <p>
     * 根据表名和行值构建INSERT语句。子类可以重写此方法来实现特定的INSERT语句格式，
     * 例如为有自增列的表添加列名列表。
     * </p>
     *
     * @param tableName 表名
     * @param rowValues 行值字符串，格式化后的VALUES部分
     * @param quote     引号
     * @return 完整的INSERT语句
     */
    protected String buildInsertSql(String tableName, String rowValues, String quote) {
        // 检查是否需要使用带列名的INSERT模板
        if (hasAutoIncrementColumn()) {
            String columnNamesString = buildColumnNamesString(quote);
            log.debug("表包含自增列，使用带列名的INSERT语句: table={}, columns={}",
                    getOriginalName(), columnNamesString);
            return String.format(INSERT_WITH_COLUMNS_SQL_TEMPLATE, tableName, columnNamesString, rowValues);
        } else {
            // 使用标准的INSERT
            return String.format(INSERT_SQL_TEMPLATE, tableName, rowValues);
        }
    }

    /**
     * 获取数据行的所有列值并格式化为INSERT语句的VALUES部分
     * <p>
     * 遍历结果集的当前行，提取所有列的值并按照SQL INSERT语句的格式
     * 进行拼接。处理包括：
     * <ul>
     *   <li>NULL值的正确表示</li>
     *   <li>字符串值的引号处理</li>
     *   <li>特殊字符的转义</li>
     *   <li>不同数据类型的格式化</li>
     * </ul>
     * </p>
     *
     * @param columnCount 列数量，必须与表的实际列数匹配
     * @param rs          JDBC结果集，当前指向要处理的数据行
     * @return 格式化后的VALUES部分字符串，如：'value1','value2',123
     * @throws SQLException 读取结果集时发生的异常
     * <AUTHOR>
     * @date 2022/6/30 10:47
     */
    protected String getRowValues(int columnCount, ResultSet rs) throws SQLException {
        if (columnCount <= 0) {
            log.warn("列数量无效: {}", columnCount);
            return "";
        }

        StringBuilder rowValues = new StringBuilder();

        // 遍历所有列并获取值
        for (int columnIndex = 1; columnIndex <= columnCount; columnIndex++) {
            // 获取当前列的格式化值
            String columnValue = getValue(columnIndex, rs);

            // 拼接列值（第一列不需要逗号前缀）
            if (columnIndex > 1) {
                rowValues.append(",");
            }
            rowValues.append(columnValue);
        }

        String result = rowValues.toString();
        if (log.isDebugEnabled()) {
            log.debug("行数据VALUES: {}", result);
        }
        return result;
    }

    /**
     * 获取指定列的格式化值
     * <p>
     * 根据列的JDBC类型和数据库特定类型，将结果集中的值转换为
     * 适合INSERT语句的格式。处理包括：
     * <ul>
     *   <li>NULL值转换为SQL的NULL</li>
     *   <li>字符串类型添加引号并转义特殊字符</li>
     *   <li>JSON/CLOB类型的特殊处理</li>
     *   <li>数值类型的直接转换</li>
     *   <li>日期时间类型的字符串格式化</li>
     * </ul>
     * </p>
     *
     * @param columnIndex 列索引（从1开始）
     * @param rs          JDBC结果集
     * @return 格式化后的列值，NULL值返回"NULL"，字符串值带引号
     * @throws SQLException 读取结果集时发生的异常
     */
    private String getValue(int columnIndex, ResultSet rs) throws SQLException {
        // 严格获取列的类型信息（不允许索引越界）
        final int listIndex = columnIndex - 1;
        final JDBCType jdbcType = columnTypeList.get(listIndex);
        final Class<?> typeClass = columnTypeClassList.get(listIndex);
        final String typeName = columnTypeNameList.get(listIndex);

        return switch (jdbcType) {
            // 需要引号的字符串和日期时间类型
            case CHAR, VARCHAR, LONGVARCHAR, CLOB, DATE, TIME, TIME_WITH_TIMEZONE, TIMESTAMP, TIMESTAMP_WITH_TIMEZONE ->
                    formatStringValue(rs.getString(columnIndex), typeName);
            default ->
                // 数值类型和其他类型
                    formatNonStringValue(rs, columnIndex, typeClass);
        };
    }

    /**
     * 格式化字符串类型的值
     * <p>
     * 处理字符串、CLOB、JSON和日期时间类型的值格式化，
     * 包括NULL值处理、引号添加和特殊字符转义。
     * </p>
     *
     * @param value    原始字符串值
     * @param typeName 数据库类型名称
     * @return 格式化后的字符串值
     */
    private String formatStringValue(String value, String typeName) {
        // NULL值处理
        if (Objects.isNull(value)) {
            return SQL_NULL;
        }

        // 简化逻辑：只处理真正需要转义的字符
        final StringBuilder builder = new StringBuilder();

        for (int i = 0; i < value.length(); i++) {
            char currentChar = value.charAt(i);

            // 只转义单引号（SQL字符串分隔符）
            if (currentChar == '\'') {
                builder.append(SQL_STRING_QUOTE);
            }

            builder.append(currentChar);
        }

        // 添加SQL字符串引号
        return SQL_STRING_QUOTE + builder + SQL_STRING_QUOTE;
    }

    /**
     * 格式化非字符串类型的值
     * <p>
     * 处理数值、布尔值等不需要引号的数据类型。
     * </p>
     *
     * @param rs          结果集
     * @param columnIndex 列索引
     * @param typeClass   Java类型
     * @return 格式化后的值
     * @throws SQLException 读取结果集时发生的异常
     */
    private String formatNonStringValue(ResultSet rs, int columnIndex, Class<?> typeClass) throws SQLException {
        // 获取原始对象值
        final Object rawValue = rs.getObject(columnIndex);
        if (Objects.isNull(rawValue)) {
            return SQL_NULL;
        }

        // 使用指定类型获取值（提高类型安全性）
        final Object typedValue = rs.getObject(columnIndex, typeClass);
        if (typedValue instanceof Boolean) {
            return ((Boolean) typedValue) ? "1" : "0";
        }
        return String.valueOf(typedValue);
    }


    // ==================== 初始化方法 ====================

    /**
     * 初始化表的列信息
     * <p>
     * 通过JDBC元数据直接获取表的列信息，包括列名、数据类型、Java类型等。
     * 这些信息用于后续的INSERT语句生成和数据类型转换。
     * </p>
     *
     * <p><strong>提取的信息包括：</strong>
     * <ul>
     *   <li>列名列表</li>
     *   <li>JDBC数据类型列表</li>
     *   <li>数据库特定类型名称列表</li>
     *   <li>对应的Java类型列表</li>
     * </ul>
     * </p>
     *
     * <p><strong>改进说明：</strong>
     * 使用DatabaseMetaData.getColumns()直接获取列元数据，
     * 避免执行实际的查询语句，提高性能和可靠性。
     * </p>
     *
     * <AUTHOR>
     * @date 2022/6/30 10:47
     * @date 2025/1/30 改为使用JDBC元数据获取
     */
    protected void initColumnInfo() {
        log.debug("开始初始化列信息: table={}, 当前列数={}, hasMetadata={}",
                getOriginalName(), columnNameList.size(),
                this.tableMetadata != null && this.tableMetadata.getColumns() != null);

        // 防止重复初始化
        if (!this.columnNameList.isEmpty()) {
            log.warn("列信息已存在，可能重复初始化: table={}, currentColumnCount={}",
                    getOriginalName(), this.columnNameList.size());
            return;
        }

        // 统一使用表元数据初始化列信息
        log.debug("使用表元数据初始化列信息: {}", getOriginalName());
        initColumnInfoFromMetadata();
    }

    /**
     * 从表元数据初始化列信息（跨数据库转换专用）
     * <p>
     * 当没有数据库连接时，从预提供的表元数据中提取列信息。
     * 这主要用于跨数据库转换场景，确保目标实例能够正确处理INSERT语句。
     * </p>
     *
     * <AUTHOR>
     * @date 2025/1/30 新增
     */
    private void initColumnInfoFromMetadata() {
        if (this.tableMetadata == null || this.tableMetadata.getColumns() == null) {
            log.warn("表元数据为空，无法初始化列信息: {}", getOriginalName());
            return;
        }

        log.debug("开始从表元数据初始化列信息: table={}, columnCount={}",
                getOriginalName(), this.tableMetadata.getColumns().size());

        // 从表元数据中提取列信息
        for (ColumnMetadata column : this.tableMetadata.getColumns()) {
            final String columnName = column.getColumnName();
            final String typeName = column.getTypeName();

            // 尝试解析JDBC类型
            JDBCType jdbcType;
            try {
                jdbcType = JDBCType.valueOf(column.getJdbcType());
            } catch (Exception e) {
                // 如果无法解析，使用VARCHAR作为默认类型
                jdbcType = JDBCType.VARCHAR;
            }

            // 根据JDBC类型推断Java类型
            final Class<?> javaClass = getJavaClassForJdbcType(jdbcType, typeName);

            // 添加到列表中
            columnNameList.add(columnName);
            columnTypeList.add(jdbcType);
            columnTypeNameList.add(typeName);
            columnTypeClassList.add(javaClass);
        }

        log.debug("从表元数据初始化列信息完成: table={}, columnCount={}",
                getOriginalName(), columnNameList.size());
    }


    // ==================== 抽象方法：子类必须实现 ====================

    /**
     * 准备用于获取INSERT数据的预编译语句
     * <p>
     * 子类需要实现此方法来提供数据查询语句，支持分页查询。
     * 不同数据库的分页语法不同，需要在子类中实现具体的分页逻辑。
     * </p>
     *
     * @param count 要查询的记录数量，-1表示查询所有剩余数据
     * @return 预编译的查询语句
     * @throws SQLException 创建预编译语句时发生的异常
     * <AUTHOR>
     */
    protected abstract PreparedStatement prepareGetInsert(int count) throws SQLException;

    // ==================== 模板方法：SQL构建相关 ====================

    /**
     * 构建数据类型定义（模板方法）
     * <p>
     * 子类需要实现具体数据库的数据类型映射和格式化。
     * 包括类型名称、长度、精度等的处理。
     * </p>
     *
     * @param columnMetadata 列元数据信息
     * @return 数据类型定义字符串
     */
    protected abstract String buildDataTypeDefinition(ColumnMetadata columnMetadata);

    // ==================== 工具方法 ====================

    /**
     * 根据JDBC类型推断对应的Java类型
     * <p>
     * 将JDBC数据类型映射到对应的Java类型，用于类型安全的数据处理。
     * </p>
     *
     * @param jdbcType JDBC数据类型
     * @param typeName 数据库特定的类型名称
     * @return 对应的Java类型
     */
    protected Class<?> getJavaClassForJdbcType(JDBCType jdbcType, String typeName) {
        return switch (jdbcType) {
            case CHAR, VARCHAR, LONGVARCHAR, CLOB -> String.class;
            case NUMERIC, DECIMAL -> java.math.BigDecimal.class;
            case BIT, BOOLEAN -> Boolean.class;
            case TINYINT -> Byte.class;
            case SMALLINT -> Short.class;
            case INTEGER -> Integer.class;
            case BIGINT -> Long.class;
            case REAL -> Float.class;
            case FLOAT, DOUBLE -> Double.class;
            case BINARY, VARBINARY, LONGVARBINARY, BLOB -> byte[].class;
            case DATE -> java.sql.Date.class;
            case TIME -> Time.class;
            case TIMESTAMP -> Timestamp.class;
            case TIME_WITH_TIMEZONE -> java.time.OffsetTime.class;
            case TIMESTAMP_WITH_TIMEZONE -> java.time.OffsetDateTime.class;
            case ARRAY -> Array.class;
            case REF -> Ref.class;
            case STRUCT -> Struct.class;
            case JAVA_OBJECT -> Object.class;
            default -> {
                log.debug("未知JDBC类型，使用Object: jdbcType={}, typeName={}", jdbcType, typeName);
                yield Object.class;
            }
        };
    }

    // ==================== 默认值处理通用方法 ====================

    /**
     * 检查默认值是否已经被引号包围
     *
     * @param defaultValue 默认值
     * @return true表示已经被引号包围，false表示需要添加引号
     */
    protected boolean isAlreadyQuoted(String defaultValue) {
        if (defaultValue == null || defaultValue.length() < 2) {
            return false;
        }
        return (defaultValue.startsWith("'") && defaultValue.endsWith("'")) ||
                (defaultValue.startsWith("\"") && defaultValue.endsWith("\""));
    }

    /**
     * 移除已存在的引号
     *
     * @param value 可能包含引号的值
     * @return 移除引号后的值
     */
    protected String removeExistingQuotes(String value) {
        if (value == null) {
            return null;
        }
        if (isAlreadyQuoted(value)) {
            return value.substring(1, value.length() - 1);
        }
        return value;
    }

    /**
     * 检查列是否为BIT类型
     *
     * @param column 列元数据
     * @return true表示是BIT类型，false表示不是
     */
    protected boolean isBitColumn(ColumnMetadata column) {
        if (column.getDataType() == null) {
            return false;
        }
        String dataType = column.getDataType().toUpperCase();
        return "BIT".equals(dataType) || dataType.startsWith("BIT(");
    }

    /**
     * 检查列是否为TINYINT类型
     *
     * @param column 列元数据
     * @return true表示是TINYINT类型，false表示不是
     */
    protected boolean isTinyIntColumn(ColumnMetadata column) {
        if (column.getDataType() == null) {
            return false;
        }
        String dataType = column.getDataType().toUpperCase();
        return "TINYINT".equals(dataType) || dataType.startsWith("TINYINT(");
    }

    /**
     * 检查列是否为数值类型
     *
     * @param column 列元数据
     * @return true表示是数值类型，false表示不是
     */
    protected boolean isNumericColumn(ColumnMetadata column) {
        if (column.getDataType() == null) {
            return false;
        }
        String dataType = column.getDataType().toUpperCase();
        return dataType.contains("INT") ||
                dataType.contains("DECIMAL") ||
                dataType.contains("NUMERIC") ||
                dataType.contains("FLOAT") ||
                dataType.contains("DOUBLE") ||
                dataType.contains("BIGINT") ||
                dataType.contains("SMALLINT") ||
                dataType.contains("TINYINT") ||
                dataType.contains("NUMBER");
    }

    /**
     * 检查索引列是否与主键列完全匹配
     *
     * @param index      索引元数据
     * @param primaryKey 主键元数据
     * @return true如果索引列与主键列完全匹配
     */
    protected boolean isIndexMatchesPrimaryKey(IndexMetadata index, PrimaryKeyMetadata primaryKey) {
        if (index.getColumns() == null || primaryKey.getColumnNames() == null) {
            return false;
        }

        // 检查列数是否相同
        if (index.getColumns().size() != primaryKey.getColumnNames().size()) {
            return false;
        }

        // 检查列名是否完全匹配（忽略顺序）
        Set<String> indexColumns = index.getColumns().stream()
                .map(IndexMetadata.IndexColumnMetadata::getColumnName)
                .collect(java.util.stream.Collectors.toSet());

        Set<String> primaryKeyColumns = new HashSet<>(primaryKey.getColumnNames());

        boolean matches = indexColumns.equals(primaryKeyColumns);

        if (matches) {
            log.debug("索引列与主键列匹配: indexName={}, columns={}",
                    index.getIndexName(), primaryKey.getColumnNames());
        }

        return matches;
    }

}
