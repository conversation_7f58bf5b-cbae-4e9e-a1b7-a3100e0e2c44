<!--
 * @Description: 设置三维效果包
 * @Version: 1.0
 * @Autor: lcm
 * @Date: 2023-05-11 10:17:48
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-26 17:27:24
-->
<template>
  <div class="scen-child-panel" v-show="visible">
    <div class="head">
      <div>子场景位置矫正</div>
    </div>
    <div class="content">
      <a-form ref="formRef" :model="form" label-align="left" class="twin-prop-form keep-px">
        <a-form-item label="旋转角度" class="special-form-item">
          <div class="special-input-wrap">
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.angles[0]"
                :min="-180"
                :max="180"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'anglesx')"
              />
              <span class="input-notice">X</span>
            </div>
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.angles[1]"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'anglesy')"
              />
              <span class="input-notice">Y</span>
            </div>
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.angles[2]"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'anglesz')"
              />
              <span class="input-notice">Z</span>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="位置" class="special-form-item">
          <div class="special-input-wrap">
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.position[0]"
                :min="-180"
                :max="180"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'posx')"
              />
              <span class="input-notice">X</span>
            </div>
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.position[1]"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'posy')"
              />
              <span class="input-notice">Y</span>
            </div>
            <div class="special-input-item">
              <a-input-number
                v-model:value="form.position[2]"
                :formatter="numberFormat"
                :parser="numberParse"
                step="0.1"
                class="special-input"
                @change="(value: number | string) => changeFn(value, 'posz')"
              />
              <span class="input-notice">Z</span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <div class="btns">
      <a-button type="primary" class="btn" @click="save">保存</a-button>
      <a-button class="btn" @click="cancel">取消</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { saveCameraConfig } from '@/api/business/scene';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
const emit = defineEmits(['back']);
const visible = ref(false);
const formRef = ref();
const form = reactive({
  position: [0, 0, 0],
  angles: [0, 0, 0],
});
const init = () => {
  visible.value = true;
  const translatePosition = window.app.level.current?.userData?.translatePosition;
  if (translatePosition) {
    form.position = JSON.parse(translatePosition)?.position;
    form.angles = JSON.parse(translatePosition)?.angles;
  } else {
    form.position = window.app.level.current.position;
    form.angles = window.app.level.current.angles;
  }
  window.scencePosCorrect?.init({
    updateCb: (resData?: any) => {
      if (resData) {
        if (resData?.type === 'angles') {
          form.angles = resData?.data;
        } else {
          form.position = resData?.data;
        }
      }
    },
  });
};
const changeFn = (val: number | string, type: string) => {
  if (!window.scencePosCorrect) return;
  const angles = window.scencePosCorrect.curChildScence.angles;
  const position = window.scencePosCorrect.curChildScence.position;
  switch (type) {
    case 'anglesx':
      window.scencePosCorrect.curChildScence.angles = [val, angles[1], angles[2]];
      break;
    case 'anglesy':
      window.scencePosCorrect.curChildScence.angles = [angles[0], val, angles[2]];
      break;
    case 'anglesz':
      window.scencePosCorrect.curChildScence.angles = [angles[0], angles[1], val];
      break;
    case 'posx':
      window.scencePosCorrect.curChildScence.position = [val, position[1], position[2]];
      break;
    case 'posy':
      window.scencePosCorrect.curChildScence.position = [position[0], val, position[2]];
      break;
    case 'posz':
      window.scencePosCorrect.curChildScence.position = [position[0], position[1], val];
      break;
    default:
      break;
  }
};
// 格式化处理
const numberFormat = (value: any) => `${Number(value)}`.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
const numberParse = (value: any) => value.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
const cancel = () => {
  if (window.app && window.app.level && window.app.level.current) {
    if (window.scencePosCorrect.curBuilid?.position) {
      window.app.level.current.position = window.scencePosCorrect.curBuilid?.position;
    }
    if (window.scencePosCorrect.curBuilid?.angles) {
      window.app.level.current.angles = window.scencePosCorrect.curBuilid?.angles;
    }
    window.scencePosCorrect.reset();
    visible.value = false;
    emit('back');
  }
};
onMounted(() => {});
const save = () => {
  const userData = window.app.level.current?.userData;
  //先保存接口再更新本地
  const param = {
    position: JSON.stringify({
      angles: form.angles,
      position: form.position,
    }),
    mainUuid: userData?.parentSceneUUID,
    childUuid: userData?.uuid,
  };
  saveCameraConfig(param).then((res: any) => {
    if (res.code === 200) {
      useGlobalMessage('success', '子场景位置矫正成功！');
      window.scencePosCorrect.update();
      window.scencePosCorrect.reset();
      visible.value = false;
      emit('back');
    } else {
      useGlobalMessage('error', '子场景位置矫正失败！');
    }
  });
};
defineExpose({ init, cancel });
</script>

<style lang="scss" scoped>
::v-deep .ant-form-item-label {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

.scen-child-panel {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 16px;
  overflow: hidden;
  background: var(--primary-bg-color);

  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    a {
      color: var(--secnd-text-color);
    }
  }

  .content {
    padding: 20px 0;
  }

  .btns {
    display: flex;
    align-items: center;
    justify-content: center;

    .btn {
      width: 100px;
      margin: 0 10px;
    }
  }
}
</style>
