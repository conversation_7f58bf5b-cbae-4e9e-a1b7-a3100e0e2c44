<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/edtap-server/edtap-app/src/main/resources/db/dm.init.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/edtap-server/edtap-app/src/main/resources/db/dm/edtap-migration/v1.0.0/V2023.09.11.14.23.20__structure-init.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/edtap-server/edtap-app/src/main/resources/db/dm/edtap-migration/v1.0.0/V2023.09.11.14.23.49__data-init.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/edtap-server/edtap-app/src/main/resources/db/dm/edtap-migration/v1.0.0/V2025.03.27.10.54.54__新增用户团队管理表是否管理员字段.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/alarm/alarm-pojo/src/main/resources/db/dm/migration/alarm/1.0.0/V2023.04.12.11.35.18__structure-init.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/alarm/alarm-pojo/src/main/resources/db/dm/migration/alarm/1.0.0/V2023.04.28.15.17.21__data-init.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/effectpackage/effectpackage-pojo/src/main/resources/db/dm/migration/effectpackage/1.0.0/V2025.03.18.15.56.56__模版增加字段.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/effectpackage/effectpackage-pojo/src/main/resources/db/mysql/migration/effectpackage/1.0.0/V2025.06.03.16.33__新增效果包是否默认字段.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/identity/identity-pojo/src/main/resources/db/dm/migration/identity/1.0.1/V2025.08.27.15.05.19__新增用户映射列表菜单按钮权限.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/placement/placement-pojo/src/main/resources/db/dm/migration/placement/1.0.0/V2025.07.22.18.56.56__新增场景校准位置字段.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-pojo/src/main/resources/db/dm/migration/twin/1.0.0/V2025.06.03.16.11.11__新增菜单孪生体表.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-pojo/src/main/resources/db/dm/migration/twin/1.0.0/V2025.06.18.10.26__映射模型增加映射气泡逻辑.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-pojo/src/main/resources/db/dm/migration/twin/1.0.0/V2025.06.20.11.12__映射模型增加气泡名称.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-pojo/src/main/resources/db/dm/migration/twin/1.0.0/V2025.07.02.17.07.07__修改数据权限字段类型.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-pojo/src/main/resources/db/dm/migration/twin/1.0.0/V2025.07.21.10.24.24__新增图例孪生体表.sql" dialect="GenericSQL" />
  </component>
</project>