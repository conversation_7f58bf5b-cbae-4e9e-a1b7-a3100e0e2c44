MERGE INTO "sys_menu" t USING
    (SELECT 1960591841450586114 AS id, 1676546065498685442 AS pid, '[0],[1676545876796948481],[1676546065498685442],' AS pids, '孪生体查询用户映射' AS name, 'LSDX-GET-USERMAPPING' AS code, 2 AS type, 'icon-zuzhi' AS icon, '/' AS router, '/' AS component, 'sys-user:list-user-id-mapping' AS permission, 'XIANG_MU_ZI_YUAN' AS application, 1 AS open_type, 'Y' AS visible, '' AS link, '' AS redirect, 1 AS weight, 100 AS sort, NULL AS remark, NULL AS default_cam_info, 'normal' AS special, NULL AS plugin_id, NULL AS micro_app_id, NULL AS sys_category_id, 0 AS status, 1 AS create_user, NULL AS update_user, '0' AS iframe_type, '0' AS with_token FROM DUAL) s ON (t.id = s.id)
    WHEN MATCHED THEN UPDATE SET t.pid = s.pid, t.pids = s.pids, t.name = s.name, t.code = s.code, t.type = s.type, t.icon = s.icon, t.router = s.router, t.component = s.component, t.permission = s.permission, t.application = s.application, t.open_type = s.open_type, t.visible = s.visible, t.link = s.link, t.redirect = s.redirect, t.weight = s.weight, t.sort = s.sort, t.remark = s.remark, t.default_cam_info = s.default_cam_info, t.special = s.special, t.plugin_id = s.plugin_id, t.micro_app_id = s.micro_app_id, t.sys_category_id = s.sys_category_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user, t.iframe_type = s.iframe_type, t.with_token = s.with_token
    WHEN NOT MATCHED THEN INSERT (id, pid, pids, name, code, type, icon, router, component, permission, application, open_type, visible, link, redirect, weight, sort, remark, default_cam_info, special, plugin_id, micro_app_id, sys_category_id, status, create_user, update_user, iframe_type, with_token) VALUES (s.id, s.pid, s.pids, s.name, s.code, s.type, s.icon, s.router, s.component, s.permission, s.application, s.open_type, s.visible, s.link, s.redirect, s.weight, s.sort, s.remark, s.default_cam_info, s.special, s.plugin_id, s.micro_app_id, s.sys_category_id, s.status, s.create_user, s.update_user, s.iframe_type, s.with_token);

MERGE INTO "sys_role_menu" t USING
    (SELECT 1960600407145787394 AS id, 1738144969088643073 AS role_id, 1960591841450586114 AS menu_id, 0 AS status, 1 AS create_user, NULL AS update_user FROM DUAL) s ON (t.id = s.id)
    WHEN MATCHED THEN UPDATE SET t.role_id = s.role_id, t.menu_id = s.menu_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user
    WHEN NOT MATCHED THEN INSERT (id, role_id, menu_id, status, create_user, update_user) VALUES (s.id, s.role_id, s.menu_id, s.status, s.create_user, s.update_user);

MERGE INTO "sys_role_menu" t USING
    (SELECT 1960601581890646019 AS id, 1745640004528422914 AS role_id, 1960591841450586114 AS menu_id, 0 AS status, 1 AS create_user, NULL AS update_user FROM DUAL) s ON (t.id = s.id)
    WHEN MATCHED THEN UPDATE SET t.role_id = s.role_id, t.menu_id = s.menu_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user
    WHEN NOT MATCHED THEN INSERT (id, role_id, menu_id, status, create_user, update_user) VALUES (s.id, s.role_id, s.menu_id, s.status, s.create_user, s.update_user);