package com.uino.x.pedestal.identity.impl;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.uino.x.common.config.properties.MultiTenantProperties;
import com.uino.x.common.core.factory.TreeBuildFactory;
import com.uino.x.common.core.util.PageOrderUtils;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.label.enums.YesOrNotEnum;
import com.uino.x.common.pojo.entity.BaseEntity;
import com.uino.x.common.tool.base.*;
import com.uino.x.pedestal.alarm.dao.mapper.AlarmXxvMapper;
import com.uino.x.pedestal.alarm.pojo.entity.AlarmXxv;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.api.service.SysAppService;
import com.uino.x.pedestal.identity.api.service.SysMenuService;
import com.uino.x.pedestal.identity.common.enums.MenuOpenTypeEnum;
import com.uino.x.pedestal.identity.common.enums.MenuTypeEnum;
import com.uino.x.pedestal.identity.common.enums.MenuWeightEnum;
import com.uino.x.pedestal.identity.common.enums.exp.SysMenuExpEnum;
import com.uino.x.pedestal.identity.common.exception.SysMenuException;
import com.uino.x.pedestal.identity.dao.mapper.SysAppMapper;
import com.uino.x.pedestal.identity.dao.mapper.SysMenuMapper;
import com.uino.x.pedestal.identity.dao.mapper.SysRoleMenuMapper;
import com.uino.x.pedestal.identity.dao.mapper.SysUserRoleMapper;
import com.uino.x.pedestal.identity.pojo.domain.LoginMenuTreeNode;
import com.uino.x.pedestal.identity.pojo.domain.MenuBaseTreeNode;
import com.uino.x.pedestal.identity.pojo.domain.SysLoginUser;
import com.uino.x.pedestal.identity.pojo.domain.XxvTreeNode;
import com.uino.x.pedestal.identity.pojo.entity.*;
import com.uino.x.pedestal.identity.pojo.param.SysMenuParam;
import com.uino.x.pedestal.identity.pojo.vo.SysMenuVo;
import com.uino.x.pedestal.legend.dao.mapper.LegendManagementMapper;
import com.uino.x.pedestal.legend.pojo.param.LegendManagementParam;
import com.uino.x.pedestal.microapp.api.service.MicroAppService;
import com.uino.x.pedestal.microapp.pojo.entity.MicroApp;
import com.uino.x.pedestal.microapp.pojo.param.MicroAppDataInitParam;
import com.uino.x.pedestal.tenant.api.context.TenantContext;
import com.uino.x.pedestal.twin.dao.mapper.TwinClassMapper;
import com.uino.x.pedestal.twin.dao.mapper.TwinXxvMapper;
import com.uino.x.pedestal.twin.pojo.entity.TwinClass;
import com.uino.x.pedestal.twin.pojo.entity.TwinXxv;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 系统菜单service实现
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2020/3/13 16:05
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMenuMapper sysRoleMenuMapper;
    private final SysAppMapper sysAppMapper;
    private final TwinXxvMapper twinXxvMapper;
    private final AlarmXxvMapper alarmXxvMapper;
    private final TwinClassMapper twinClassMapper;
    private final LegendManagementMapper legendManagementMapper;

    private final SysMenuMapper sysMenuMapper;

    private final MicroAppService microAppService;

    private final MultiTenantProperties multiTenantProperties;

    @Override
    public List<SysMenuVo> list(SysMenuParam sysMenuParam) {

        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                // 根据所属应用查询
                .eq(StringUtils.isNotEmpty(sysMenuParam.getApplication()), SysMenu::getApplication, sysMenuParam.getApplication())
                // 根据子应用查询
                .eq(Objects.nonNull(sysMenuParam.getSysCategoryId()), SysMenu::getSysCategoryId, sysMenuParam.getSysCategoryId())
                // 根据菜单名称模糊查询
                .like(StringUtils.isNotEmpty(sysMenuParam.getName()), SysMenu::getName, sysMenuParam.getName())
                .eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());

        //用户自定义排序
        PageOrderUtils.wrapperOrder(queryWrapper, sysMenuParam.getSortField(), sysMenuParam.getSortRule());

        // 默认排序：根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysMenu::getSort).orderByAsc(SysMenu::getCreateTime);

        // 多租户禁用时，过滤相关数据
        queryWrapper.ne(!TenantContext.getMultiTenantEnable(), SysMenu::getApplication, SysAppService.TENANT_APP_CODE);
        // 填充应用
        List<SysMenuVo> sysMenuVoList = buildSysMenuAppName(this.list(queryWrapper));
        // 填充插件
        sysMenuVoList = buildSysMenuPluginName(sysMenuVoList);
        // 填充子应用
        sysMenuVoList = buildSysMenuMicroAppName(sysMenuVoList);
        //将结果集处理成树
        return new TreeBuildFactory<SysMenuVo>().doTreeBuild(sysMenuVoList);
    }

    @Override
    public List<SysMenuVo> listRole(SysMenuParam sysMenuParam) {

        Long sysLoginUserId = IdentityContext.getSysLoginUserId();
        LambdaQueryWrapper<SysUserRole> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(SysUserRole::getUserId, sysLoginUserId);
        List<SysUserRole> list1 = sysUserRoleMapper.selectList(sysUserRoleLambdaQueryWrapper);
        List<Long> longs = list1.stream().map(e -> e.getRoleId()).toList();

        LambdaQueryWrapper<SysRoleMenu> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(SysRoleMenu::getRoleId, longs);
        List<SysRoleMenu> list = sysRoleMenuMapper.selectList(lambdaQueryWrapper);
        List<Long> menuIds = list.stream().map(e -> e.getMenuId()).toList();

        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                // 根据所属应用查询
                .eq(StringUtils.isNotEmpty(sysMenuParam.getApplication()), SysMenu::getApplication, sysMenuParam.getApplication())
                // 根据子应用查询
                .eq(Objects.nonNull(sysMenuParam.getSysCategoryId()), SysMenu::getSysCategoryId, sysMenuParam.getSysCategoryId())
                // 根据菜单名称模糊查询
                .like(StringUtils.isNotEmpty(sysMenuParam.getName()), SysMenu::getName, sysMenuParam.getName())
                .eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode())
                .in(SysMenu::getId, menuIds);

        //用户自定义排序
        PageOrderUtils.wrapperOrder(queryWrapper, sysMenuParam.getSortField(), sysMenuParam.getSortRule());

        // 默认排序：根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysMenu::getSort).orderByAsc(SysMenu::getCreateTime);

        // 多租户禁用时，过滤相关数据
        queryWrapper.ne(!TenantContext.getMultiTenantEnable(), SysMenu::getApplication, SysAppService.TENANT_APP_CODE);
        // 填充应用
        List<SysMenuVo> sysMenuVoList = buildSysMenuAppName(this.list(queryWrapper));
        // 填充插件
        sysMenuVoList = buildSysMenuPluginName(sysMenuVoList);
        // 填充子应用
        sysMenuVoList = buildSysMenuMicroAppName(sysMenuVoList);
        //将结果集处理成树
        return new TreeBuildFactory<SysMenuVo>().doTreeBuild(sysMenuVoList);
    }

    /**
     * 构建系统菜单名称
     *
     * @param sysMenuList 系统菜单列表
     * @return 系统菜单视图列表
     * <AUTHOR>
     * @date 2020/3/21 11:01
     */
    private List<SysMenuVo> buildSysMenuAppName(List<SysMenu> sysMenuList) {

        List<SysApp> sysAppList = sysAppMapper.selectList(Wrappers.emptyWrapper());

        return sysMenuList.stream()
                .map(sysMenu -> {

                    SysMenuVo sysMenuVo = BeanUtils.copyToBean(sysMenu, SysMenuVo.class);
                    for (SysApp sysApp : sysAppList) {

                        String code = sysApp.getCode();
                        if (code.equals(sysMenuVo.getApplication())) {
                            sysMenuVo.setApplicationName(sysApp.getName());
                            break;
                        }
                    }
                    return sysMenuVo;
                }).collect(Collectors.toList());
    }

    /**
     * 构建系统插件名称
     *
     * @param sysMenuList 系统菜单列表
     * @return 系统菜单视图列表
     * <AUTHOR>
     * @date 2022-11-8 15:54:01
     */
    private List<SysMenuVo> buildSysMenuPluginName(List<SysMenuVo> sysMenuList) {


//        List<PluginInfo> pluginInfos = pluginInfoMapper.selectList(Wrappers.emptyWrapper());
//
//        return sysMenuList.stream()
//                .peek(sysMenu -> {
//                    for (PluginInfo pluginInfo : pluginInfos) {
//                        Long id = pluginInfo.getId();
//                        if (Objects.nonNull(id) && id.equals(sysMenu.getPluginId())) {
//                            sysMenu.setPluginName(pluginInfo.getName());
//                            break;
//                        }
//                    }
//                }).collect(Collectors.toList());
        return sysMenuList;
    }

    private List<SysMenuVo> buildSysMenuMicroAppName(List<SysMenuVo> sysMenuList) {


        List<MicroApp> microApps = microAppService.list();

        return sysMenuList.stream()
                .peek(sysMenu -> {
                    for (MicroApp microApp : microApps) {
                        Long id = microApp.getId();
                        if (Objects.nonNull(id) && id.equals(sysMenu.getMicroAppId())) {
                            sysMenu.setMicroAppName(microApp.getName());
                            break;
                        }
                    }
                }).collect(Collectors.toList());
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void sync(List<SysMenuParam> menus, SysCategoryInfo categoryInfo){
        if (CollectionUtils.isEmpty(menus)){
            return;
        }
        for (SysMenuParam menuParam:menus) {
            menuParam.setSysCategoryId(categoryInfo.getId());
            // 因为是软删除，原来的数据在物理意义上还存在，为了避免重复，重置一下
            //menuParam.setId(null);
            add(menuParam);
        }
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void add(SysMenuParam sysMenuParam) {
        // 校验参数
        checkParam(sysMenuParam, false);

        SysMenu sysMenu = new SysMenu();
        BeanUtils.copyIgnoreNullValue(sysMenuParam, sysMenu);

        // 设置新的pid
        String newPids = createNewPids(sysMenuParam.getPid());
        sysMenu.setPids(newPids);

        // 设置启用状态
        sysMenu.setStatus(StatusEnum.ENABLE.getCode());
        //添加XXV菜单关联的孪生体
        List<Long> twinIds = sysMenuParam.getTwinId() == null ? new ArrayList<>() : sysMenuParam.getTwinId();
        if (twinIds.size() > 0) {
            for (Long twinId : twinIds) {
                TwinXxv twinXxv = new TwinXxv();
                twinXxv.setXxvCode(sysMenu.getCode());
                twinXxv.setTwinId(twinId);
                twinXxvMapper.insert(twinXxv);
            }
        }
        //添加XXV菜单关联的设备告警分类
        addXxvAlarm(sysMenuParam);
        this.save(sysMenu);
        sysMenuParam.setId(sysMenu.getId());
        final Integer type = sysMenuParam.getType();
        if (MenuTypeEnum.PLUGIN.getCode().equals(type)) {
//            final PluginInfo pluginInfo = pluginInfoMapper.selectById(sysMenuParam.getPluginId());
//            AssertUtils.isTrue(Objects.nonNull(pluginInfo), HttpStatus.INTERNAL_SERVER_ERROR, "插件不存在");
//            // 如果是应用插件
//            if (Objects.equals(pluginInfo.getType(), PluginInfoTypeEnum.APP.getCode())) {
//                final String code = pluginInfo.getCode();
//                try {
//                    pluginInfoManagement.pluginDataLoad(code, new DataManagerInitParam(sysMenu.getApplication(), sysMenu.getId()));
//                } catch (Throwable e) {
//                    log.error(String.format("调用插件服务加载插件数据异常, code: %s", code), e);
//                    throw new SysMenuException("加载插件数据失败");
//                }
//            }
        }
        if (MenuTypeEnum.MICRO_APP.getCode().equals(type)) {
            final MicroApp microApp = microAppService.getById(sysMenuParam.getMicroAppId());
            AssertUtils.isTrue(Objects.nonNull(microApp), HttpStatus.INTERNAL_SERVER_ERROR, "子应用不存在");
            final String code = microApp.getCode();
            try {
                microAppService.init(new MicroAppDataInitParam(code, sysMenu.getApplication(), sysMenu.getId()));
            } catch (Throwable e) {
                log.error(String.format("调用子应用服务加载子应用数据异常, code: %s", code), e);
                throw new SysMenuException("加载子应用数据失败");
            }
        }
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void delete(SysMenuParam sysMenuParam) {
        Long id = sysMenuParam.getId();
        //级联删除子节点
        List<Long> childIdList = this.getChildIdListById(id);
        childIdList.add(id);
        LambdaUpdateWrapper<SysMenu> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SysMenu::getId, childIdList)
                .set(SysMenu::getStatus, StatusEnum.DELETED.getCode());
        this.remove(updateWrapper);
        //删除xxv菜单关联孪生体
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        List<String> codeList = this.list(queryWrapper.in(SysMenu::getId, childIdList)).stream().map(SysMenu::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(codeList)){
            LambdaQueryWrapper<TwinXxv> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(TwinXxv::getXxvCode, codeList);
            twinXxvMapper.delete(wrapper);
            //删除xxv菜单关联设备告警分类
            alarmXxvMapper.delete(new LambdaQueryWrapper<AlarmXxv>().in(AlarmXxv::getXxvCode, codeList));
        }

        if (CollectionUtils.isNotEmpty(childIdList)) {
            //级联删除该菜单及子菜单对应的角色-菜单表信息
            sysRoleMenuMapper.deleteRoleMenuListByMenuIdList(childIdList);
            // 删除关联的默认孪生体
//            final String tenant = TenantUtils.getTenantByRequest();
//            final String table = "twin_menu";
//            final String tableName = TenantConstant.MASTER.equals(tenant)
//                    ? DataSourceConstant.TWIN_X + StringConstant.DOT + table
//                    : tenant.toLowerCase() + StringConstant.UNDERSCORE + DataSourceConstant.TWIN_X + StringConstant.DOT + table;
//            sysMenuMapper.deleteTwinData(tableName, childIdList);
        }

    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void edit(SysMenuParam sysMenuParam) {
        // 校验参数
        checkParam(sysMenuParam, true);
        // 获取修改的菜单的旧数据（库中的）
        SysMenu oldMenu = this.querySysMenu(sysMenuParam);
        // 本菜单旧的pids
        Long oldPid = oldMenu.getPid();
        String oldPids = oldMenu.getPids();
        // 生成新的pid和pids
        Long newPid = sysMenuParam.getPid();
        String newPids = this.createNewPids(sysMenuParam.getPid());
        // 是否更新子应用的标识
        boolean updateSubAppsFlag = false;
        // 是否更新子节点的pids的标识
        boolean updateSubPidsFlag = false;
        // 如果应用有变化
        if (!sysMenuParam.getApplication().equals(oldMenu.getApplication())) {
            // 父节点不是根节点不能移动应用
            if (!oldPid.equals(0L)) {
                throw new SysMenuException(SysMenuExpEnum.CANT_MOVE_APP);
            }
            updateSubAppsFlag = true;
        }
        // 父节点有变化
        if (!newPid.equals(oldPid)) {
            updateSubPidsFlag = true;
        }
        // 开始更新所有子节点的配置
        if (updateSubAppsFlag || updateSubPidsFlag) {
            // 查找所有叶子节点，包含子节点的子节点
            LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(SysMenu::getPids, oldMenu.getId());
            List<SysMenu> list = this.list(queryWrapper);
            // 更新所有子节点的应用为当前菜单的应用
            if (CollectionUtils.isNotEmpty(list)) {
                // 更新所有子节点的application
                if (updateSubAppsFlag) {
                    list.forEach(child -> child.setApplication(sysMenuParam.getApplication()));
                }
                // 更新所有子节点的pids
                if (updateSubPidsFlag) {
                    list.forEach(child -> {
                        // 子节点pids组成 = 当前菜单新pids + 当前菜单id + 子节点自己的pids后缀
                        String oldParentCodesPrefix = oldPids + StringConstant.LEFT_SQ_BRACKET + oldMenu.getId()
                                + StringConstant.RIGHT_SQ_BRACKET + StringConstant.COMMA;
                        String oldParentCodesSuffix = child.getPids().substring(oldParentCodesPrefix.length());
                        String menuParentCodes = newPids + StringConstant.LEFT_SQ_BRACKET + oldMenu.getId()
                                + StringConstant.RIGHT_SQ_BRACKET + StringConstant.COMMA + oldParentCodesSuffix;
                        child.setPids(menuParentCodes);
                    });
                }
                this.updateBatchById(list);
            }
        }
        // 拷贝参数到实体中
        BeanUtils.copyIgnoreNullValue(sysMenuParam, oldMenu);
        if ("XXV".equals(sysMenuParam.getApplication())) {
            //删除菜单下关联的孪生体
            LambdaQueryWrapper<TwinXxv> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TwinXxv::getXxvCode, sysMenuParam.getCode());
            if (twinXxvMapper.selectList(wrapper).size() > 0) {
                twinXxvMapper.delete(wrapper);
            }
            //添加XXV菜单关联的孪生体
            if (sysMenuParam.getTwinId().size() > 0) {
                sysMenuParam.getTwinId().forEach(twinId -> {
                    TwinXxv twinXxv = new TwinXxv();
                    twinXxv.setXxvCode(sysMenuParam.getCode());
                    twinXxv.setTwinId(twinId);
                    twinXxvMapper.insert(twinXxv);
                });
            }
            //编辑菜单下关联的设备告警分类
            alarmXxvMapper.delete(new LambdaQueryWrapper<AlarmXxv>().eq(AlarmXxv::getXxvCode, sysMenuParam.getCode()));
            addXxvAlarm(sysMenuParam);
        }
        // 设置新的pids
        oldMenu.setPids(newPids);
        //不能修改状态，用修改状态接口修改状态
        oldMenu.setStatus(null);
        this.updateById(oldMenu);
    }

    /**
     * 添加剂xxv告警数据
     *
     * @param sysMenuParam 菜单参数
     * <AUTHOR>
     * @date 2021/12/2 16:26
     */
    private void addXxvAlarm(SysMenuParam sysMenuParam) {

        List<Long> alarmClassifyIds = sysMenuParam.getAlarmClassifyIds();
        if (alarmClassifyIds != null && alarmClassifyIds.size() > 0) {
            alarmClassifyIds.forEach(alarmClassifyId -> {
                List<Map> maps = alarmXxvMapper.selectMenuByClassify(alarmClassifyId);
                if (maps.size() > 0) {
                    throw new SysMenuException(maps.get(0).get("classify") + "告警分类已经属于菜单：" + maps.get(0).get("menu"));
                }
                AlarmXxv alarmXxv = new AlarmXxv();
                alarmXxv.setXxvCode(sysMenuParam.getCode());
                alarmXxv.setMappingId(alarmClassifyId);
                alarmXxvMapper.insert(alarmXxv);
            });
        }
    }

    @Override
    public SysMenu detail(SysMenuParam sysMenuParam) {
        return this.querySysMenu(sysMenuParam);
    }

    @Override
    public List<MenuBaseTreeNode> tree(SysMenuParam sysMenuParam) {

        List<MenuBaseTreeNode> menuTreeNodeList = Lists.newArrayList();
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(sysMenuParam)) {
            if (StringUtils.isNotEmpty(sysMenuParam.getApplication())) {
                queryWrapper.eq(SysMenu::getApplication, sysMenuParam.getApplication());
            }
        }
        // 根据子应用查询
        queryWrapper.eq(Objects.nonNull(sysMenuParam.getSysCategoryId()), SysMenu::getSysCategoryId, sysMenuParam.getSysCategoryId());
        queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode())
                .in(SysMenu::getType, Lists.newArrayList(MenuTypeEnum.DIR.getCode(), MenuTypeEnum.MENU.getCode()));
        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysMenu::getSort).orderByAsc(SysMenu::getCreateTime);
        this.list(queryWrapper).forEach(sysMenu -> {
            MenuBaseTreeNode menuTreeNode = new MenuBaseTreeNode();
            menuTreeNode.setId(sysMenu.getId());
            menuTreeNode.setParentId(sysMenu.getPid());
            menuTreeNode.setValue(String.valueOf(sysMenu.getId()));
            menuTreeNode.setTitle(sysMenu.getName());
            menuTreeNode.setWeight(sysMenu.getSort());
            menuTreeNodeList.add(menuTreeNode);
        });
        return new TreeBuildFactory<MenuBaseTreeNode>().doTreeBuild(menuTreeNodeList);
    }

    @Override
    public List<MenuBaseTreeNode> treeForGrant(SysMenuParam sysMenuParam) {

        LambdaQueryWrapper<SysApp> queryWrapper = new LambdaQueryWrapper<>();
        String application = sysMenuParam.getApplication();
        queryWrapper.eq(StringUtils.isNotBlank(application), SysApp::getCode, application);
        queryWrapper.eq(SysApp::getStatus, StatusEnum.ENABLE.getCode());

        // 根据子应用查询
        queryWrapper.eq(Objects.nonNull(sysMenuParam.getSysCategoryId()), SysApp::getSysCategoryId, sysMenuParam.getSysCategoryId());

        // 多租户禁用，过滤相关数据
        queryWrapper.ne(!TenantContext.getMultiTenantEnable(), SysApp::getCode, SysAppService.TENANT_APP_CODE);
        queryWrapper.eq(Objects.nonNull(sysMenuParam.getSysCategoryId()), SysApp::getSysCategoryId, sysMenuParam.getSysCategoryId());
        queryWrapper.orderByAsc(SysApp::getSort);

        final boolean isSuperAdmin = IdentityContext.isMe();
        return sysAppMapper.selectList(queryWrapper).stream()
                .map(app -> wrapperAppMenuNode(isSuperAdmin, app)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public MenuBaseTreeNode XxvtreeForGrant(Boolean hasLegendManagement) {
        List<XxvTreeNode> xxvTreeNodeList = Lists.newArrayList();
        MenuBaseTreeNode appMenuXxvTreeNode = new MenuBaseTreeNode();
        String application = "XXV";
        LambdaQueryWrapper<SysApp> wrapper = new LambdaQueryWrapper<>();
        SysApp sysApp = sysAppMapper.selectOne(wrapper.eq(SysApp::getCode, application));
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenu::getApplication, application);
        final boolean isSuperAdmin = IdentityContext.isMe();
        if (isSuperAdmin) {
            queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());
        } else {
            //非超级管理员则获取自己拥有的菜单，分配给人员，防止越级授权
            Long userId = IdentityContext.getSysLoginUserId();
            List<Long> roleIdList = sysUserRoleMapper.getUserRoleIdList(userId);
            if (CollectionUtils.isNotEmpty(roleIdList)) {
                List<Long> menuIdList = sysRoleMenuMapper.getRoleMenuIdList(roleIdList);
                if (CollectionUtils.isNotEmpty(menuIdList)) {
                    queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());
                    queryWrapper.and(q -> q.in(SysMenu::getId, menuIdList).or().eq(BaseEntity::getCreateUser, userId));
                } else {
                    //如果角色的菜单为空，则查不到菜单
                    return null;
                }
            } else {
                //如果角色为空，则根本没菜单
                return null;
            }
        }
        queryWrapper.orderByAsc(SysMenu::getSort);
        List<SysMenu> sysMenuList = this.list(queryWrapper);

        // 批量查询legendManagement
        Map<Long, List<LegendManagementParam>> legendMap;
        if (Objects.nonNull(hasLegendManagement) && hasLegendManagement) {
            List<Long> menuIds = sysMenuList.stream().map(SysMenu::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(menuIds)) {
                List<LegendManagementParam> allLegends = legendManagementMapper.getTreeByMenuIds(menuIds);
                legendMap = allLegends.stream().collect(Collectors.groupingBy(LegendManagementParam::getMenuId));
            } else {
                legendMap = new HashMap<>();
            }
        } else {
            legendMap = new HashMap<>();
        }

        // 批量查询TwinXxv和TwinClass
        List<String> menuCodes = sysMenuList.stream().map(SysMenu::getCode).collect(Collectors.toList());
        Map<String, List<TwinClass>> menuCodeToTwinClass = new HashMap<>();

        if (CollectionUtils.isNotEmpty(menuCodes)) {
            List<TwinXxv> allTwinXxv = twinXxvMapper.selectList(new LambdaQueryWrapper<TwinXxv>()
                    .in(TwinXxv::getXxvCode, menuCodes)
                    .eq(TwinXxv::getStatus, StatusEnum.ENABLE.getCode())
                    .groupBy(TwinXxv::getTwinId));

            if (CollectionUtils.isNotEmpty(allTwinXxv)) {
                Set<Long> twinIds = allTwinXxv.stream().map(TwinXxv::getTwinId).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(twinIds)) {
                    List<TwinClass> allTwinClass = twinClassMapper.selectList(new LambdaQueryWrapper<TwinClass>()
                            .in(TwinClass::getId, twinIds)
                            .eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode()));
                    Map<Long, TwinClass> twinClassMap = allTwinClass.stream()
                            .collect(Collectors.toMap(TwinClass::getId, Function.identity()));

                    // 构建menuCode到TwinClass的映射
                    allTwinXxv.forEach(twinXxv -> {
                        String xxvCode = twinXxv.getXxvCode();
                        TwinClass twinClass = twinClassMap.get(twinXxv.getTwinId());
                        if (twinClass != null) {
                            menuCodeToTwinClass.computeIfAbsent(xxvCode, k -> new ArrayList<>()).add(twinClass);
                        }
                    });
                }
            }
        }

        // 构建树节点
        sysMenuList.forEach(sysMenu -> {
            XxvTreeNode xxvTreeNode = new XxvTreeNode();
            xxvTreeNode.setId(sysMenu.getId());
            xxvTreeNode.setParentId(sysMenu.getPid());
            xxvTreeNode.setValue(String.valueOf(sysMenu.getId()));
            xxvTreeNode.setTitle(sysMenu.getName());
            xxvTreeNode.setWeight(sysMenu.getSort());
            xxvTreeNode.setCode(sysMenu.getCode());

            // 设置legendManagement
            if (Objects.nonNull(hasLegendManagement) && hasLegendManagement) {
                xxvTreeNode.setLegendManagementParams(legendMap.getOrDefault(sysMenu.getId(), new ArrayList<>()));
            }

            // 设置twinXxv
            xxvTreeNode.setTwinXxv(menuCodeToTwinClass.getOrDefault(sysMenu.getCode(), new ArrayList<>()));

            xxvTreeNode.setIcon(sysMenu.getIcon());
            xxvTreeNode.setRouter(sysMenu.getRouter());
            xxvTreeNode.setOpenType(sysMenu.getOpenType());
            xxvTreeNode.setComponent(sysMenu.getComponent());
            xxvTreeNode.setRemark(sysMenu.getRemark());
            xxvTreeNode.setLink(sysMenu.getLink());
            xxvTreeNode.setRedirect(sysMenu.getRedirect());
            xxvTreeNode.setType(sysMenu.getType());
            xxvTreeNode.setVisible(sysMenu.getVisible());
            xxvTreeNodeList.add(xxvTreeNode);
        });

        List<XxvTreeNode> xxvBaseTreeNodes = new TreeBuildFactory<XxvTreeNode>().doTreeBuild(xxvTreeNodeList);
        if (CollectionUtils.isEmpty(xxvBaseTreeNodes)) {
            return null;
        }
        Long sysAppId = sysApp.getId();
        for (XxvTreeNode menuBaseTreeNode : xxvBaseTreeNodes) {
            menuBaseTreeNode.setParentId(sysAppId);
        }
        appMenuXxvTreeNode.setId(sysAppId);
        appMenuXxvTreeNode.setParentId(0L);
        appMenuXxvTreeNode.setTitle(sysApp.getName());
        appMenuXxvTreeNode.setValue(String.valueOf(sysAppId));
        appMenuXxvTreeNode.setWeight(sysApp.getSort());
        appMenuXxvTreeNode.setChildren(xxvBaseTreeNodes);
        return appMenuXxvTreeNode;
    }

    /**
     * 包装应用级树
     *
     * @param isSuperAdmin 是否是超管
     * @param sysApp       应用信息
     * @return 菜单树节点
     */
    private MenuBaseTreeNode wrapperAppMenuNode(boolean isSuperAdmin, SysApp sysApp) {

        List<MenuBaseTreeNode> menuTreeNodeList = Lists.newArrayList();
        MenuBaseTreeNode appMenuBaseTreeNode = new MenuBaseTreeNode();
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenu::getApplication, sysApp.getCode());
        if (isSuperAdmin) {
            queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());
        } else {
            //非超级管理员则获取自己拥有的菜单，分配给人员，防止越级授权
            Long userId = IdentityContext.getSysLoginUserId();
            List<Long> roleIdList = sysUserRoleMapper.getUserRoleIdList(userId);
            if (CollectionUtils.isNotEmpty(roleIdList)) {
                List<Long> menuIdList = sysRoleMenuMapper.getRoleMenuIdList(roleIdList);
                if (CollectionUtils.isNotEmpty(menuIdList)) {
                    queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());
//                    queryWrapper.and(q -> q.in(SysMenu::getId, menuIdList).or().eq(BaseEntity::getCreateUser, userId));
                } else {
                    //如果角色的菜单为空，则查不到菜单
                    return null;
                }
            } else {
                //如果角色为空，则根本没菜单
                return null;
            }
        }
        //根据排序升序排列，序号越小越在前
        queryWrapper.orderByAsc(SysMenu::getSort);
        this.list(queryWrapper).forEach(sysMenu -> {
            MenuBaseTreeNode menuTreeNode = new MenuBaseTreeNode();
            menuTreeNode.setId(sysMenu.getId());
            menuTreeNode.setParentId(sysMenu.getPid());
            menuTreeNode.setValue(String.valueOf(sysMenu.getId()));
            menuTreeNode.setTitle(sysMenu.getName());
            menuTreeNode.setWeight(sysMenu.getSort());
            menuTreeNodeList.add(menuTreeNode);
        });
        List<MenuBaseTreeNode> menuBaseTreeNodes = new TreeBuildFactory<MenuBaseTreeNode>().doTreeBuild(menuTreeNodeList);
        Long sysAppId = sysApp.getId();

        if (CollectionUtils.isNotEmpty(menuBaseTreeNodes)) {
            for (MenuBaseTreeNode menuBaseTreeNode : menuBaseTreeNodes) {
                menuBaseTreeNode.setParentId(sysAppId);
            }
        }

        appMenuBaseTreeNode.setId(sysAppId);
        appMenuBaseTreeNode.setParentId(0L);
        appMenuBaseTreeNode.setTitle(sysApp.getName());
        appMenuBaseTreeNode.setValue(String.valueOf(sysAppId));
        appMenuBaseTreeNode.setWeight(sysApp.getSort());
        appMenuBaseTreeNode.setChildren(menuBaseTreeNodes);
        return appMenuBaseTreeNode;
    }

    @Override
    public boolean hasMenu(String appCode, Long sysCategoryId) {
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysMenu::getApplication, appCode)
                .eq(SysMenu::getSysCategoryId, sysCategoryId)
                .ne(SysMenu::getStatus, StatusEnum.DELETED.getCode());
        return this.list(queryWrapper).size() != 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer editApplicationByApplication(String oldApplication, String newApplication) {

        return getBaseMapper().updateApplicationByApplication(oldApplication, newApplication);
    }


    /**
     * 校验参数
     *
     * <AUTHOR>
     * @date 2020/3/27 9:15
     */
    private void checkParam(SysMenuParam sysMenuParam, boolean isExcludeSelf) {
        //菜单类型（字典 0目录 1菜单 2按钮）
        Integer type = sysMenuParam.getType();

        Integer openType = sysMenuParam.getOpenType();

        String router = sysMenuParam.getRouter();
        String component = sysMenuParam.getComponent();

        String permission = sysMenuParam.getPermission();
        if (!MenuOpenTypeEnum.NONE.getCode().equals(openType)) {
            if (StringUtils.isBlank(router)) {

                throw ThrowUtils.getThrow().badRequest("路由地址不能为空");
            }
            if (StringUtils.isBlank(component)) {

                throw ThrowUtils.getThrow().badRequest("组件地址不能为空");
            }
        }

        if (MenuTypeEnum.DIR.getCode().equals(type)) {
            if (StringUtils.isEmpty(router)) {
                throw new SysMenuException(SysMenuExpEnum.MENU_ROUTER_EMPTY);
            }
        }

        if (MenuTypeEnum.MENU.getCode().equals(type)) {
            if (StringUtils.isEmpty(router)) {
                throw new SysMenuException(SysMenuExpEnum.MENU_ROUTER_EMPTY);
            }
            if (Objects.isNull(openType)) {
                throw new SysMenuException(SysMenuExpEnum.MENU_OPEN_TYPE_EMPTY);
            }
        }

        if (MenuTypeEnum.BTN.getCode().equals(type)) {
            if (StringUtils.isEmpty(permission)) {
                throw new SysMenuException(SysMenuExpEnum.MENU_PERMISSION_EMPTY);
            }
        }

        if (MenuTypeEnum.PLUGIN.getCode().equals(type)) {
            if (Objects.isNull(sysMenuParam.getPluginId())) {
                throw new SysMenuException(SysMenuExpEnum.PLUGIN_CANT_EMPTY);
            }
        }

        if (MenuTypeEnum.MICRO_APP.getCode().equals(type)) {
            if (Objects.isNull(sysMenuParam.getMicroAppId())) {
                throw new SysMenuException(SysMenuExpEnum.MICRO_APP_CANT_EMPTY);
            }
        }

        // 如果是编辑菜单时候，pid和id不能一致，一致会导致无限递归
        if (isExcludeSelf) {
            if (sysMenuParam.getId().equals(sysMenuParam.getPid())) {
                throw new SysMenuException(SysMenuExpEnum.PID_CANT_EQ_ID);
            }

            // 如果是编辑，父id不能为自己的子节点
            List<Long> childIdListById = this.getChildIdListById(sysMenuParam.getId());
            if (CollectionUtils.isNotEmpty(childIdListById)) {
                if (childIdListById.contains(sysMenuParam.getPid())) {
                    throw new SysMenuException(SysMenuExpEnum.PID_CANT_EQ_CHILD_ID);
                }
            }
        }

        Long id = sysMenuParam.getId();
        String name = sysMenuParam.getName();
        String code = sysMenuParam.getCode();
        final Long sysCategoryId = sysMenuParam.getSysCategoryId();

        LambdaQueryWrapper<SysMenu> queryWrapperByName = new LambdaQueryWrapper<>();
        queryWrapperByName.eq(SysMenu::getName, name)
                .eq(SysMenu::getApplication, sysMenuParam.getApplication())
                .ne(SysMenu::getStatus, StatusEnum.DELETED.getCode())
                .eq(SysMenu::getPid, sysMenuParam.getPid())
                .eq(SysMenu::getSysCategoryId, sysCategoryId);

        LambdaQueryWrapper<SysMenu> queryWrapperByCode = new LambdaQueryWrapper<>();
        queryWrapperByCode.eq(SysMenu::getCode, code)
                .eq(SysMenu::getApplication, sysMenuParam.getApplication())
                .ne(SysMenu::getStatus, StatusEnum.DELETED.getCode())
                .eq(SysMenu::getSysCategoryId, sysCategoryId);

        if (isExcludeSelf) {
            queryWrapperByName.ne(SysMenu::getId, id);
            queryWrapperByCode.ne(SysMenu::getId, id);
        }

        long countByName = this.count(queryWrapperByName);
        long countByCode = this.count(queryWrapperByCode);

        if (countByName >= 1) {
            throw new SysMenuException(SysMenuExpEnum.MENU_NAME_REPEAT);
        }
        if (countByCode >= 1) {
            throw new SysMenuException(SysMenuExpEnum.MENU_CODE_REPEAT);
        }
    }

    /**
     * 获取系统菜单
     *
     * <AUTHOR>
     * @date 2020/3/27 9:13
     */
    private SysMenu querySysMenu(SysMenuParam sysMenuParam) {
        SysMenu sysMenu = this.getById(sysMenuParam.getId());
        if (Objects.isNull(sysMenu)) {
            throw new SysMenuException(SysMenuExpEnum.MENU_NOT_EXIST);
        }
        return sysMenu;
    }

    /**
     * 创建pids的值
     * <p>
     * 如果pid是0顶级节点，pids就是 [0],
     * <p>
     * 如果pid不是顶级节点，pids就是 pid菜单的pids + [pid] + ,
     *
     * <AUTHOR>
     * @date 2020/3/26 11:28
     */
    private String createNewPids(Long pid) {
        if (pid.equals(0L)) {
            return StringConstant.LEFT_SQ_BRACKET + 0 + StringConstant.RIGHT_SQ_BRACKET
                    + StringConstant.COMMA;
        } else {
            //获取父菜单
            SysMenu parentMenu = this.getById(pid);
            return parentMenu.getPids()
                    + StringConstant.LEFT_SQ_BRACKET + pid + StringConstant.RIGHT_SQ_BRACKET
                    + StringConstant.COMMA;
        }
    }

    /**
     * 根据节点id获取所有子节点id集合
     *
     * <AUTHOR>
     * @date 2020/3/26 11:31
     */
    private List<Long> getChildIdListById(Long id) {
        List<Long> childIdList = Lists.newArrayList();
        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SysMenu::getPids, StringConstant.LEFT_SQ_BRACKET + id +
                StringConstant.RIGHT_SQ_BRACKET);
        this.list(queryWrapper).forEach(sysMenu -> childIdList.add(sysMenu.getId()));
        return childIdList;
    }


    @Override
    public List<SysMenu> list(Wrapper<SysMenu> queryWrapper) {

        return super.list(queryWrapper);
    }

    @Override
    public SysMenu getOneByCodeAndApp(String code, String app) {
        final LambdaQueryWrapper<SysMenu> eqCodeWrapper = Wrappers.<SysMenu>lambdaQuery()
                .eq(SysMenu::getCode, code)
                .eq(SysMenu::getApplication, app)
                .ne(SysMenu::getStatus, StatusEnum.DELETED.getCode());

        return this.getOne(eqCodeWrapper, false);
    }

    @Override
    public List<String> getLoginPermissions(Long userId) {
        Set<String> permissions = Sets.newHashSet();
        // 根据用户查询角色
        List<Long> roleIdList = sysUserRoleMapper.getUserRoleIdList(userId);
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            List<Long> menuIdList = sysRoleMenuMapper.getRoleMenuIdList(roleIdList);
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(SysMenu::getId, menuIdList).eq(SysMenu::getType, MenuTypeEnum.BTN.getCode())
                        .eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());

                // 多租户禁用时，过滤相关数据
                queryWrapper.ne(!multiTenantProperties.isEnable(), SysMenu::getApplication, SysAppService.TENANT_APP_CODE);

                sysMenuMapper.selectList(queryWrapper).forEach(sysMenu -> permissions.add(sysMenu.getPermission()));
            }
        }
        return Lists.newArrayList(permissions);
    }

    @Override
    public List<String> getUserMenuAppCodeList(Long userId) {

        Set<String> appCodeSet = Sets.newHashSet();
        List<Long> roleIdList = sysUserRoleMapper.getUserRoleIdList(userId);

        if (CollectionUtils.isNotEmpty(roleIdList)) {
            List<Long> menuOrAppIdList = sysRoleMenuMapper.getRoleMenuIdList(roleIdList);

            if (CollectionUtils.isNotEmpty(menuOrAppIdList)) {
                final LambdaQueryWrapper<SysApp> queryWrapper = Wrappers.<SysApp>lambdaQuery()
                        .in(SysApp::getId, menuOrAppIdList)
                        .eq(SysApp::getStatus, StatusEnum.ENABLE.getCode());

                sysAppMapper.selectList(queryWrapper).forEach(sysApp -> appCodeSet.add(sysApp.getCode()));

                final LambdaQueryWrapper<SysMenu> menuWrapper = Wrappers.<SysMenu>lambdaQuery()
                        .in(SysMenu::getId, menuOrAppIdList)
                        .eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode());
                sysMenuMapper.selectList(menuWrapper).forEach(sysMenu -> appCodeSet.add(sysMenu.getApplication()));
            }
        }

        return Lists.newArrayList(appCodeSet);
    }

    @Override
    public List<LoginMenuTreeNode> getLoginMenusAntDesign(Long userId, String appCode, Long sysCategoryId) {

        return getLoginMenusAntDesign(userId, Collections.singletonList(appCode), sysCategoryId);
    }

    @Override
    public List<LoginMenuTreeNode> getLoginMenusAntDesign(Long userId, List<String> appCodeList, Long sysCategoryId) {
        // 根据app查询角色权限菜单列表
        final List<SysMenu> sysMenuList = appCodeList.stream()
                .map(appCode -> listRoleMenuByApp(userId, appCode, sysCategoryId))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        //转换成登录菜单
        return this.convertSysMenuToLoginMenu(sysMenuList);
    }

    /**
     * 将SysMenu格式菜单转换为LoginMenuTreeNode菜单
     *
     * <AUTHOR>
     * @date 2020/4/17 17:53
     */
    private List<LoginMenuTreeNode> convertSysMenuToLoginMenu(List<SysMenu> sysMenuList) {

        List<LoginMenuTreeNode> antDesignMenuTreeNodeList = Lists.newArrayList();
        sysMenuList.forEach(sysMenu -> {
            LoginMenuTreeNode loginMenuTreeNode = new LoginMenuTreeNode();
            loginMenuTreeNode.setComponent(sysMenu.getComponent());
            loginMenuTreeNode.setId(sysMenu.getId());
            loginMenuTreeNode.setName(sysMenu.getCode());
            loginMenuTreeNode.setPath(sysMenu.getRouter());
            loginMenuTreeNode.setPid(sysMenu.getPid());
            loginMenuTreeNode.setSpecial(sysMenu.getSpecial());
            loginMenuTreeNode.setPluginId(sysMenu.getPluginId());
            loginMenuTreeNode.setMicroAppId(sysMenu.getMicroAppId());
            LoginMenuTreeNode.Meta mateItem = new LoginMenuTreeNode().new Meta();
            mateItem.setIcon(sysMenu.getIcon());
            mateItem.setTitle(sysMenu.getName());
            mateItem.setLink(sysMenu.getLink());
            mateItem.setType(sysMenu.getType());
            mateItem.setOpenType(sysMenu.getOpenType());
            mateItem.setIframeType(sysMenu.getIframeType());
            mateItem.setWithToken(sysMenu.getWithToken());
            //是否可见
            mateItem.setShow(!YesOrNotEnum.N.getCode().equals(sysMenu.getVisible()));
            //设置的首页，默认打开此链接
            loginMenuTreeNode.setRedirect(sysMenu.getRedirect());
            //是否是外链
            if (MenuOpenTypeEnum.OUTER.getCode().equals(sysMenu.getOpenType())) {
                //打开外链
                mateItem.setTarget("_blank");
                loginMenuTreeNode.setPath(sysMenu.getLink());
                loginMenuTreeNode.setRedirect(sysMenu.getLink());
            }
            loginMenuTreeNode.setMeta(mateItem);
            antDesignMenuTreeNodeList.add(loginMenuTreeNode);
        });
        return antDesignMenuTreeNodeList;
    }


    /**
     * 根据app查询角色权限菜单列表
     *
     * @param userId        用户id
     * @param appCode       应用code
     * @param sysCategoryId 系统种类
     * @return 菜单列表
     * <AUTHOR>
     * @date 2021/9/15 17:26
     */
    private List<SysMenu> listRoleMenuByApp(Long userId, String appCode, Long sysCategoryId) {

        LambdaQueryWrapper<SysMenu> queryWrapper = new LambdaQueryWrapper<>();
        final SysLoginUser sysLoginUser = BeanUtils.copyToBean(IdentityContext.getUserById(userId), SysLoginUser.class);
        // 超级管理员
        if (IdentityContext.isMe(sysLoginUser)) {

            //查询权重不为业务权重的且类型不是按钮的
            queryWrapper.eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode())
                    .eq(SysMenu::getApplication, appCode)
                    .eq(Objects.nonNull(sysCategoryId), SysMenu::getSysCategoryId, sysCategoryId)
                    .notIn(SysMenu::getType, MenuTypeEnum.BTN.getCode())
                    .notIn(SysMenu::getWeight, MenuWeightEnum.DEFAULT_WEIGHT.getCode())
                    .orderByAsc(SysMenu::getSort);
        } else {
            //非超级管理员则获取自己角色所拥有的菜单集合
            List<Long> roleIdList = sysUserRoleMapper.getUserRoleIdList(userId);
            if (CollectionUtils.isNotEmpty(roleIdList)) {
                List<Long> menuIdList = sysRoleMenuMapper.getRoleMenuIdList(roleIdList);
                if (CollectionUtils.isNotEmpty(menuIdList)) {
                    queryWrapper.in(SysMenu::getId, menuIdList)
                            .eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode())
                            .eq(SysMenu::getApplication, appCode)
                            .eq(Objects.nonNull(sysCategoryId), SysMenu::getSysCategoryId, sysCategoryId)
                            .notIn(SysMenu::getType, MenuTypeEnum.BTN.getCode())
                            .orderByAsc(SysMenu::getSort);

                } else {
                    //如果角色的菜单为空，则查不到菜单
                    return Lists.newArrayList();
                }
            } else {
                //如果角色为空，则根本没菜单
                return Lists.newArrayList();
            }
        }
        //查询列表
        return sysMenuMapper.selectList(queryWrapper);
    }
}
