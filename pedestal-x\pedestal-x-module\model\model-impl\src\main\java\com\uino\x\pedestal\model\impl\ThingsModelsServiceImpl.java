package com.uino.x.pedestal.model.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.uino.x.common.cache.CacheRefresh;
import com.uino.x.common.core.factory.ExecutorServiceFactory;
import com.uino.x.common.core.factory.PageFactory;
import com.uino.x.common.core.util.*;
import com.uino.x.common.label.annotation.aop.RedissonLock;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.enums.exp.ExpEnumOption;
import com.uino.x.common.objectstorage.template.ObjectStorageTemplate;
import com.uino.x.common.pojo.domain.node.ClassifyTreeNode;
import com.uino.x.common.pojo.entity.BaseEntity;
import com.uino.x.common.pojo.response.ResponseData;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.base.BeanUtils;
import com.uino.x.common.tool.base.DateUtils;
import com.uino.x.common.tool.base.ThrowUtils;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.common.utils.ProximabPathUtil;
import com.uino.x.pedestal.effectpackage.common.utils.PlacementDownloadUtils;
import com.uino.x.pedestal.file.common.properties.DownloadProperties;
import com.uino.x.pedestal.file.common.util.FileViewer;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.message.api.RealTimeMessageApi;
import com.uino.x.pedestal.message.pojo.entity.SysRealTimeMessage;
import com.uino.x.pedestal.model.api.service.ThingsModelsService;
import com.uino.x.pedestal.model.common.exception.TextureResourceException;
import com.uino.x.pedestal.model.common.exception.ThingsModelException;
import com.uino.x.pedestal.model.common.properties.ModelConfigProperties;
import com.uino.x.pedestal.model.dao.mapper.SceneProjectModelMapper;
import com.uino.x.pedestal.model.dao.mapper.TextureResourceMapper;
import com.uino.x.pedestal.model.dao.mapper.ThingsModelMapper;
import com.uino.x.pedestal.model.impl.util.*;
import com.uino.x.pedestal.model.pojo.entity.ThingsModel;
import com.uino.x.pedestal.model.pojo.param.ThingsModelClassifyAddParam;
import com.uino.x.pedestal.model.pojo.param.ThingsModelClassifyDeleteParam;
import com.uino.x.pedestal.model.pojo.param.ThingsModelClassifyUpdateParam;
import com.uino.x.pedestal.model.pojo.param.ThingsModelParam;
import com.uino.x.pedestal.model.pojo.vo.IndustryVo;
import com.uino.x.pedestal.model.pojo.vo.ThingsModelPageVo;
import com.uino.x.pedestal.model.pojo.vo.ThingsModelVo;
import com.uino.x.pedestal.placement.api.SceneApi;
import com.uino.x.pedestal.placement.common.function.ResourceAsyncTask;
import com.uino.x.pedestal.placement.common.utils.SyncModelKit;
import com.uino.x.pedestal.placement.pojo.entity.SceneModel;
import com.uino.x.pedestal.placement.pojo.entity.SceneRecord;
import com.uino.x.pedestal.placement.pojo.entity.TextureResource;
import com.uino.x.pedestal.placement.pojo.param.SceneModelParam;
import com.uino.x.pedestal.placement.pojo.param.SceneResourceParam;
import com.uino.x.pedestal.placement.pojo.param.SceneResourceWrapperParam;
import com.uino.x.pedestal.placement.pojo.vo.SceneResourcePageVo;
import com.uino.x.pedestal.placement.pojo.vo.SceneResourceVo;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 物模型service实现
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/7/1 16:32
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ThingsModelsServiceImpl extends ServiceImpl<ThingsModelMapper, ThingsModel> implements ThingsModelsService {

    public static final ExecutorService EXECUTOR_SERVICE = ExecutorServiceFactory.createThreadPool(ThingsModelsServiceImpl.class);

    private final ThingsModelMapper pluginThingsModelMapper;
    private final SceneProjectModelMapper sceneProjectModelMapper;
    private final TextureResourceMapper textureResourceMapper;
    private final ResourceAsyncTask asyncTask;
    private final RealTimeMessageApi realTimeMessageService;
    private final SceneApi sceneApi;
    private final ObjectStorageTemplate ost;
    private final CacheRefresh cacheRefresh;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void synchronizeModelClassify(String param, boolean importOp, boolean flag) {
        JSONObject modelJson = null;
        JSONArray industry;
        if (flag) {
            // 为在线同步分类
            if (StringUtils.isEmpty(param)) {
                param = ModelConfigProperties.getModelBasexUrl();
            }
            modelJson = getMomodaJSON(param);
        } else {
            // 离线同步分类
            try {
                modelJson = JSONObject.parseObject(param);
            } catch (Exception e) {
                log.error("格式化JSON发生异常：", e);
            }
        }
        if (Objects.isNull(modelJson)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "无效的JSON数据"));
        }
        JSONObject data = (JSONObject) modelJson.get("data");
        if (null == data) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "JSON缺少数据节点"));
        }
        industry = data.getJSONArray("industry");
        if (Objects.isNull(industry)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型json未读取到industry数据"));
        }
        // 这里是拿到请求后的模型分类
        List<ThingsModel> result = new ArrayList<>(20);
        // 将JSON树转换list（industry留给for校验）
        createThingModelList(industry, "", result);
        if (CollectionUtils.isEmpty(result)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "解析JSON失败"));
        }
        ThingsModel where = new ThingsModel();
        // TODO 原则上项目模型和基础模型的id不会重复，但在开发中，发现有重复，这里一并查出
        where.setModelType(1);
        // 只查询基础模型
        List<ThingsModel> pluginThingsModels = pluginThingsModelMapper.queryModel(where);
        Map<String, ThingsModel> existModel = pluginThingsModels.stream().collect(Collectors.toMap(ThingsModel::getModelId, e -> e));
        // 获取oss上的模型id，方便下面计算大小
        List<String> modelFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/", false).getNames();
        Set<String> modelExistCollect = new HashSet<>(modelFilePath);
        if (!importOp) {
            // 采用覆盖的方式导入，需先将之前的模型分类进行删除
            pluginThingsModelMapper.deleteBasicModel();
            for (ThingsModel item : result) {
                if (2 == item.getDirFlag() || StringUtils.isEmpty(item.getModelId())) {
                    // 为目录节点
                    item.setModelType(2);
                    continue;
                }
                // 判断之前的模型分类及数据是否存在
                ThingsModel model = existModel.getOrDefault(item.getModelId(), null);
                if (Objects.nonNull(model)) {
                    item.setModelType(1);
                }
                boolean exist = modelExistCollect.contains(TenantUtils.getTenantByRequest() + "/" + "model/" + item.getModelId() + "/");
                if (exist) {
                    // 计算一下大小
                    long fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(),"/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + item.getModelId()).getSize();
                    item.setFileSize(fileSize);
                }
                item.setFileExist(exist ? 2 : 1);
            }
            // 其实就是普通的insert
            pluginThingsModelMapper.insertForBatch(result);
        } else {
            // 采用追加的方式进行导入，有就更新，没有就做一次添加
            for (ThingsModel item : result) {
                try {
                    // 判断之前的模型分类及数据是否存在
                    ThingsModel model = null;
                    if (StringUtils.isNotEmpty(item.getModelId())) {
                        model = existModel.getOrDefault(item.getModelId(), null);
                    }
                    boolean exist = modelExistCollect.contains(TenantUtils.getTenantByRequest() + "/" + "model/" + item.getModelId() + "/");
                    long fileSize = 0L;
                    if (exist) {
                        fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(),"/" + TenantUtils.getTenantByRequest() + "/" +ModelConfigProperties.getModelPath() + "/" + item.getModelId()).getSize();
                    }
                    if (Objects.nonNull(model)) {
                        model.setCreateTime(model.getCreateTime());
                        model.setUpdateTime(new Date());
                        model.setFileExist(exist ? 2 : 1);
                        // 文件不存在，直接默认0
                        model.setFileSize(exist ? fileSize : 0L);
                        if (2 == model.getDirFlag()) {
                            // 为目录节点
                            model.setModelType(2);
                        }
                        pluginThingsModelMapper.updateById(model);
                    } else {
                        if (2 == item.getDirFlag()) {
                            // 为目录节点
                            item.setModelType(2);
                        }
                        item.setFileExist(exist ? 2 : 1);
                        pluginThingsModelMapper.insertModel(item);
                    }
                } catch (Exception e) {
                    // 本条数据处理失败后，继续操作下一条数据
                    log.error("在同步模型分类时，发生异常，根本原因：", e);
                }
            }
        }
    }

    @Override
    public JSONObject getMomodaJSON(String url) {
        HttpURLConnection connection = null;
        StringBuilder json = new StringBuilder();
        try {
            URL getUrl = new URL(url);
            connection = (HttpURLConnection) getUrl.openConnection();
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader reader = new BufferedReader(inputStreamReader);
            String lines;
            while ((lines = reader.readLine()) != null) {
                json.append(lines);
            }
            reader.close();
            connection.disconnect();
            return JSONObject.parseObject(json.toString());
        } catch (Exception e) {
            log.error("在请求模型库JSON文件时，发生了异常：", e);
            return null;
        } finally {
            if (Objects.nonNull(connection)) {
                connection.disconnect();
            }
        }
    }

    @Override
    public void createThingModelList(JSONArray child, String path, List<ThingsModel> result) {
        final long userId = IdentityContext.getSysLoginUserId();
        for (Object o : child) {
            JSONObject item = (JSONObject) o;
            String type = item.getString("type");
            if ("Floder".equals(type)) {
                // 本级为目录结构，还有子级
                JSONArray children = (JSONArray) item.get("children");
                String currentPath = ("".equals(path)) ? item.getString("title") : path + "/" + item.getString("title");
                if (children.isEmpty()) {
                    // 虽然说是目录，但是子节点为空
                    ThingsModel thingModel = new ThingsModel();
                    thingModel.setClassify(currentPath);
                    thingModel.setModelType(1);
                    thingModel.setCreateTime(new Date());
                    thingModel.setCreateUser(userId);
                    thingModel.setDirFlag(2);
                    thingModel.setModelId(UUID.randomUUID().toString());
                    result.add(thingModel);
                } else {
                    // 还有子级
                    createThingModelList(children, currentPath, result);
                }
            } else {
                // 没有子级
                ThingsModel thingModel = new ThingsModel();
                thingModel.setCreateTime(new Date());
                thingModel.setModelType(1);
                //thingModel.setDirFlag(1);
                thingModel.setModelId(item.getString("id"));
                String modelType = item.getString("type");
                if ("Particles".equals(modelType)) {
                    // TODO 粒子类型的特殊处理
                }
                thingModel.setType(modelType);
                thingModel.setTitle(item.getString("title"));
                thingModel.setVersion(item.getString("version"));
                JSONObject props = item.getJSONObject("props");
                if (null != props && props.size() > 0) {
                    String tags = props.getString("tags");
                    thingModel.setTags(StringUtils.isEmpty(tags) ? null : tags);
                }
                JSONArray size = item.getJSONArray("size");
                if (null != size && size.size() > 0) {
                    thingModel.setSize(item.getString("size"));
                }
                thingModel.setClassify(path);
                thingModel.setCreateUser(userId);
                if (!"Texture".equals(modelType)) {
                    result.add(thingModel);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:saveThingsByClassify:' + #param", message = "当前模型分类正在同步中，请稍后再试")
    @Override
    public void saveThingsByClassify(ThingsModelParam param) {
        ThingsModel where = new ThingsModel();
        where.setClassify(param.getClassify());
        where.setModelType(1);
        // 不区分模型是否存在，统一放到执行模型同步方法中判断
        List<ThingsModel> pluginThingsModels = pluginThingsModelMapper.queryModel(where);
        if (CollectionUtils.isEmpty(pluginThingsModels)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该分类下，没有模型资源"));
        }
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_saveThingsByClassify_" + (param.getClassify()).hashCode(), "同步选中分类下模型");
        final long sysLoginUser = IdentityContext.getSysLoginUserId();
//        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_saveThingsByClassify_" + (param.getClassify()).hashCode(), DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            JSONObject result = currentProxy().onlineSaveThings(pluginThingsModels, sysLoginUser, param.isAsyncFlag());
            JSONArray errorList = result.getJSONArray("error");
            JSONArray successList = result.getJSONArray("success");
            int successNum = 0;
            if (!Objects.isNull(successList) && !successList.isEmpty()) {
                successNum = successList.size();
            }
            String message = String.format("基础模型同步完成，成功：%s个", successNum);
            if (!Objects.isNull(errorList) && !errorList.isEmpty()) {
                StringJoiner joiner = new StringJoiner("，", "[", "]");
                errorList.forEach(e -> joiner.add(e.toString()));
                message += "，以下模型同步失败：" + joiner;
            }
            log.warn("基础模型同步完成");
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
        } catch (Exception e) {
            log.warn("基础模型同步失败，原因：", e);
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "基础模型同步失败！", null));
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "基础模型同步失败"));
        } finally {
//                taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_saveThingsByClassify_" + (param.getClassify()).hashCode());
            log.warn("基础模型资源同步完成");
        }
//        }, EXECUTOR_SERVICE);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:saveThingsById:' + #id", message = "当前模型正在同步中，请稍后再试")
    @Override
    public void saveThingsById(Long id, int type, Boolean asyncFlag) {
        List<ThingsModel> pluginThingsModels;
        if (Objects.isNull(id)) {
            // 未指定时，同步该分类下的模型
            ThingsModel where = new ThingsModel();
            where.setModelType(type);
            pluginThingsModels = pluginThingsModelMapper.queryModel(where);
            if (CollectionUtils.isEmpty(pluginThingsModels)) {
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "没有可同步的模型"));
            }
            // 后置埋点，加锁的标识
            id = -1L;
        } else {
            ThingsModel model = pluginThingsModelMapper.queryModelById(id);
            if (Objects.isNull(model)) {
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "该模型不存在"));
            }
            pluginThingsModels = Collections.singletonList(model);
        }
        final Long modelId = id;
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_saveThingsById_" + modelId, "更新模型");
        final long sysLoginUser = IdentityContext.getSysLoginUserId();
//        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_saveThingsById_" + modelId, DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            JSONObject result = currentProxy().onlineSaveThings(pluginThingsModels, sysLoginUser, asyncFlag);
            JSONArray successList = result.getJSONArray("success");
            JSONArray errorList = result.getJSONArray("error");
            int successNum = 0;
            if (!Objects.isNull(successList) && !successList.isEmpty()) {
                successNum = successList.size();
            }
            String message;
            if (pluginThingsModels.size() == 1 && 1 == successNum) {
                // 同步单个
                message = String.format("模型：[%s]同步完成", pluginThingsModels.get(0).getModelId());
                log.warn("模型同步完成");
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
                return;
            } else if (pluginThingsModels.size() == 1 && 0 == successNum) {
                message = String.format("模型:[%s]同步失败", pluginThingsModels.get(0).getModelId());
                log.warn("模型同步完成");
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
                return;
            } else {
                message = String.format("模型同步完成，成功：%s个", successNum);
            }
            if (!CollectionUtils.isEmpty(errorList)) {
                StringJoiner joiner = new StringJoiner("，", "[", "]");
                errorList.forEach(e -> joiner.add(e.toString()));
                message += "，以下模型同步失败：" + joiner;
            }
            log.info("模型同步完成");
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
        } catch (Exception e) {
            log.warn("模型同步失败，原因：", e);
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "基础模型同步失败！", null));
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型同步失败"));
        } finally {
//                taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_saveThingsById_" + modelId);
            log.warn("资源同步完成");
        }
//        }, EXECUTOR_SERVICE);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:saveThingsByScene:' + #uuid", message = "当前场景模型正在同步中，请稍后再试")
    @Override
    public void saveThingsByScene(Long uuid, Boolean asyncFlag) {
        List<SceneRecord> sceneRecords = sceneApi.getCurrentSceneRecords(uuid);
        if (CollectionUtils.isEmpty(sceneRecords)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "没有找到该场景的数据信息"));
        }
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_saveThingsByScene_" + uuid, "同步资源");

        final long sysLoginUser = IdentityContext.getSysLoginUserId();
//        AsyncUtils.asyncExecutorBean(() -> {
//             开始正式同步资源，先保存同步
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_saveThingsByScene_" + uuid, DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            // 同步本主场景下（子场景）的贴图
            List<TextureResource> allTextureResources = new ArrayList<>();
            SceneResourceParam param = new SceneResourceParam();
            for (SceneRecord scene : sceneRecords) {
                param.setSceneCode(scene.getSceneCode());
                allTextureResources.addAll(textureResourceMapper.queryAllByScene(getSceneDbPre(), param));
            }
            String message = "【" + sceneRecords.get(0).getName() + "】 资源同步完成";
            if (!CollectionUtils.isEmpty(allTextureResources)) {
                // 同步本场景或者子场景的贴图数据
                JSONObject result = currentProxy().doOnlineSyncTexture(allTextureResources, sysLoginUser, asyncFlag);
                JSONArray successList = result.getJSONArray("success");
                JSONArray errorList = result.getJSONArray("error");
                int successNum = (!Objects.isNull(successList) && !successList.isEmpty()) ? successList.size() : 0;
                message += String.format("。贴图同步成功：%s个", successNum);
                if (!CollectionUtils.isEmpty(errorList)) {
                    StringJoiner joiner = new StringJoiner("，", "[", "]");
                    errorList.forEach(e -> joiner.add(e.toString()));
                    message += "，以下贴图同步失败：" + joiner;
                }
            }
            // 根据主场景code找出这个主场景以及子场景依赖的项目模型
            List<String> sceneCodes = sceneRecords.stream().map(SceneRecord::getSceneCode).collect(Collectors.toList());
            List<ThingsModel> models = pluginThingsModelMapper.queryBySceneCodes(getSceneDbPre(), sceneCodes);
            if (!CollectionUtils.isEmpty(models)) {
                JSONObject result = currentProxy().onlineSaveThings(models, sysLoginUser, asyncFlag);
                JSONArray errorList = result.getJSONArray("error");
                JSONArray successList = result.getJSONArray("success");
                int successNum = (!Objects.isNull(successList) && !successList.isEmpty()) ? successList.size() : 0;
                message += String.format("。模型同步成功：%s个", successNum);
                if (!CollectionUtils.isEmpty(errorList)) {
                    StringJoiner joiner = new StringJoiner("，", "[", "]");
                    errorList.forEach(e -> joiner.add(e.toString()));
                    message += "，以下模型同步失败：" + joiner;
                }
            }
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
            log.info("资源同步完成");
        } catch (Exception e) {
            log.error("通过场景同步资源同步异常：", e);
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "资源同步失败！", null));
            throw new TextureResourceException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型同步失败"));
        }
//        }, EXECUTOR_SERVICE);
    }




    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject onlineSaveThings(List<ThingsModel> planSyncList, Long userId, boolean asyncFlag) {
        // planSyncList 是全量的模型列表，不区分是否存在
        // 获取minio里面已经存在的目录
        List<String> modelFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/", false).getNames();
        // 根据情况，确定要同步任务列表
        List<ThingsModel> result;
        // 同步成功的列表
        List<String> successList = new ArrayList<>();
        Map<String, Long> calculated = new HashMap<>(CollectionUtils.isEmpty(planSyncList) ? 16 : planSyncList.size());
        if (asyncFlag) {
            // 强制同步
            result = planSyncList.stream().filter(e -> StringUtils.isNotEmpty(e.getModelId())).collect(Collectors.toList());
        } else {
            // 已存在的就不再同步了
            Set<String> modelExistCollect = new HashSet<>(modelFilePath);
            // 由于选择的是同步不存在的模型，所以已经成功的也算在本次成功（需求！！！）
            result = new ArrayList<>();
            for (ThingsModel item : planSyncList) {
                if (StringUtils.isEmpty(item.getModelId())) {
                    // 该模型数据可能是目录
                    continue;
                }
                // 双重校验，只有数据库存在，且文件也存在才算都存在
                if (modelExistCollect.contains(TenantUtils.getTenantByRequest() +"/" + "model/" + item.getModelId() + "/") && (2 == item.getFileExist())) {
                    long fileSize;
                    fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + item.getModelId()).getSize();
                    calculated.put(item.getModelId(), fileSize);
                    successList.add(item.getModelId());
                } else {
                    result.add(item);
                }
            }
        }
        // 对于已经存在的文件，还需要重新计算一下大小
        if (!CollectionUtils.isEmpty(successList)) {
            pluginThingsModelMapper.updateFileSize(userId, calculated);
        }

        // 导入结果
        JSONObject outData = new JSONObject();
        Map<String, ThingsModel> modelMap = new HashMap<>(result.size());
        result.forEach(e -> modelMap.put(e.getModelId(), e));
        List<String> allDependentModel = new ArrayList<>(result.size());
        // 对传入的模型数据进行分类，粒子下载走专门的通道
        List<String> normal = new ArrayList<>(result.size());
        List<String> particle = new ArrayList<>(result.size());
        for (ThingsModel item : result) {
            if ("Particles".equals(item.getType())) {
                particle.add(item.getModelId());
            } else {
                normal.add(item.getModelId());
            }
            allDependentModel.add(item.getModelId());
        }
        List<CompletableFuture<List<String>>> allAsyncTask = new ArrayList<>();
        // 开始处理粒子
        if (!CollectionUtils.isEmpty(particle)) {
            List<List<String>> particleModels = asyncTask.taskSplit(particle);
            allAsyncTask.addAll(particleModels.stream().map(item -> AsyncUtils.asyncExecutorBean(() -> asyncTask.particlesModelByMomoda(ost, item), EXECUTOR_SERVICE)).toList());
        }
        // 开始处理正常的模型
        if (!CollectionUtils.isEmpty(normal)) {
            List<List<String>> placementModels = asyncTask.taskSplit(normal);
            allAsyncTask.addAll(placementModels.stream().map(item ->
                    AsyncUtils.asyncExecutorBean(() -> asyncTask.placementModelByUmodel(ost, item), EXECUTOR_SERVICE)).toList());
            // TODO 从模型库同步
            // String loginRespones = PlacementDownloadUtils.momodaLogin("0");
            // allAsyncTask.addAll(placementModels.stream().map(item -> CompletableFuture.supplyAsync(() -> asyncTask.placementModelByMomoda(fileApi, loginRespones, item, "0"), resourceAsyncTaskExecutor)).collect(Collectors.toList()));
        }
        List<List<String>> taskResult = allAsyncTask.stream().map(CompletableFuture::join).toList();
        List<String> uploadSuccessModel = new ArrayList<>();
        for (List<String> task : taskResult) {
            uploadSuccessModel.addAll(task);
        }
        // 经过分离后，发现要处理的粒子和普通模型都为空，则直接返回错误
        if (CollectionUtils.isEmpty(uploadSuccessModel)) {
            outData.put("success", successList);
            outData.put("error", result.stream().map(ThingsModel::getModelId).collect(Collectors.toList()));
            return outData;
        }

        // 已经存在的模型
        modelFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/", false).getNames();
        Set<String> modelExistCollect = new HashSet<>(modelFilePath);
        List<ThingsModel> pluginThingsModels = pluginThingsModelMapper.queryModel(null);
        Map<String, ThingsModel> exsitsModel = new HashMap<>();
        pluginThingsModels.forEach(e -> exsitsModel.put(e.getModelId(), e));
        List<ThingsModel> waitInsert = new ArrayList<>();
        List<ThingsModel> waitUpdate = new ArrayList<>();
        for (String modelId : uploadSuccessModel) {
            // 判断之前的模型是否存在，如果存在，需要把之前的模型文件删除,同时更新数据
            ThingsModel fromDbModel = exsitsModel.getOrDefault(modelId, null);
            ThingsModel model = modelMap.get(modelId);
            // 等待同步的模型
            if (Objects.isNull(model)) {
                continue;
            }
            boolean exist = modelExistCollect.contains(TenantUtils.getTenantByRequest() + "/model/" + modelId + "/");
            long fileSize = 0L;
            if (exist) {
                // 计算一下大小
                fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + modelId).getSize();
            }
            model.setFileExist(exist ? 2 : 1);
            model.setFileSize(exist ? fileSize : 0L);
            if (model.getModelType() == 2) {
                model.setClassify("");
            }
            if (Objects.nonNull(fromDbModel)) {
                model.setId(fromDbModel.getId());
                model.setCreateTime(fromDbModel.getCreateTime());
                model.setUpdateUser(userId);
                model.setCreateUser(userId);
                waitUpdate.add(model);
            } else {
                model.setUpdateUser(userId);
                model.setCreateUser(userId);
                waitInsert.add(model);
            }
            successList.add(modelId);
        }
        if (!CollectionUtils.isEmpty(waitInsert)) {
            pluginThingsModelMapper.insertForBatch(waitInsert);
        }
        if (!CollectionUtils.isEmpty(waitUpdate)) {
            pluginThingsModelMapper.updateForBatch(waitUpdate);
        }
        outData.put("success", successList);
        // 差集运算，得到处理失败的结果
        allDependentModel.removeAll(successList);
        outData.put("error", allDependentModel);
        return outData;
    }

    @Override
    public ClassifyTreeNode getClassifyTree(String classify, Integer modelType) {
        // AssertUtils.isTrue(modelType != null, HttpStatus.BAD_REQUEST, "modelType不能为空");
        // 1.根据模型类型筛选出基础模型和公共模型
        List<ThingsModel> pluginThingsModels = listModels(modelType, classify);
        List<JSONObject> stringPath = new ArrayList<>();
        for (ThingsModel pluginThingsModel : pluginThingsModels) {
            if (null != pluginThingsModel.getClassify()) {
                JSONObject obj = new JSONObject();
//                stringPath.add(pluginThingsModel.getClassify());
                obj.put("title", pluginThingsModel.getClassify());
                obj.put("type", pluginThingsModel.getModelType());
                stringPath.add(obj);
            }
        }
        try {
            List<ClassifyTreeNode> classifys = TreeUtils.stringPathConversionTreeWithType(stringPath);
            return new ClassifyTreeNode("/", "全部", classifys);
        } catch (Exception e) {
            log.error("在构建模型分类树结构数据时，发生异常：", e);
            return new ClassifyTreeNode("/", "全部", Collections.emptyList());
        }
    }

    /**
     * 查询3d-momoda模型树
     *
     * @return 分类树
     * <AUTHOR>
     * @date 2023/5/11 10:38
     */
    @Override
    public JSONObject get3DMomoDaTree() {
        List<IndustryVo> industries = pluginThingsModelMapper.quetyIndustrys();
        if (CollectionUtils.isEmpty(industries)) {
            return null;
        }
        for (IndustryVo item : industries) {
            if (StringUtils.isEmpty(item.getTitle()) && StringUtils.isEmpty(item.getPath())) {
                // 脏数据，原则：title 和 path 不能全为空
                continue;
            }
            if (StringUtils.isNotEmpty(item.getPath()) && StringUtils.isEmpty(item.getTitle())) {
                // 路径不为空，名称为空
                continue;
            }
            if (StringUtils.isEmpty(item.getPath()) && StringUtils.isNotEmpty(item.getTitle())) {
                // 路径为空，名称不为空
                item.setPath(item.getTitle());
                continue;
            }
            if (StringUtils.isNotEmpty(item.getPath()) && StringUtils.isNotEmpty(item.getTitle())) {
                // 路径不为空，名称不为空，为了避免/重复，在拼接时需要检查
                // 如果path最后一个字符为/，那么在拼接时不要/，反之亦然
                String lastSymbol = item.getPath().substring(item.getPath().length() - 1);
                if ("/".equals(lastSymbol)) {
                    item.setPath(item.getPath() + item.getTitle());
                } else {
                    item.setPath(item.getPath() + "/" + item.getTitle());
                }
            }
        }
        List<IndustryVo> filePathTree = IndustryTreeUtil.getFilePathTree(industries);
        JSONObject result = new JSONObject();
        result.put("industry", filePathTree);
        return result;
    }

    @Override
    public PageResult<ThingsModel> basicModelPage(ThingsModelParam param, boolean downloadUrl) {
        cacheRefresh.refresh();
        LambdaQueryWrapper<ThingsModel> queryWrapper = new LambdaQueryWrapper<>();
        // 为空的是目录节点
        queryWrapper.isNotNull(ThingsModel::getModelId);
        // 目录节点去除
        queryWrapper.eq(ThingsModel::getDirFlag, 1);
        if (Objects.nonNull(param)) {
            // 根据名称或者id进行模糊查询
            if (StringUtils.isNotEmpty(param.getTitle())) {
                queryWrapper.and(wrapper -> wrapper.like(ThingsModel::getModelId, param.getTitle())
                        .or().like(ThingsModel::getTitle, param.getTitle())
                        .or().like(ThingsModel::getTags, param.getTitle())
                );
            }
            // 根据分类进行检索
            if (StringUtils.isNotEmpty(param.getClassify())) {
                queryWrapper.like(ThingsModel::getClassify, param.getClassify());
            }
            // 根据模型type
            if (Objects.nonNull(param.getModelType())) {
                queryWrapper.eq(ThingsModel::getModelType, param.getModelType());
            }
            // 根据模型type
            if (StringUtils.isNotEmpty(param.getType())) {
                queryWrapper.like(ThingsModel::getType, param.getType());
            }
            // 根据模型tag
            if (StringUtils.isNotEmpty(param.getTags())) {
                queryWrapper.like(ThingsModel::getTags, param.getTags());
            }
            // 根据模型是否存在
            if (Objects.nonNull(param.getFileExist())) {
                queryWrapper.eq(ThingsModel::getFileExist, param.getFileExist());
            }
        }
        //用户自定义排序
        PageOrderUtils.wrapperOrder(queryWrapper, param.getSortField(), param.getSortRule());
        //根据访问时间倒序排列
        queryWrapper.orderByDesc(ThingsModel::getCreateTime).orderByDesc(ThingsModel::getId);
        Page<ThingsModel> page = this.page(PageFactory.defaultPage(), queryWrapper);
        // 包装分页实体，添加模型下载url
        return downloadUrl ? wrapThingsModel(page) : new PageResult<>(page);
    }

    /**
     * 包装分页
     *
     * @param page 原始分页
     * @return 分页结果
     * <AUTHOR>
     * @date 2023/2/17 11:29
     */
    private PageResult<ThingsModel> wrapThingsModel(Page<ThingsModel> page) {
        if (page != null && page.getRecords() != null) {
            ArrayList<ThingsModel> thingsModels = new ArrayList<>(page.getRecords().size());
            String type = "模型";
            String prefix = String.format("%s/%s/", TenantUtils.getTenantByRequest(), type);
            String filePath = TenantUtils.getTenantByRequest() + StringConstant.SLASH + type + StringConstant.SLASH;
            Set<String> modelNames = ost.listObjects(DownloadProperties.getTempPath(), filePath, false).getNames().stream().map(e -> PatternUtil.removeSuffix(PatternUtil.removePrefix(e,prefix))).collect(Collectors.toSet());
            page.getRecords().forEach(f -> {
                ThingsModelPageVo thingsModelPageVo = new ThingsModelPageVo();
                BeanUtils.copyProperties(f, thingsModelPageVo);
                thingsModelPageVo.setFileUrl(getModelPath(f.getModelId(), type,modelNames));
                thingsModels.add(thingsModelPageVo);
            });
            PageResult<ThingsModel> res = new PageResult<>();
            res.setRows(thingsModels);
            res.setTotalRows(Long.valueOf(page.getTotal()).intValue());
            res.setPageNo(Long.valueOf(page.getCurrent()).intValue());
            res.setPageSize(Long.valueOf(page.getSize()).intValue());
            res.setTotalPage(Long.valueOf(page.getPages()).intValue());
            res.setRainbow(PageResult.rainbow(Long.valueOf(page.getCurrent()).intValue(),
                    Long.valueOf(res.getTotalPage()).intValue(), PageResult.RAINBOW_NUM));
            return res;
        } else {
            return new PageResult<>(PageFactory.defaultPage());
        }
    }

    @Override
    public PageResult<ThingsModel> projectModelPage(ThingsModelParam param) {
        Page<ThingsModel> pageParam = PageFactory.defaultPage();
        param.setPageFrom(((pageParam.getCurrent() - 1) * pageParam.getSize()));
        param.setPageSize((int) pageParam.getSize());
        Long count = pluginThingsModelMapper.selectProjectModelCount(getSceneDbPre(), param);
        List<ThingsModel> models = pluginThingsModelMapper.selectProjectModelPage(getSceneDbPre(), param);
        pageParam.setTotal(count);
        pageParam.setRecords(models);
        return new PageResult<>(pageParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteThingsModelOrTexture(List<SceneModelParam> params) {
        for (SceneModelParam param : params) {
            if("模型".equals(param.getType())) {
                // 首先校验在中间关联表中是否存在
                ThingsModel fromDbModel = pluginThingsModelMapper.queryModelByModelId(param.getModelId(), null);
                if (Objects.isNull(fromDbModel)) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该模型不存在"));
                }
                int count = sceneProjectModelMapper.queryCountByModelId(getSceneDbPre(), param.getModelId());
                if(count > 1) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该模型被其他场景关联，无法删除"));
                }
                pluginThingsModelMapper.deleteProjectModel(param.getModelId());
                ost.removeObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/"+ ModelConfigProperties.getModelPath() + "/" + param.getModelId());
            }else if("贴图".equals(param.getType())) {
//                SceneTexture sceneTexture = textureResourceMapper.queryByTextureId(getSceneDbPre(), param.getModelId());
//                if(Objects.isNull(sceneTexture)) {
//                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该贴图不存在"));
//                }
                TextureResource textureResource = textureResourceMapper.queryOneById(param.getModelId());
                if(Objects.isNull(textureResource)) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该贴图不存在"));
                }
                int count = textureResourceMapper.queryCountByTextureId(getSceneDbPre(), param.getModelId());
                if(count > 1) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该贴图被其他场景关联，无法删除"));
                }
                textureResourceMapper.deleteByTextureId(param.getModelId());
                ost.removeObject(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/"+ ModelConfigProperties.getTexturePath() + "/" + param.getModelId() + "." + textureResource.getExtendName());
            }else {
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该类型不存在"));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject offlineUploadModeByClassify(MultipartFile multipartFile, String classify) {
        final long userId = IdentityContext.getSysLoginUserId();
        // 校验上传的模型库文件不能为空
        if (Objects.isNull(multipartFile)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传的基础模型包变量 file 不能为空"));
        }
        if (StringUtils.isBlank(classify)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该分类不存在"));
        }
        ThingsModel model = new ThingsModel();
        model.setClassify(classify);
        return currentProxy().doOfflineUploadBasicModel(multipartFile, null, model, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject offlineUploadBasicModel(MultipartFile multipartFile, String modelId) {
        final long sysLoginUser = IdentityContext.getSysLoginUserId();
        try {
            if (!StringUtils.isEmpty(modelId)) {
                return currentProxy().doOfflineUploadBasicModel(multipartFile, modelId, null, sysLoginUser);
            }
            currentProxy().doOfflineUploadBasicModel(multipartFile, modelId, null, sysLoginUser);
            // TODO 异步多线程下 可能取不到stream 或者stream已被关闭
            // taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_offlineUploadBasicMode_-1");
            // AsyncUtils.asyncExecutorBean(() -> {
            //     // 开始正式同步资源，先保存同步
            //     taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_offlineUploadBasicMode_-1", DateUtils.getTime(), TaskCache.EXPIRED, TimeUnit.HOURS);
            //     try {
            //         currentProxy().doOfflineUploadBasicModel(fileParam, modelId, null, sysLoginUser.getId());
            //         log.warn("离线同步基础模型完成");
            //         realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), "离线同步基础模型成功！", null));
            //     } catch (Exception e) {
            //         log.error("离线同步基础模型失败：", e);
            //         realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), "离线同步基础模型失败！", null));
            //         throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型同步失败"));
            //     } finally {
            //         taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_offlineUploadBasicMode_-1");
            //     }
            // }, EXECUTOR_SERVICE);
        } catch (Exception e) {
            log.error("获取文件流异常：", e);
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传失败"));
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject doOfflineUploadBasicModel(MultipartFile multipartFile, String modelId, ThingsModel model, long userId) {
        // 校验上传的模型库文件不能为空
        if (Objects.isNull(multipartFile)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传的基础模型包变量 file 不能为空"));
        }
        List<String> modelIds = new ArrayList<>();
        Map<String, String> modelMap = new HashMap<>();
        JSONObject outData = new JSONObject();
        // 将上传的文件解压至临时目录中
        String tempFileName = IdUtils.fastUuid();
        // zip 包文件路径
        final String bundleLibTempPath = ProximabPathUtil.getTempPath() + File.separator + tempFileName;
        final File file = new File(bundleLibTempPath);
        if (!file.exists()) {
            boolean flag = file.mkdirs();
            log.warn("开始创建目录：{}，创建结果：{}", file.getAbsoluteFile(), flag);
        }
        String filePath = "";
        try {
            ZipUtils.unzip(multipartFile.getInputStream(), file);
            File mapping = new File(bundleLibTempPath + File.separator + "mapping.json");
            File projectLib = new File(bundleLibTempPath + File.separator + "product_lib.json");
            // 如果这俩文件都不存在
            if (!mapping.exists() && !projectLib.exists()) {
                // 则循环将文件移动到bundleLib/bundleLib下
                String path = file.getPath();
                BundleLibUtil.copeModelFile(path + File.separator + "resource",path + File.separator + "bundleLib/bundleLib", modelIds);
                filePath = path + File.separator + "bundleLib";
            } else {
                // 获取最新模型包的路径地址
                filePath = BundleLibUtil.parseToBundleLibZip(file);
                String productJSON = file.getAbsolutePath() + File.separator + "product_lib.json";
                File jsonFile = new File(productJSON);
                if (jsonFile.exists()) {
                    // 解析JSON数据
                    String modelJson = MinioUtil.readJSONFile(jsonFile, false);
                    JSONObject jsonObject = JSONObject.parseObject(modelJson);
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String modelTitle = entry.getKey();
                        String modelInfo = entry.getValue().toString();
                        JSONObject info = JSONObject.parseObject(modelInfo);
                        modelMap.put(info.getString("id"), modelTitle);
                    }
                }
                // 得到模型id文件
                modelIds = SyncModelKit.getDirectoryUnderModelId(filePath, true, null);
                if (CollectionUtils.isEmpty(modelIds)) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传模型包失败，原因：1、所上传的包中无有效模型"));
                }
            }
            if (StringUtils.isNotBlank(modelId)){
                modelIds.add(modelId);
            }
            //  ZipUtil.unzip(multipartFile.getInputStream(), file, StandardCharsets.UTF_8);
//            if (!BundleLibUtil.assertBundleLib(file, multipartFile.getOriginalFilename())) {
//                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "请上传正确的模型包"));
//            }
//            // 获取最新模型包的路径地址
//            filePath = BundleLibUtil.parseToBundleLibZip(file);
//            if (StringUtils.isEmpty(filePath)) {
//                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "请上传正确的模型包"));
//            }
//            // 得到模型id文件
//            List<String> modelIds = SyncModelKit.getDirectoryUnderModelId(filePath, true, modelId);
//            if (CollectionUtils.isEmpty(modelIds)) {
//                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.OK, "上传模型包失败，原因：1、所上传的包中无物模型，2、所上传的包中没有指定的待上传模型"));
//            }
            // 开启异步上传
            List<List<String>> particleModels = asyncTask.taskSplit(modelIds);
            String finalFilePath = filePath;
            final String tenant = TenantUtils.getTenantByRequest();
            List<CompletableFuture<Void>> tasks = particleModels.stream().map(item -> CompletableFuture.runAsync(() -> asyncTask.uploadLocalModel(ost,tenant, file.getAbsolutePath(), finalFilePath, item), EXECUTOR_SERVICE)).collect(Collectors.toList());
//            // 读取包的简介内容
//            String productJSON = file.getAbsolutePath() + File.separator + "product_lib.json";
//            File jsonFile = new File(productJSON);
//            if (jsonFile.exists()) {
//                // 解析JSON数据
//                String modelJson = MinioUtil.readJSONFile(jsonFile, false);
//                JSONObject jsonObject = JSONObject.parseObject(modelJson);
//                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
//                    String modelInfo = entry.getValue().toString();
//                    JSONObject info = JSONObject.parseObject(modelInfo);
//                    String modelTitle = entry.getKey();
//                    modelMap.put(info.getString("id"), modelTitle);
//                }
//            }
            // 所有的以及存在的模型
            List<ThingsModel> pluginThingsModels = pluginThingsModelMapper.queryModel(null);
            Map<String, ThingsModel> exsitsModel = new HashMap<>();
            pluginThingsModels.forEach(e -> exsitsModel.put(e.getModelId(), e));
            // 等待上传任务执行完毕
            tasks.forEach(CompletableFuture::join);
            // 获取minio中的目录，判断上传结果
            List<String> modelFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/", false).getNames();
            Set<String> modelExistCollect = new HashSet<>(modelFilePath);
            // 用于保存执行结果
            List<String> successList = new ArrayList<>();
            List<String> errorList = new ArrayList<>();
            List<ThingsModel> waitInsert = new ArrayList<>();
            List<ThingsModel> waitUpdate = new ArrayList<>();
            for (String things : modelIds) {
                // 判断之前的模型是否存在，如果存在，需要把之前的模型文件删除,同时更新数据,上传到指定分类
                ThingsModel fromDbModel = exsitsModel.getOrDefault(things, null);
                // 如果是传入了基础模型，在没有同步模型分类的情况下，使用用户传入的model填充模型分类的信息
                if (Objects.nonNull(model) && Objects.isNull(fromDbModel)) {
                    fromDbModel = new ThingsModel();
                    long fileSize = 0L;
                    boolean exist = modelExistCollect.contains(TenantUtils.getTenantByRequest() +"/" + "model/"+ things + "/");
                    if (exist) {
                        // 计算一下大小
                        fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/"+ ModelConfigProperties.getModelPath() + "/" + things).getSize();
                    }
                    String classify = model.getClassify();
                    String tags = classify != null && classify.split("/").length == 2 ? "[\"" + classify.substring(classify.lastIndexOf("/") + 1) + "\"]" : "[\"无\"]";
                    fromDbModel.setFileSize(exist ? fileSize : 0L);
                    fromDbModel.setFileExist(exist ? 2 : 1);
                    fromDbModel.setClassify(classify);
                    fromDbModel.setModelType(1);
                    fromDbModel.setType("Placement");
                    fromDbModel.setModelId(things);
                    fromDbModel.setCreateTime(new Date());
                    fromDbModel.setTitle(modelMap.get(things) != null ? modelMap.get(things) : "");
                    fromDbModel.setCreateUser(userId);
                    fromDbModel.setTags(tags);
                    successList.add(things);
                    waitInsert.add(fromDbModel);
                    continue;
                }
                // 如果是基础模型，在已经同步模型分类的情况下，直接更新数据
                if (Objects.nonNull(fromDbModel)) {
                    // 之前模型已经存在
                    fromDbModel.setUpdateTime(new Date());
                    boolean exist = modelExistCollect.contains(TenantUtils.getTenantByRequest() +"/" + "model/" + fromDbModel.getModelId() + "/");
                    long fileSize = 0L;
                    if (exist) {
                        fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + modelId).getSize();
                    }
                    fromDbModel.setFileSize(exist ? fileSize : 0L);
                    fromDbModel.setFileExist(exist ? 2 : 1);
                    //fromDbModel.setModelType(1);
                    fromDbModel.setUpdateUser(userId);
                    waitUpdate.add(fromDbModel);
                    if (Objects.nonNull(model) && !(fromDbModel.getClassify()).equals(model.getClassify())) {
                        errorList.add(things + "导入失败，已存在于分类：" + model.getClassify());
                    } else {
                        successList.add(things);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(waitInsert)) {
                pluginThingsModelMapper.insertForBatch(waitInsert);
            }
            if (!CollectionUtils.isEmpty(waitUpdate)) {
                pluginThingsModelMapper.updateForBatch(waitUpdate);
            }
            // 上传贴图
            SyncModelKit.uploadProjectTexture(ost, filePath);
            outData.put("success", successList);
            outData.put("error", errorList);
            return outData;
        } catch (ThingsModelException e) {
            log.error("离线上传模型失败：",e);
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, e.getErrorMessage()));
        } catch (Exception e) {
            log.error("离线上传模型异常：",e);
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "请上传正确的模型包"));
        } finally {
            // 上传完成之后,删除临时目录
            if (!FileUtils.deleteQuietly(file)) {
                log.error("在删除临时目录：{}时，发生异常", file.getAbsolutePath());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject doOfflineUploadProjectModel(MultipartFile multipartFile, String sceneCode, boolean overrideFlag, boolean downloadFlag, String classify) {
        AssertUtils.isTrue(StringUtils.isNotEmpty(classify), HttpStatus.BAD_REQUEST, "项目模型分类参数classify不能为空");
        final long userId = IdentityContext.getSysLoginUserId();
        // 将上传的文件解压至临时目录中
        final String bundleLibTempPath = ProximabPathUtil.getTempPath() + File.separator + IdUtils.fastUuid();
        final File file = new File(bundleLibTempPath);
        List<String> modelIds = new ArrayList<>();
        Map<String, String> modelMap = new HashMap<>();
        boolean flagbol = false;
        if (!file.exists()) {
            boolean flag = file.mkdirs();
            log.warn("开始创建目录：{}，创建结果：{}", file.getAbsoluteFile(), flag);
        }
        JSONObject outData = new JSONObject();
        try {
            ZipUtils.unzip(multipartFile.getInputStream(), file);
//            if (!BundleLibUtil.assertBundleLib(file, multipartFile.getOriginalFilename())) {
//                // 如果上传的资源包没有BundleLib.json和product_lib.json没有，则认为是贴图资源，不走模型上传
//                outData.put("success", currentProxy().offlineUploadTexture(userId, file.getAbsolutePath()));
//                outData.put("error", Collections.EMPTY_LIST);
//                return outData;
//            }
            String filePath = "";
            File mapping = new File(bundleLibTempPath,"mapping.json");
            File projectLib = new File(bundleLibTempPath , "product_lib.json");
            File _modelsPath = new File(bundleLibTempPath , "model");
            // 如果这俩文件都不存在，则按照底座导出的模型格式进行解析
            if (!mapping.exists() && !projectLib.exists() && _modelsPath.exists()) {
                File[] files = _modelsPath.listFiles();
                if (Objects.isNull(files) || files.length <= 0) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传模型包失败，原因：1、所上传的包中无有效模型"));
                }
                for (File modelDir : files) {
                    if (modelDir.isFile()){
                        continue;
                    }
                    File jsonFile = new File(modelDir, "0" + File.separator + "gltf" + File.separator + "bundle.json");
                    String modelJson = MinioUtil.readJSONFile(jsonFile, false);
                    JSONObject info = JSONObject.parseObject(modelJson);
                    modelMap.put(modelDir.getName(), info.getString("name"));
                    modelIds.add(modelDir.getName());
                }
                filePath = _modelsPath.getAbsolutePath();
            } else {
                flagbol = true;
                // 获取最新模型包的路径地址
                filePath = BundleLibUtil.parseToBundleLibZip(file);
                String productJSON = file.getAbsolutePath() + File.separator + "product_lib.json";
                File jsonFile = new File(productJSON);
                if (jsonFile.exists()) {
                    // 解析JSON数据
                    String modelJson = MinioUtil.readJSONFile(jsonFile, false);
                    JSONObject jsonObject = JSONObject.parseObject(modelJson);
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        String modelTitle = entry.getKey();
                        String modelInfo = entry.getValue().toString();
                        JSONObject info = JSONObject.parseObject(modelInfo);
                        modelMap.put(info.getString("id"), modelTitle);
                    }
                }
                // 得到模型id文件
                modelIds = SyncModelKit.getDirectoryUnderModelId(filePath, true, null);
                if (CollectionUtils.isEmpty(modelIds)) {
                    throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传模型包失败，原因：1、所上传的包中无有效模型"));
                }
            }
            if (StringUtils.isEmpty(filePath)) {
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "请上传正确的模型包"));
            }

            List<String> successList = new ArrayList<>();
            List<String> errorList = new ArrayList<>();
            for (String things : modelIds) {
                try {
                    // 检查这个模型是否已经存在（不关联项目）
                    ThingsModel fromDbModel = pluginThingsModelMapper.queryModelByModelId(things, 2);
                    if (Objects.isNull(fromDbModel)) {
                        // 没有过上传（非uModel资源跳过）
                        SyncModelKit.uploadProjectModel(ost, file.getAbsolutePath(), filePath, things, flagbol);
                        // 之前的模型不存在，则创建一条新的数据到数据库里面
                        ThingsModel model = new ThingsModel();
                        model.setClassify(classify);
                        model.setModelType(2);
                        model.setFileExist(2);
                        model.setModelId(things);
                        model.setCreateTime(new Date());
                        model.setTitle(modelMap.get(things) != null ? modelMap.get(things) : "");
                        model.setCreateUser(userId);
                        long fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + things).getSize();
                        model.setFileExist(fileSize > 0 ? 2 : 1);
                        model.setFileSize(fileSize);
                        // 模型表插入一条记录
                        pluginThingsModelMapper.insertModel(model);
                    } else {
                        boolean exist = fromDbModel.getFileExist() == 2 && !overrideFlag;
                        if (exist) {
                            throw new ThingsModelException(HttpStatus.BAD_REQUEST, "该项目模型已经存在于其他分类下面");
                        }
                        // 文件上次上传失败 或者 强烈需要进行覆盖
                        boolean permission = (1 == fromDbModel.getFileExist()) || (2 == fromDbModel.getFileExist() && overrideFlag);
                        if (permission) {
                            SyncModelKit.uploadProjectModel(ost, file.getAbsolutePath(), filePath, things, flagbol);
                            // 之前模型已经存在
                            fromDbModel.setUpdateTime(new Date());
                            fromDbModel.setUpdateUser(userId);
                            long fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + things).getSize();
                            fromDbModel.setFileExist(fileSize > 0 ? 2 : 1);
                            fromDbModel.setFileSize(fileSize);
                            fromDbModel.setClassify(classify);
                            pluginThingsModelMapper.updateById(fromDbModel);
                        }
                    }
                    successList.add(things);
                    // 如果没有传入场景的code，则表示该模型不需要关联到场景
                    if (StringUtils.isEmpty(sceneCode)) {
                        continue;
                    }
                    // 判断这个模型是否关联了这个场景
                    ThingsModel projectJoinModel = pluginThingsModelMapper.selectOneByProjectIdAndModelId(getSceneDbPre(), sceneCode, things, 2);
                    if (Objects.isNull(projectJoinModel)) {
                        SceneModel sceneModel = new SceneModel();
                        sceneModel.setSceneCode(sceneCode);
                        sceneModel.setModelId(things);
                        sceneModel.setCreateUser(userId);
                        // 中间关联的表加入一条记录
                        sceneProjectModelMapper.add(getSceneDbPre(), sceneModel);
                    }
                } catch (Exception e) {
                    errorList.add(things);
                    log.error("在同步项目模型：{}时，发生异常，根本原因：{}", things, e);
                }
            }
            // 上传贴图
            SyncModelKit.uploadProjectTexture(ost, filePath);
            if (downloadFlag) {
                // 重新压缩模型包文件
                ZipUtil.zip(filePath);
                filePath += ".zip";
                // 下载模型包
                File downloadZip = new File(filePath);
                DownloadUtils.download(downloadZip.getName(), new FileInputStream(downloadZip), HttpServletUtils.getResponse());
            }
            outData.put("success", successList);
            outData.put("error", errorList);
            return outData;
        } catch (ThingsModelException e) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, e.getErrorMessage()));
        } catch (Exception e) {
            throw new ThingsModelException(HttpStatus.BAD_REQUEST, "请上传正确的模型包", e);
        } finally {
            // 上传完成之后,删除临时目录
            try {
                FileUtils.deleteQuietly(file);
            } catch (IORuntimeException e) {
                log.error("在删除临时目录：{}时，发生异常：{}", file.getAbsolutePath(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> offlineUploadTexture(Long userId, String path) {
        // 这里的path里面已经是一个个贴图的列表了
        List<String> listFiles = FileViewer.getListFiles(path, "", true);
        if (!CollectionUtils.isEmpty(listFiles)) {
            Map<String, String> textureFileMap = FileViewer.getCurrentPathFileNames(path);
            if (CollectionUtils.isEmpty(textureFileMap)) {
                return Collections.emptyList();
            }
            // 传入到minio贴图路径
            ost.uploadObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() + "/", listFiles);
            sceneApi.saveSceneTextureJson(userId, textureFileMap);
            return new ArrayList<>(textureFileMap.keySet());
        }
        return Collections.emptyList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean offlineUploadTexture(MultipartFile file, String textureId) {
        try {
            TextureResource texture = Optional.ofNullable(textureResourceMapper.queryOneById(textureId))
                    .orElseThrow(() -> new TextureResourceException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "贴图数据不存在")));
            String fileName = file.getOriginalFilename();
            ost.putObject(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() + "/" + fileName, file.getBytes(), file.getSize(), file.getContentType());
            long fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest()+ "/" + ModelConfigProperties.getTexturePath() + "/" + fileName).getSize();
            texture.setFileExist(fileSize > 0 ? 2 : 1);
            texture.setFileSize(fileSize);
            textureResourceMapper.insert(texture);
            return true;
        } catch (Exception e) {
            log.error("上传贴图({})失败，原因：{}", textureId, e);
            throw new TextureResourceException(ExpEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "贴图上传错误"));
        }
    }

    /**
     * 获取模型index.json数据信息
     *
     * @param modelId 模型id
     * @return 模型属性
     */
    @Override
    public JSONObject getModelInfo(String modelId) {
        ThingsModel fromDbModel = pluginThingsModelMapper.queryModelByModelId(modelId, null);
        if (Objects.isNull(fromDbModel)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "该模型不存在"));
        }
        String modelPath = TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/" + modelId + "/0/";
        if ("Particles".equals(fromDbModel.getType())) {
            modelPath += "particles/";
        } else {
            modelPath += "gltf/";
        }
        JSONObject result = MinioUtil.readModelConfigJSON(ModelConfigProperties.getRootBucket(), modelPath + "/index.json", modelId, ost);
        if (Objects.isNull(result)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "获取模型JSON数据异常"));
        }
        result.put("type", fromDbModel.getType());
        result.put("title", fromDbModel.getTitle());
        result.put("tags", fromDbModel.getTags());
        result.put("classify", fromDbModel.getClassify());
        result.put("viewUrl", modelPath);
        result.put("createTime", Objects.isNull(fromDbModel.getCreateTime()) ? null : DateFormatUtils.format(fromDbModel.getCreateTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        result.put("updateTime", Objects.isNull(fromDbModel.getUpdateTime()) ? null : DateFormatUtils.format(fromDbModel.getUpdateTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        result.put("version", fromDbModel.getVersion());
        return result;
    }

    @Override
    public List<ClassifyTreeNode> getClassifyParallelTree(String classify) {
        List<String> stringPath = pluginThingsModelMapper.queryClassifyPathList(classify);
        List<ClassifyTreeNode> treeNodes;
        try {
            treeNodes = TreeUtils.stringPathConversionTree(stringPath);
        } catch (Exception e) {
            treeNodes = new ArrayList<>();
            log.error("在构建模型分类树结构数据时，发生异常：", e);
        }
        return treeNodes;
    }

    @Override
    public List<ThingsModelVo> getList(ThingsModelParam param) {
        LambdaQueryWrapper<ThingsModel> queryWrapper = new LambdaQueryWrapper<>();
        // 为空的是目录节点
        queryWrapper.isNotNull(ThingsModel::getModelId);
        queryWrapper.eq(ThingsModel::getDirFlag, 1);
        // 根据分类进行检索
        if (null != param.getClassify() && ("项目模型/模型列表".equals(param.getClassify()) || "项目模型/".equals(param.getClassify()))) {
            queryWrapper.and(wrapper -> wrapper.isNull(ThingsModel::getClassify)
                    .or().like(ThingsModel::getClassify,param.getClassify())
            );
        } else {
            queryWrapper.like(StringUtils.isNotEmpty(param.getClassify()), ThingsModel::getClassify, param.getClassify());
        }
        // 根据名称或者id进行模糊查询
        queryWrapper.and(StringUtils.isNotEmpty(param.getTitle()), wrapper -> wrapper.like(ThingsModel::getModelId, param.getTitle())
                .or().like(ThingsModel::getTitle, param.getTitle())
                .or().like(ThingsModel::getTags, param.getTitle())
        );
        // 根据类别进行检索
        queryWrapper.eq(Objects.nonNull(param.getModelType()), ThingsModel::getModelType, param.getModelType());

        return list(queryWrapper).stream().map(e -> BeanUtils.copyToBean(e, ThingsModelVo.class)).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:downloadModel:' + #models", message = "当前模型正在下载中，请稍后再试")
    @Override
    public void downloadModel(List<String> models) {
        // 取出全量的数据，然后判断资源的类型
        SceneResourceParam param = new SceneResourceParam();
        param.setFileExist(2);
        Long count = textureResourceMapper.queryResourceCount(param);
        List<SceneResourceVo> resources;
        if (count.compareTo(0L) > 0) {
            param.setPageFrom(0L);
            param.setPageSize(count.intValue());
            resources = textureResourceMapper.queryResourcePage(param);
        } else {
            throw new TextureResourceException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "系统没有可下载的资源"));
        }
        // 存放计划下载的资源
        List<String> planDownloadModel = new ArrayList<>(64);
        List<String> planDownloadTexture = new ArrayList<>(128);
        if (CollectionUtils.isEmpty(models)) {
            // 下载全部
            Map<String, List<SceneResourceVo>> allTypeResource = resources.stream().filter(e -> StringUtils.isNotEmpty(e.getType())).collect(Collectors.groupingBy(SceneResourceVo::getType, Collectors.toList()));
            List<SceneResourceVo> existModels = allTypeResource.get("模型");
            if (!CollectionUtils.isEmpty(existModels)) {
                planDownloadModel = existModels.stream().map(SceneResourceVo::getModelId).collect(Collectors.toList());
            }
            List<SceneResourceVo> existTextures = allTypeResource.get("贴图");
            if (!CollectionUtils.isEmpty(existTextures)) {
                planDownloadTexture = existTextures.stream().map(SceneResourceVo::getModelId).collect(Collectors.toList());
            }
        } else {
            Map<String, String> resourceMap = new HashMap<>(Math.toIntExact(count));
            // 将资源的modelId，type放到map里面
            resources.stream().filter(e -> StringUtils.isNotEmpty(e.getType())).forEach(e -> resourceMap.put(e.getModelId(), e.getType()));
            // 按需下载
            for (String model : models) {
                String type = resourceMap.get(model);
                if (StringUtils.isEmpty(type)) {
                    // 未取到说明按基础模型处理
                    planDownloadModel.add(model);
                    continue;
                }
                if ("模型".equals(type)) {
                    planDownloadModel.add(model);
                }
                if ("贴图".equals(type)) {
                    planDownloadTexture.add(model);
                }
            }
        }

        final long sysLoginUser = IdentityContext.getSysLoginUserId();
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser, "下载资源包");
        List<String> finalPlanDownloadModel = planDownloadModel;
        List<String> finalPlanDownloadTexture = planDownloadTexture;
//        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser, DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            currentProxy().doDownloadModel(sysLoginUser, null, finalPlanDownloadModel, finalPlanDownloadTexture);
            log.warn("资源包下载成功");
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "资源包下载失败，请稍后再试！", null));
            log.warn("资源包下载失败，原因：", e);
            throw new TextureResourceException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "资源包下载失败"));
        } finally {
//                taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser);
            log.warn("资源包下载完成");
        }
//        }, EXECUTOR_SERVICE);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:hardDownload:' + #models", message = "当前模型正在下载中，请稍后再试")
    @Override
    public void hardDownload(List<String> models) {
        if (CollectionUtils.isEmpty(models)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "缺少模型集合参数"));
        }
        final long sysLoginUser = IdentityContext.getSysLoginUserId();
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser, "下载模型");
        // 下载成功的列表（也就是minio中有的文件）
        List<String> successList = new ArrayList<>();
        // 先在本地的minio里面取出已经上传的模型
        // 获取minio里面已经存在的模型
        List<String> modelFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/", false).getNames();
        //  存放还没有同步的资源
        List<String> unExistModel;
        if (CollectionUtils.isEmpty(modelFilePath)) {
            // 整体未同步过
            unExistModel = models;
        } else {
            Set<String> modelExistCollect = new HashSet<>(modelFilePath);
            unExistModel = new ArrayList<>(models.size());
            for (String item : models) {
                if (modelExistCollect.contains(TenantUtils.getTenantByRequest() + "/" + "model/"+ item + "/")) {
                    successList.add(item);
                } else {
                    unExistModel.add(item);
                }
            }
        }
        if (!CollectionUtils.isEmpty(unExistModel)) {
            List<String> asyncSuccessModel = new ArrayList<>();
            try {
                List<List<String>> placementModels = asyncTask.taskSplit(unExistModel);
                List<CompletableFuture<List<String>>> allAsyncTask = placementModels.stream().map(item -> AsyncUtils.asyncExecutorBean(() -> asyncTask.placementModelByUmodel(ost, item), EXECUTOR_SERVICE)).collect(Collectors.toList());
                List<List<String>> taskResult = allAsyncTask.stream().map(CompletableFuture::join).toList();
                for (List<String> task : taskResult) {
                    asyncSuccessModel.addAll(task);
                }
            } catch (Exception e) {
                log.error("硬性下载模型失败：", e);
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型下载失败"));
            }
            if (!CollectionUtils.isEmpty(asyncSuccessModel)) {
                successList.addAll(asyncSuccessModel);
                List<ThingsModel> pluginThingsModels = pluginThingsModelMapper.queryModel(null);
                Map<String, ThingsModel> exsitsModel = new HashMap<>();
                pluginThingsModels.forEach(e -> exsitsModel.put(e.getModelId(), e));
                List<ThingsModel> waitInsert = new ArrayList<>();
                List<ThingsModel> waitUpdate = new ArrayList<>();
                // 并把这些数据放到表数据里面
                // 同步后将其保存到数据中
                for (String modelId : asyncSuccessModel) {
                    ThingsModel fromDbModel = exsitsModel.getOrDefault(modelId, null);
                    long fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getModelPath() + "/"+ modelId).getSize();
                    if (Objects.isNull(fromDbModel)) {
                        // 之前的模型不存在，则创建一条新的数据到数据库里面
                        ThingsModel model = new ThingsModel();
                        model.setClassify(null);
                        model.setModelType(2);
                        model.setFileExist(2);
                        model.setModelId(modelId);
                        // TODO 在通过umodel进行下载时，模型的名称是拿不到的
                        model.setTitle(null);
                        model.setCreateTime(new Date());
                        model.setFileSize(fileSize);
                        waitInsert.add(model);
                    } else {
                        // 之前模型已经存在
                        fromDbModel.setUpdateTime(new Date());
                        fromDbModel.setFileExist(2);
                        fromDbModel.setFileSize(fileSize);
                        waitUpdate.add(fromDbModel);
                    }
                }
                if (!CollectionUtils.isEmpty(waitInsert)) {
                    pluginThingsModelMapper.insertForBatch(waitInsert);
                }
                if (!CollectionUtils.isEmpty(waitUpdate)) {
                    pluginThingsModelMapper.updateForBatch(waitUpdate);
                }
            }
        }
        if (CollectionUtils.isEmpty(successList)) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "模型资源下载失败！", null));
            return;
        }
        // 计算下载失败的模型
        models.removeAll(successList);
        String message = String.format("资源下载完成，成功：%s个", successList.size());
        if (!models.isEmpty()) {
            StringJoiner joiner = new StringJoiner("，", "[", "]");
            models.forEach(joiner::add);
            message += "，以下模型下载失败：" + joiner;
        }
        final String diyMessage = message + "。";
        // 执行下载
//        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser, DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            currentProxy().doDownloadModel(sysLoginUser, diyMessage, successList, null);
            log.info("模型文件下载成功");
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "模型资源导出失败，请稍后再试！", null));
            log.warn("模型文件下载失败，原因：", e);
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型导出失败"));
        } finally {
//                taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUser);
            log.warn("模型文件下载完成");
        }
//        }, EXECUTOR_SERVICE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doDownloadModel(Long userId, String diyMessage, List<String> planDownloadModel, List<String> planDownloadTexture) {
        LocalDate expiredDate = LocalDate.now().plusDays(DownloadProperties.getFileExpiredDays());
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String expiredDay = formatter1.format(expiredDate);

        // 临时导出目录
        String tempRootPath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        String tempPath = tempRootPath + PlacementDownloadUtils.SEP + "temp";
        String zipPath = tempRootPath + PlacementDownloadUtils.SEP + "zip";
        final File tempRootDir = new File(tempRootPath);
        final File tempDir = new File(tempPath);
        final File zipDir = new File(zipPath);
        try {
            FileUtils.forceMkdir(tempRootDir);
            FileUtils.forceMkdir(tempDir);
            FileUtils.forceMkdir(zipDir);
        } catch (IOException e) {
            if (!tempRootDir.exists()) {
                try {
                    FileUtils.forceMkdir(tempRootDir);
                } catch (IOException ex) {
                    throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + tempRootPath, ex);
                }
            }
            if (!tempDir.exists()) {
                try {
                    FileUtils.forceMkdir(tempDir);
                } catch (IOException ex) {
                    throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + tempPath, ex);
                }
            }
            if (!zipDir.exists()) {
                try {
                    FileUtils.forceMkdir(zipDir);
                } catch (IOException ex) {
                    throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + zipPath, ex);
                }
            }
        }
        String zipFileName;
        if (!CollectionUtils.isEmpty(planDownloadModel) && !CollectionUtils.isEmpty(planDownloadTexture)) {
            zipFileName = "模型及贴图资源_" + DateUtils.nowDateFormatNoSeparator();
        } else if (!CollectionUtils.isEmpty(planDownloadModel)) {
            zipFileName = "模型资源_" + DateUtils.nowDateFormatNoSeparator();
        } else if (!CollectionUtils.isEmpty(planDownloadTexture)) {
            zipFileName = "贴图资源_" + DateUtils.nowDateFormatNoSeparator();
        } else {
            zipFileName = "导出资源";
        }
        List<CompletableFuture<Boolean>> tasks = new ArrayList<>();
        if (!CollectionUtils.isEmpty(planDownloadModel)) {
            // 对下载模型的文件进行任务切分
            List<List<String>> particleModels = asyncTask.taskSplit(planDownloadModel);
            tasks = particleModels.stream().map(item -> AsyncUtils.asyncExecutorBean(() -> (new ForkJoinDownloadModel(item, ModelConfigProperties.getRootBucket(),
                    tempPath + File.separator, ost, TenantUtils.getTenantByRequest())).get(), EXECUTOR_SERVICE)).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(planDownloadTexture)) {
            for (String textureId : planDownloadTexture) {
                // 下载贴图资源
                ost.downloadObject(ModelConfigProperties.getRootBucket(), String.format("%s/%s/%s/",TenantUtils.getTenantByRequest(),ModelConfigProperties.getTexturePath(), textureId), tempPath + File.separator + ModelConfigProperties.getRootBucket() + File.separator + String.format("texture/%s/%s/",TenantUtils.getTenantByRequest(), textureId));
            }
        }
        if (!CollectionUtils.isEmpty(tasks)) {
            List<Boolean> rs = tasks.stream().map(CompletableFuture::join).toList();
            log.info("资源包总成功执行数：{}", rs.size());
        }
        // resource\model\master\
        File modelPath = new File(tempPath, String.format("%s%s%s%s%s", ModelConfigProperties.getRootBucket(), File.separator, ModelConfigProperties.getModelPath(), File.separator, TenantUtils.getTenantByRequest()));
        if(modelPath.exists()){
            try {
                for (File model:modelPath.listFiles()){
                    FileUtils.moveDirectory(model,new File(model.getParentFile().getParentFile().getAbsolutePath(),model.getName()));
                }
                FileUtils.deleteDirectory(modelPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        // resource\model
        }
        File texturePath = new File(tempPath, String.format("%s%s%s%s%s", ModelConfigProperties.getRootBucket(), File.separator, ModelConfigProperties.getTexturePath(), File.separator, TenantUtils.getTenantByRequest()));
        if(texturePath.exists()){
            try {
                for (File model:texturePath.listFiles()){
                    FileUtils.moveDirectory(model,new File(model.getParentFile().getParentFile().getAbsolutePath(),model.getName()));
                }
                FileUtils.deleteDirectory(texturePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            // resource\texture
        }
        String buildFileName = zipPath + PlacementDownloadUtils.SEP + zipFileName + ".zip";
        // 压缩这个文件夹
        ZipUtil.zip( String.format("%s%s%s",tempPath,  File.separator,ModelConfigProperties.getRootBucket()), zipPath + PlacementDownloadUtils.SEP + zipFileName + ".zip");
        try {
            // 文件导出完毕后，上传到minio
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), expiredDay + "/"+ TenantUtils.getTenantByRequest() +"/"+ zipFileName + ".zip", buildFileName);
            // 下载完成之后,删除临时目录
            FileUtils.deleteQuietly(new File(tempRootPath));
        } catch (Exception e) {
            log.error("删除临时目录失败：", e);
        }

        if (StringUtils.isEmpty(diyMessage)) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("资源包下载完成！请在%s日之前下载，过期后，文件将被删除", formatter2.format(expiredDate)), expiredDay + "/"+ TenantUtils.getTenantByRequest() + "/" + zipFileName + ".zip"));
        } else {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("%s请在%s日之前下载，过期后，文件将被删除", diyMessage, formatter2.format(expiredDate)), expiredDay + "/" + TenantUtils.getTenantByRequest() + "/" + zipFileName + ".zip"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject onlineSyncTexture(List<String> params) {
        List<TextureResource> textureResources = textureResourceMapper.queryAllByInIds(params);
        final long userId = IdentityContext.getSysLoginUserId();
        return doOnlineSyncTexture(textureResources, userId, true);
    }
    @Override
    public void downloadUModel(List<String> modelIds) {
        // 取出全量的数据，然后判断资源的类型
        SceneResourceParam param = new SceneResourceParam();
        param.setFileExist(2);
        Long count = textureResourceMapper.queryResourceCount(param);
        List<SceneResourceVo> resources;
        if (count.compareTo(0L) > 0) {
            param.setPageFrom(0L);
            param.setPageSize(count.intValue());
            resources = textureResourceMapper.queryResourcePage(param);
        } else {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "系统没有可下载的资源"));
        }
        // 存放计划下载的资源
        List<String> planDownloadModel = new ArrayList<>(64);
        if (CollectionUtils.isEmpty(modelIds)) {
            // 下载全部
            Map<String, List<SceneResourceVo>> allTypeResource = resources.stream().filter(e -> StringUtils.isNotEmpty(e.getType())).collect(Collectors.groupingBy(SceneResourceVo::getType, Collectors.toList()));
            List<SceneResourceVo> existModels = allTypeResource.get("模型");
            if (!CollectionUtils.isEmpty(existModels)) {
                planDownloadModel = existModels.stream().map(SceneResourceVo::getModelId).collect(Collectors.toList());
            }
        } else {
            Map<String, String> resourceMap = new HashMap<>(Math.toIntExact(count));
            // 将资源的modelId，type放到map里面
            resources.stream().filter(e -> StringUtils.isNotEmpty(e.getType())).forEach(e -> resourceMap.put(e.getModelId(), e.getType()));
            // 按需下载
            for (String modelId : modelIds) {
                String type = resourceMap.get(modelId);
                if (StringUtils.isEmpty(type)) {
                    // 未取到说明按基础模型处理
                    planDownloadModel.add(modelId);
                    continue;
                }
                if ("模型".equals(type)) {
                    planDownloadModel.add(modelId);
                }
            }
        }

        final long sysLoginUserId = IdentityContext.getSysLoginUserId();
//        taskCache.checkTask(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUserId, "下载模型包");
        List<String> finalPlanDownloadModel = planDownloadModel;
        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUserId, DateUtils.getTime(), TaskCache.EXPIRED, TimeUnit.HOURS);
            try {
                currentProxy().doDownloadUModel(sysLoginUserId, null, finalPlanDownloadModel);
                log.warn("资源包下载成功");
            } catch (Exception e) {
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUserId, "模型包下载失败，请稍后再试！", null));
                log.warn("资源包下载失败，原因：", e);
                throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型包下载失败"));
            } finally {
//                taskCache.delete(TaskCache.TASK_SYNC_PREFIX + "_downloadModel_" + sysLoginUserId);
                log.warn("资源包下载完成");
            }
        }, EXECUTOR_SERVICE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doDownloadUModel(Long userId, String diyMessage, List<String> planDownloadModel) {
        LocalDate expiredDate = LocalDate.now().plusDays(DownloadProperties.getFileExpiredDays());
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String expiredDay = formatter1.format(expiredDate);

        // 临时导出目录
        String tempRootPath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        String tempPath = tempRootPath + PlacementDownloadUtils.SEP + "temp";
        String zipPath = tempRootPath + PlacementDownloadUtils.SEP + "zip";
        FileUtil.mkdir(tempRootPath);
        FileUtil.mkdir(tempPath);
        FileUtil.mkdir(zipPath);

        // 生成mapping.json
        final File mappingJsonFile = FileUtil.newFile(tempPath + "/" + "mapping.json");
        FileUtil.writeString("{}", mappingJsonFile, StandardCharsets.UTF_8);

        // 生成product_lib.json
        final File productLibJsonFile = FileUtil.newFile(tempPath + "/" + "product_lib.json");
        final List<ThingsModel> thingsModels = getListByModelIds(new HashSet<>(planDownloadModel));
        JSONObject productLib = new JSONObject();
        for (ThingsModel thingsModel : thingsModels) {
            JSONObject modelInfo = new JSONObject();
            modelInfo.put("id", thingsModel.getModelId());
            modelInfo.put("模型", thingsModel.getModelId());
            modelInfo.put("版本", thingsModel.getVersion());
            productLib.put(thingsModel.getTitle(), modelInfo);
        }
        FileUtil.writeString(productLib.toJSONString(), productLibJsonFile, StandardCharsets.UTF_8);

        // 生成模型包
        if (!CollectionUtils.isEmpty(thingsModels)) {
            for (ThingsModel thingsModel : thingsModels) {
                final String modelId = thingsModel.getModelId();

                String minioPath = String.format("/%s/model/%s/0/gltf/", TenantUtils.getTenantByRequest(),modelId);
                String tempModelPath = "resource" + "/" + minioPath;
                ost.downloadObject(ModelConfigProperties.getRootBucket(), minioPath, tempPath);

                // 生成模型预览图文件
                final File jpgFile = FileUtil.newFile(tempPath + "/" + modelId + ".jpg");
                final File sourceJpgFile = FileUtil.newFile(tempPath + "/" + tempModelPath + "screenshot.jpg");
                FileUtil.copy(sourceJpgFile, jpgFile, true);

                // 生成模型压缩包文件
                final String zipFilePath = tempPath + "/" + modelId + ".zip";
                final String unzipFilePath = tempPath + "/" + StringUtils.removeEnd(tempModelPath, "/");
                ZipUtil.zip(unzipFilePath, zipFilePath);
            }
        }

        // 删除minio下载的resource目录
        File minioResource = new File(tempPath + "/" + "resource");
        try {
            FileUtils.deleteDirectory(minioResource);
        } catch (IOException e) {
            log.error("删除minio下载的resource目录失败", e);
        }

        String zipFileName = "模型包_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        String buildFileName = zipPath + PlacementDownloadUtils.SEP + zipFileName;

        // 压缩这个文件夹
        ZipUtil.zip(tempPath, buildFileName);
        try {
            // 文件导出完毕后，上传到minio
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), expiredDay + "/" + TenantUtils.getTenantByRequest() +"/"+ zipFileName, buildFileName);
            // 下载完成之后,删除临时目录
            FileUtil.del(tempRootPath);
        } catch (Exception e) {
            log.error("删除临时目录失败：", e);
        }

        if (StringUtils.isEmpty(diyMessage)) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("模型包下载完成！请在%s日之前下载，过期后，文件将被删除", formatter2.format(expiredDate)), expiredDay + "/"+TenantUtils.getTenantByRequest() +"/"+ zipFileName));
        } else {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("%s请在%s日之前下载，过期后，文件将被删除", diyMessage, formatter2.format(expiredDate)), expiredDay + "/" +TenantUtils.getTenantByRequest() +"/"+ zipFileName));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject doOnlineSyncTexture(List<TextureResource> params, long userId, boolean asyncFlag) {
        // 获取minio里面已经存在的目录
        List<String> textureFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() +"/", false).getNames();
        // 已存在的就不再同步了
        Set<String> textureExistCollect = new HashSet<>(textureFilePath);
        Map<String, Long> calculated = new HashMap<>(CollectionUtils.isEmpty(params) ? 16 : params.size());
        List<String> successList = new ArrayList<>();
        if (!asyncFlag) {
            // 同步没有的
            List<TextureResource> planAsyncTemp = new ArrayList<>();
            for (TextureResource item : params) {
                // 双重校验，只有数据库存在，且文件也存在才算都存在
                if (textureExistCollect.contains("texture/"+ TenantUtils.getTenantByRequest() +"/" + item.getTextureId() + "." + item.getExtendName()) && (2 == item.getFileExist())) {
                    successList.add(item.getTextureId());
                    long fileSize;
                    fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() +"/" + item.getTextureId() + "." + item.getExtendName()).getSize();
                    calculated.put(item.getTextureId(), fileSize);
                } else {
                    planAsyncTemp.add(item);
                }
            }
            if (!CollectionUtils.isEmpty(planAsyncTemp)) {
                params = new ArrayList<>(planAsyncTemp);
            }
        }
        // 对于已经存在的文件，还需要重新计算一下大小
        if (!CollectionUtils.isEmpty(successList)) {
            textureResourceMapper.updateFileSize(userId, calculated);
        }
        // 导入结果
        JSONObject outData = new JSONObject();
        if (CollectionUtils.isEmpty(params)) {
            outData.put("success", successList);
            outData.put("error", Collections.EMPTY_LIST);
            return outData;
        }
        TextureDownloadUtils.downloadTexture(ost, params);
        // 再次获取minio里面已经存在的目录
        textureFilePath = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() + "/", false).getNames();
        textureExistCollect = new HashSet<>(textureFilePath);

        // 同步失败和成功的结果集
        List<TextureResource> planUpadteList = new ArrayList<>(params.size());

        List<String> errorList = new ArrayList<>();
        for (TextureResource item : params) {
            boolean exist = textureExistCollect.contains("texture/"+ TenantUtils.getTenantByRequest() +"/" + item.getTextureId() + "." + item.getExtendName());
            long fileSize = 0L;
            if (exist) {
                // 计算一下大小
                fileSize = ost.listObjects(ModelConfigProperties.getRootBucket(), "/" + TenantUtils.getTenantByRequest() + "/" + ModelConfigProperties.getTexturePath() +"/" + item.getTextureId() + "." + item.getExtendName()).getSize();
            }
            item.setFileExist(exist ? 2 : 1);
            item.setFileSize(exist ? fileSize : 0L);
            if (exist) {
                item.setCreateUser(userId);
                item.setUpdateUser(userId);
                planUpadteList.add(item);
                successList.add(item.getTextureId());
            } else {
                errorList.add(item.getTextureId());
            }
        }
        if (!CollectionUtils.isEmpty(planUpadteList)) {
            // 执行添加或者覆盖
            textureResourceMapper.batchInsert(planUpadteList);
        }
        outData.put("success", successList);
        outData.put("error", errorList);
        return outData;
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(value = "'thingsModel:easySyncAllResource:' + #flag", message = "当前资源正在同步中，请稍后再试")
    @Override
    public void easySyncAllResource(Boolean flag) {
        final long sysLoginUser = IdentityContext.getSysLoginUserId();
        // 判断当前是否已经在同步了或是是否溢出
//        taskCache.checkTask(TaskCache.RESOURCE_SYNC_PREFIX, "同步全部资源");
//        AsyncUtils.asyncExecutorBean(() -> {
//            taskCache.set(TaskCache.RESOURCE_SYNC_PREFIX, DateUtils.nowDateFormatDefault(), TaskCache.EXPIRED, TimeUnit.HOURS);
        try {
            String message = "全量同步资源完成";
            try {
                // 同步贴图数据
                List<TextureResource> allTextureResources = textureResourceMapper.queryAll();
                JSONObject result = doOnlineSyncTexture(allTextureResources, sysLoginUser, flag);
                JSONArray errorList = result.getJSONArray("error");
                JSONArray successList = result.getJSONArray("success");
                int successNum = (!Objects.isNull(successList) && !successList.isEmpty()) ? successList.size() : 0;
                message += String.format("。贴图资源同步成功：%s个", successNum);
                if (!Objects.isNull(errorList) && !errorList.isEmpty()) {
                    StringJoiner joiner = new StringJoiner("，", "[", "]");
                    errorList.forEach(e -> joiner.add(e.toString()));
                    message += "，以下贴图同步失败：" + joiner;
                }
            } catch (Exception e) {
                message += "贴图资源同步失败";
                log.error("全量同步贴图数据异常：", e);
            }
            try {
                // 同步模型数据
                ThingsModel queryModelWhere = new ThingsModel();
                queryModelWhere.setModelType(2);
                List<ThingsModel> allProjectModel = pluginThingsModelMapper.queryModel(queryModelWhere);
                JSONObject result = currentProxy().onlineSaveThings(allProjectModel, sysLoginUser, flag);
                JSONArray errorList = result.getJSONArray("error");
                JSONArray successList = result.getJSONArray("success");
                int successNum = (!Objects.isNull(successList) && !successList.isEmpty()) ? successList.size() : 0;
                message += String.format("。模型资源同步成功：%s个", successNum);
                if (!Objects.isNull(errorList) && !errorList.isEmpty()) {
                    StringJoiner joiner = new StringJoiner("，", "[", "]");
                    errorList.forEach(e -> joiner.add(e.toString()));
                    message += "，以下模型同步失败：" + joiner;
                }
            } catch (Exception e) {
                message += "模型资源同步失败";
                log.error("全量同步项目模型异常：", e);
            }
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, message, null));
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser, "全量同步项目模型失败！", null));
            log.error("全量同步资源异常：", e);
        } finally {
//                taskCache.delete(TaskCache.RESOURCE_SYNC_PREFIX);
            log.warn("资源同步完成");
        }
//        }, EXECUTOR_SERVICE);
    }

    @Override
    public Optional<ThingsModel> getOneByModelId(String modelId) {
        AssertUtils.isNotNull(modelId, new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型ID不能为空")));

        final LambdaQueryWrapper<ThingsModel> wrapper = Wrappers.<ThingsModel>lambdaQuery().eq(ThingsModel::getModelId, modelId);
        return Optional.ofNullable(getOne(wrapper, false));
    }

    @Override
    public List<ThingsModel> getListByModelIds(Set<String> modelIds) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return new ArrayList<>();
        }

        final LambdaQueryWrapper<ThingsModel> wrapper = Wrappers.<ThingsModel>lambdaQuery().in(ThingsModel::getModelId, modelIds);
        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void downloadBasicModel(ThingsModelParam param) {
        LambdaQueryWrapper<ThingsModel> queryWrapper = new LambdaQueryWrapper<>();
        // 为空的是目录节点
        queryWrapper.isNotNull(ThingsModel::getModelId);
        if (Objects.nonNull(param)) {
            // 根据名称或者id进行模糊查询
            if (StringUtils.isNotEmpty(param.getTitle())) {
                queryWrapper.and(wrapper -> wrapper.like(ThingsModel::getModelId, param.getTitle()).or().like(ThingsModel::getTitle, param.getTitle()));
            }
            // 根据分类进行检索
            if (StringUtils.isNotEmpty(param.getClassify())) {
                queryWrapper.like(ThingsModel::getClassify, param.getClassify());
            }
        }
        queryWrapper.eq(ThingsModel::getModelType, 1);
        queryWrapper.eq(ThingsModel::getFileExist, 2);
        List<ThingsModel> models = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(models)) {
            throw new ThingsModelException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "系统没有可下载的资源"));
        }
        List<String> modelIds = models.stream().map(ThingsModel::getModelId).collect(Collectors.toList());
        currentProxy().downloadModel(modelIds);
    }

    @Override
    public void downloadTempFile(SysRealTimeMessage messageVo) {
        final HttpServletResponse response = HttpServletUtils.getResponse();
        if (StringUtils.isEmpty(messageVo.getAttachmentAddress())) {
            try (PrintWriter writer = response.getWriter()) {
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/json; charset=utf-8");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                writer.write(JSONObject.toJSONString(ResponseData.error("缺少文件参数")));
            } catch (Exception e) {
                log.error("向前端打印回执消息异常：", e);
            }
            return;
        }
        String downloadPath = PlacementDownloadUtils.DOWNLOAD_PATH + messageVo.getAttachmentAddress();
        // 判断当天的文件夹是否存在
        File downloadDir = new File(downloadPath);
        if (downloadDir.exists()) {
            try (FileInputStream downloadStream = new FileInputStream(downloadDir);
                 ServletOutputStream outputStream = response.getOutputStream()) {
                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(downloadDir.getName(), StandardCharsets.UTF_8) + "\"");
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.setContentType("application/json; charset=utf-8");
                byte[] b = new byte[100];
                int len;
                while ((len = downloadStream.read(b)) > 0) {
                    outputStream.write(b, 0, len);
                }
                // TODO 避免内存溢出，走stream
                //DownloadUtils.download(downloadDir.getName(), downloadStream, response);
            } catch (Exception e) {
                log.error("文件下载异常：", e);
            }
        } else {
            try (PrintWriter writer = response.getWriter()) {
                response.setContentType("application/json; charset=utf-8");
                response.setCharacterEncoding("UTF-8");
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                writer.write(JSONObject.toJSONString(ResponseData.error("文件不存在，请重新下载")));
            } catch (Exception e) {
                log.error("向前端打印回执消息异常：", e);
            }
        }
    }

    /**
     * 添加模型分组数据
     *
     * @param param 模型分组参数
     * <AUTHOR>
     * @date 2023/2/16 17:02
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveClassify(ThingsModelClassifyAddParam param) {
        // 1.参数校验
        checkAddParam(param);
        // 2.入库
        boolean isDir = StringConstant.SLASH.equals(param.getParentKey());
        ThingsModel pluginThingsModel = new ThingsModel();
        pluginThingsModel.setModelType(param.getModelType());
        pluginThingsModel.setClassify(isDir ? param.getTitle() + StringConstant.SLASH : (param.getParentKey() + param.getTitle()));
        pluginThingsModel.setDirFlag(2);
        pluginThingsModel.setFileSize(0);
        pluginThingsModel.setStatus(0);
        pluginThingsModel.setType("Floder");
        pluginThingsModel.setCreateTime(new Date());
        pluginThingsModel.setCreateUser(IdentityContext.getSysLoginUserId());
        pluginThingsModel.setUpdateTime(new Date());
        pluginThingsModelMapper.insertModel(pluginThingsModel);
    }

    /**
     * 参数校验
     *
     * @param classify
     * <AUTHOR>
     * @date 2023/2/16 17:03
     */
    private void checkAddParam(ThingsModelClassifyAddParam classify) {
        AssertUtils.isTrue(classify.getParentKey().endsWith(StringConstant.SLASH), HttpStatus.BAD_REQUEST, "父节点key有误");
        AssertUtils.isTrue(!classify.getTitle().contains(StringConstant.SLASH), HttpStatus.BAD_REQUEST, "该模型分类名称不能含有字符 /");
        List<ThingsModel> pluginThingsModels = listModels(classify.getModelType(), null);
        long number = pluginThingsModels.stream().filter(s ->
                Objects.nonNull(s.getClassify()) && (
                        s.getClassify().startsWith(classify.getTitle() + StringConstant.SLASH) ||
                                Objects.equals(s.getClassify(), classify.getParentKey() + classify.getTitle()))).count();
        AssertUtils.isTrue(number == 0, HttpStatus.BAD_REQUEST, "该模型分类名称已经存在");
    }

    /**
     * 修改模型分类
     *
     * @param param 模型分类修改参数
     * <AUTHOR>
     * @date 2023/2/16 22:18
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editClassify(ThingsModelClassifyUpdateParam param) {
        String key = param.getKey();
        if (key.equals(param.getEditTitle() + StringConstant.SLASH) || StringConstant.SLASH.equals(key)) {
            return;
        }
        // 1.参数校验
        List<ThingsModel> pluginThingsModels = checkEditParam(param);
        // 2.修改
        if (!CollectionUtils.isEmpty(pluginThingsModels)) {
            boolean isDir = key.endsWith(StringConstant.SLASH);
            Long sysLoginUserId = IdentityContext.getSysLoginUserId();
            List<ThingsModel> updateList = pluginThingsModels.stream().filter(f ->
                    Objects.nonNull(f.getClassify()) &&
                            ((isDir &&
                                    f.getClassify().startsWith(param.getKey())) ||
                                    (!isDir &&
                                            Objects.equals(f.getClassify(), param.getKey())))
            ).toList();

            updateList.forEach(i -> {
                String classify = isDir
                        ? i.getClassify().replace(key, param.getEditTitle() + StringConstant.SLASH)
                        : i.getClassify().replace(key, key.split(StringConstant.SLASH)[0] + StringConstant.SLASH + param.getEditTitle());
                i.setClassify(classify);
                i.setUpdateUser(sysLoginUserId);
                i.setUpdateTime(new Date());
            });
            pluginThingsModelMapper.updateForBatch(pluginThingsModels);
        }
    }

    /**
     * 参数校验待修改的模型分类
     *
     * @param classify
     * @return list
     * <AUTHOR>
     * @date 2023/2/16 21:59
     */
    private List<ThingsModel> checkEditParam(ThingsModelClassifyUpdateParam classify) {
        AssertUtils.isTrue(classify.getKey().contains(StringConstant.SLASH), HttpStatus.BAD_REQUEST, "该模型分类的参数key缺失字符/");
        // 1.判断修改的模型分类名称是否已经存在
        List<ThingsModel> pluginThingsModels = listModels(classify.getModelType(), null);
        long count = pluginThingsModels.stream().map(ThingsModel::getClassify).filter(f -> f != null && f.contains(classify.getEditTitle() + StringConstant.SLASH)).count();
        AssertUtils.isTrue(count == 0, HttpStatus.BAD_REQUEST, "该模型分类名称已经存在");
        return pluginThingsModels;
    }

    /**
     * 根据模型分类key删除该分类，以及该分类下的模型
     *
     * @param key 模型分类key
     * <AUTHOR>
     * @date 2023/2/16 22:19
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteClassify(ThingsModelClassifyDeleteParam key) {
        AssertUtils.isTrue(!StringConstant.SLASH.equals(key.getKey()), HttpStatus.BAD_REQUEST, "该模型分类根节点不能修改");
        AssertUtils.isTrue(key.getKey().contains(StringConstant.SLASH), HttpStatus.BAD_REQUEST, "参数key缺失字符 /");
        List<ThingsModel> pluginThingsModels = listModels(key.getModelType(), key.getKey());
        pluginThingsModels.forEach(s -> {
            s.setStatus(2);
            pluginThingsModelMapper.updateById(s);
        });
    }

    private List<ThingsModel> listModels(Integer modelType, String classify) {
        cacheRefresh.refresh();
        LambdaQueryWrapper<ThingsModel> lambdaQueryWrapper = Wrappers.<ThingsModel>lambdaQuery().
                eq(BaseEntity::getStatus, 0).
                eq(modelType != null, ThingsModel::getModelType, modelType).
                like(StringUtils.isNotEmpty(classify), ThingsModel::getClassify, classify);
        return pluginThingsModelMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据模型存储位置下载模型和贴图
     * /resource/model/9cd444b962e84af98f1055a4c10d94c5/0/gltf/screenshot.jpg
     *
     * @param modelId 模型id
     * @param type    模型/贴图
     * @param modelNames    已经存在的贴图/模型
     * <AUTHOR>
     * @date 2023/2/21 21:44
     */
    @Override
    public String getModelPath(String modelId, String type, Set<String> modelNames) {
        String zipFileName = modelId + StringConstant.DOT + "zip";
        String filePath = TenantUtils.getTenantByRequest() + StringConstant.SLASH + type + StringConstant.SLASH + zipFileName;
        String tempRootPath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        try {
            if (!modelNames.contains(modelId)) {
                // 临时导出目录
                String tempPath = tempRootPath + StringConstant.SLASH + "temp";
                String zipPath = tempRootPath + StringConstant.SLASH + "zip";
                final File tempRootDir = new File(tempRootPath);
                final File tempDir = new File(tempPath);
                final File zipDir = new File(zipPath);
                try {
                    FileUtils.forceMkdir(tempRootDir);
                    FileUtils.forceMkdir(tempDir);
                    FileUtils.forceMkdir(zipDir);
                } catch (IOException e) {
                    if (!tempRootDir.exists()) {
                        try {
                            FileUtils.forceMkdir(tempRootDir);
                        } catch (IOException ex) {
                            throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + tempRootPath, ex);
                        }
                    }
                    if (!tempDir.exists()) {
                        try {
                            FileUtils.forceMkdir(tempDir);
                        } catch (IOException ex) {
                            throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + tempPath, ex);
                        }
                    }
                    if (!zipDir.exists()) {
                        try {
                            FileUtils.forceMkdir(zipDir);
                        } catch (IOException ex) {
                            throw ThrowUtils.getThrow().internalServerError("创建临时目录失败: " + zipPath, ex);
                        }
                    }
                }

                // 下载贴图资源,压缩这个文件夹
                // TODO: 2023/3/15
                String path = "模型".equals(type)? ModelConfigProperties.getModelPath() : ModelConfigProperties.getTexturePath();
                ost.downloadObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/" + path + "/"+ modelId, tempPath + "/");
                String buildFileName = zipPath + StringConstant.SLASH + zipFileName;
                ZipUtil.zip(tempPath, buildFileName);
                ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
                ost.uploadObject(DownloadProperties.getTempPath(), TenantUtils.getTenantByRequest() + "/" + type + "/" + zipFileName, buildFileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            FileUtils.deleteQuietly(new File(tempRootPath));
        }
        return filePath;
    }

    @Override
    public List<TextureResource> queryAllTextureResource() {
        return textureResourceMapper.queryAll();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchInsertTextureResource(@RequestBody List<TextureResource> list) {
        textureResourceMapper.batchInsert(list);
    }

    @Override
    public PageResult<SceneResourcePageVo> resourcePage(SceneResourceWrapperParam param) {
        Page<SceneResourcePageVo> pageParam = PageFactory.defaultPage();
        param.setPageFrom(((pageParam.getCurrent() - 1) * pageParam.getSize()));
        param.setPageSize((int) pageParam.getSize());
        param.setModelType(2);
        Long count = textureResourceMapper.queryResourceWrapCount(param);
        List<SceneResourcePageVo> models = textureResourceMapper.queryResourceWrapPage(param);
        pageParam.setTotal(count);
        pageParam.setRecords(wrapSceneResourceList(models));
        return new PageResult<>(pageParam);
    }

    /**
     * 包装分页,添加贴图的下载url
     * /gateway/pluginx/
     *
     * @param list 原始分页
     * @return 分页结果
     * <AUTHOR>
     * @date 2023/2/17 11:29
     */
    private List<SceneResourcePageVo> wrapSceneResourceList(List<SceneResourcePageVo> list) {
        Set<String> types = list.stream().collect(Collectors.groupingBy(SceneResourcePageVo::getType)).keySet();
        Map<String, Set<String>> existsResource = new HashMap<>();
        for (String type : types) {
            String prefix = type.equals("模型") ? String.format("%s/%s/", TenantUtils.getTenantByRequest(), type) : String.format("%s/%s/", type, TenantUtils.getTenantByRequest());
            String filePath = type.equals("模型") ? TenantUtils.getTenantByRequest() + StringConstant.SLASH + type + StringConstant.SLASH : type + StringConstant.SLASH + TenantUtils.getTenantByRequest() + StringConstant.SLASH;
            Set<String> existsFiles = ost.listObjects(DownloadProperties.getTempPath(), filePath, false).getNames().stream().map(e -> PatternUtil.removeSuffix(PatternUtil.removePrefix(e,prefix))).collect(Collectors.toSet());
            existsResource.put(type,existsFiles);
        }


        list.forEach(f -> {
            String modelPath = getModelPath(f.getModelId(), f.getType(),existsResource.get(f.getType()));
            f.setFileUrl(modelPath);
        });
        return list;
    }

    private String getSceneDbPre() {
        String tenant = TenantUtils.getTenantByRequest();
        tenant = tenant.toLowerCase();
        return TenantConstant.MASTER.equals(tenant) ? DataSourceConstant.SCENE_X : tenant + StringConstant.UNDERSCORE + DataSourceConstant.SCENE_X;
    }
}
