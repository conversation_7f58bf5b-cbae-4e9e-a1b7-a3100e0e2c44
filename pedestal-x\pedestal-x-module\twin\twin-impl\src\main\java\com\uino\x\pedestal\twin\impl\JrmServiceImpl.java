package com.uino.x.pedestal.twin.impl;


import apijson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.pedestal.twin.api.service.JrmAdditionalDataService;
import com.uino.x.pedestal.twin.api.service.JrmService;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.JrmLockContext;
import com.uino.x.pedestal.twin.jrm.apijson.JrmSqlConfig;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.exception.JrmException;
import com.uino.x.pedestal.twin.jrm.core.json.serialize.JsonSerialize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Jrm service实现
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/11/30 15:54
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class JrmServiceImpl implements JrmService {

    private final JrmAdditionalDataService additionalDataService;
    private final JsonSerialize jsonSerialize;
    private final JrmLockContext jrmLockContext;

    @Override
    public String get(String request) {
        AssertUtils.isTrue(StringUtils.isJsonObject(request), new JrmException("json 不合法"));
        JSONObject beforeParamJson = JSONObject.parseObject(request, Feature.OrderedField);
        AssertUtils.isNotNull(beforeParamJson, new JrmException("json 不合法"));
        boolean perInfo = beforeParamJson.containsKey("perInfo") && (beforeParamJson.get("perInfo").getClass() == Boolean.class) && (boolean) beforeParamJson.get("perInfo");
        boolean alarmInfo = beforeParamJson.containsKey("alarmInfo") && (beforeParamJson.get("alarmInfo").getClass() == Boolean.class) && (boolean) beforeParamJson.get("alarmInfo");
        beforeParamJson.remove("perInfo");
        beforeParamJson.remove("alarmInfo");

        JSONObject result = JSON.parseObject(JrmBean.request(RequestType.GET, beforeParamJson.toJSONString()));
        //如果需要查询告警或性能数据
        if (perInfo || alarmInfo) {
            //获取返回的数据key
            List<String> pendTableList = new ArrayList<>();
            beforeParamJson.keySet().forEach(item -> {
                if (StringUtils.isJsonObject(beforeParamJson.get(item).toString())) {
                    pendTableList.add(item);
                }
            });

            for (String table : pendTableList) {
                if (ObjectUtils.isEmpty(result.get(table))) {
                    continue;
                }
                //查询出为多条数据
                if (StringUtils.isJsonArray(result.get(table).toString())) {
                    JSONArray queryDataSs = JSON.parseArray(result.get(table).toString());
                    for (int i = 0; i < queryDataSs.size(); i++) {
                        JSONObject queryDataMap = JSON.parseObject(queryDataSs.get(i));
                        additionalDataService.queryAlarmAndPreDataMapDispose(queryDataMap, perInfo, alarmInfo);
                        queryDataSs.set(i, queryDataMap);
                    }
                    result.put(table, queryDataSs);
                } else if (StringUtils.isJsonObject(result.get(table).toString())) {
                    JSONObject queryDataMap = JSON.parseObject(result.get(table).toString());
                    additionalDataService.queryAlarmAndPreDataMapDispose(queryDataMap, perInfo, alarmInfo);
                    result.put(table, queryDataMap);
                } else {
                    throw new JrmException("基础数据解析未成功，查询告警失败");
                }
            }
        }
        return JSONObject.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect);

    }


    @Override
    public String post(String request) {
        final String table = jsonSerialize.firstKey(request);
        final String sqlTable = new JrmSqlConfig(RequestType.POST.getApiJsonRequest())
                .setTable(table)
                .gainSQLTable();
        return jrmLockContext.tableLock(sqlTable, k -> JrmBean.request(RequestType.POST, request));
    }

    @Override
    public String put(String request) {
        return JrmBean.request(RequestType.PUT, request);
    }

    @Override
    public String delete(String request) {
        return JrmBean.request(RequestType.DELETE, request);
    }

    @Override
    public String alter(String request) {
        return JrmBean.request(RequestType.ALTER, request);
    }

    @Override
    public String create(String request) {
        return JrmBean.request(RequestType.CREATE, request);
    }

    @Override
    public String drop(String request) {
        return JrmBean.request(RequestType.DROP, request);
    }
}
