<template>
  <div class="content keep-px">
    <!-- 搜索栏 -->
    <div class="search-wrap">
      <div class="search-item">
        <span class="search-label" style="width: 50px">关键词</span>
        <a-input v-model:value="queryParam.searchValue" allow-clear placeholder="请输入设备编码" class="search-input" @keyup.enter="clickSearch()" />
      </div>
      <div class="search-btns">
        <a-button type="primary" size="small" class="search-btn" @click="clickSearch()">查询</a-button>
        <a-button class="search-btn" size="small" @click="reset()">重置</a-button>
        <ConditionBox :assembly-filter-group="assemblyFilterGroup" @call-back="conditionBox" />
      </div>
    </div>
    <div class="table-wrap">
      <div ref="table" class="table-content">
        <a-table
          class="table"
          :scroll="{ x: 1200, y: 460 }"
          :custom-row="customRow"
          :row-class-name="rowClassName"
          :pagination="false"
          size="small"
          :row-selection="rowSelection"
          :loading="loading"
          :row-key="(record: any) => record.uuid"
          :columns="columns"
          :data-source="tabData"
          @change="tableChange"
        >
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'action'">
              <div class="table-actions">
                <a @click.stop="handleEdit(record)">更新</a>
                <a-popconfirm
                  v-if="!(chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID && chooseTwinData.dataType !== 'FORM')"
                  placement="topRight"
                  title="确认删除？"
                  @confirm="() => twinDataDelete([record.uuid])"
                >
                  <a @click.stop>删除</a>
                </a-popconfirm>
              </div>
            </template>
            <template v-if="column.dataIndex === 'iconStatus'">
              <span style="display: inline-block; width: 10px; height: 10px; margin-right: 8px; border-radius: 5px" :style="{ backgroundColor: text ? '#1DBE53' : '#C3C4C7' }"></span>
              <span>{{ text ? '已标注' : '未标注' }}</span>
            </template>
            <!-- 处理单选框和多选框显示 -->
            <template v-for="item in selectOrCheckbox">
              <template v-if="item === column.key">
                <span :key="item">{{ jsonParseArr(text) }}</span>
              </template>
            </template>
            <!-- 处理富文本 -->
            <template v-for="item in editorSlotName">
              <template v-if="item === column.key">
                <a :key="item" @click="showEditor(text)">详情</a>
              </template>
            </template>
            <!-- 处理文件 -->
            <template v-for="item in downloadFileSlotNames">
              <template v-if="item === column.key">
                <preview-and-download :key="item" :item="item" :record="record" :text="text" />
              </template>
            </template>
            <!-- 处理关联孪生体 -->
            <template v-for="item in releativeSlotNames">
              <template v-if="item === column.key">
                <a v-if="relativeLength(record, column.dataIndex)" :key="item" @click="showReletive(record, column)">详情</a>
                <span v-else :key="item + '1'">--</span>
              </template>
            </template>
          </template>
          <template #expandedRowRender="{ record }">
            <p style="margin: 0">
              <span v-for="(item, i) in userDetailCol" :key="i" style="display: block"> {{ item.name }}：{{ disposeRecord(item, record) }} </span>
            </p>
          </template>
        </a-table>
        <div class="pagination">
          <a-pagination v-if="tabData.length > 0" v-bind="paginationConfig" @change="paginationChange" />
        </div>
      </div>
    </div>
  </div>
  <!-- 孪生体数据更新 -->
  <AddEditForm ref="addEditFormRef" @success="editAndAddCb" />
  <!-- 富文本预览 -->
  <a-modal
    :title="editorDialog.title"
    :body-style="{ maxHeight: '80vh', paddingBottom: '40px', overflowY: 'auto', overflowX: 'hidden' }"
    wrap-class-name="cus-modal"
    :footer="null"
    :width="900"
    :open="editorDialog.show"
    :mask-closable="false"
    @cancel="editorDialog.show = false"
  >
    <div class="editor-html" v-html="editorDialog.content"></div>
  </a-modal>
  <!-- 关联孪生体 -->
  <Reletive ref="reletiveRef" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { getTwinData, deleteTwinData, getSceneListByTwinCode, getAllVersionSceneCodes, getAllVersionSceneIds } from '@/api/business/twinObject';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { sysDictTypeDropDown } from '@/api/develop/dictionaryManage';
import AddEditForm from './AddEditForm.vue';
import PreviewAndDownload from './PreviewAndDownload.vue';
import ConditionBox from './ConditionBox.vue';
import Reletive from './Reletive.vue';

const emits = defineEmits(['dataChange', 'selectChange', 'clickRowChange']);
// 当前选中的行
const tableCurrRowUuid = ref();
// 自定义行class
const rowClassName = (record: any) => {
  return record.uuid === tableCurrRowUuid.value ? 'active' : '';
};
// 处理关联孪生体边界问题
const relativeLength = (record: any, dataIndex: any) => {
  try {
    const cVal = record[dataIndex];
    if (cVal) {
      return JSON.parse(cVal).length;
    }
    return false;
  } catch (error) {
    return false;
  }
};
// 处理返回数据
const disposeRecord = (item: any, record: any) => {
  return item.key === 'current_level'
    ? levelData.value.find((ele: any) => ele.code === record[item.key]) && levelData.value.find((ele: any) => ele.code === record[item.key]).value
    : record[item.key]
      ? record[item.key]
      : '--';
};
// 行点击
const customRow = (record: any) => {
  return {
    onClick: () => {
      if (tableCurrRowUuid.value === record.uuid) {
        tableCurrRowUuid.value = '';
        emits('clickRowChange', '');
      } else {
        tableCurrRowUuid.value = record.uuid;
        emits('clickRowChange', record);
      }
    },
  };
};
const table = ref();
// 搜索条件
let queryParam = reactive<any>({
  searchValue: '',
  scene_id: '', // 所属场景
  data_source: '', // 点位数据来源
  Building_data: '', // 所属建筑
  Floor_data: '', // 所属楼层
  Room_data: '', // 房间
  scene_ids: [], // 历史版本所有场景id
  Name_data: '', //名称
});

const DEFAULT_TWIN_CLASS_GROUP_ID = '1476825472376508418';

const loading = ref(false);
const columns = ref<any>([]);
const tabData = ref([]);
const selects = ref<any>([]);
const searchs = ref<any>([]);
let chooseTwinData: any = {};
let filelds: any = {};
const formJson = ref<any>({});
const sceneList = ref<any>([]);
const sourceType = ref<any>([]);

// 格式化数组对象
const jsonParseArr = (data: string) => {
  try {
    return JSON.parse(data).toString();
  } catch (err) {
    return data;
  }
};
// 编辑和新增回调
const editAndAddCb = () => {
  getData();
};

const assemblyFilterGroup = () => {
  const result = [];
  // 排除表单
  // if (chooseTwinData.dataType !== 'FORM') {
  //     result.push({
  //         label: '所属场景',
  //         type: 'select',
  //         fieldValue: queryParam.scene_id ? queryParam.scene_id : '',
  //         fieldName: 'scene_id',
  //         data: sceneList.value,
  //         placeholder: '请选择孪生体点位所属场景',
  //     })
  // }
  // 排除默认孪生体和表单
  if (chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID && chooseTwinData.dataType !== 'FORM') {
    result.push({
      label: '点位数据来源',
      type: 'select',
      fieldValue: queryParam.data_source ? queryParam.data_source : '',
      fieldName: 'data_source',
      data: sourceType.value,
      placeholder: '请选择孪生体',
    });
  }
  // 园区孪生体
  if (chooseTwinData.dataType !== 'FORM' && chooseTwinData.level === 'PARK') {
    if (chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID) {
      // 默认建筑孪生 | 楼层 | 房间孪生体
      if (chooseTwinData.code === 'BUILDING' || chooseTwinData.code === 'FLOOR' || chooseTwinData.code === 'ROOM') {
        if (chooseTwinData.code === 'BUILDING') {
          result.push({
            label: '名称',
            type: 'input',
            fieldValue: queryParam.Name_data ? queryParam.Name_data : '',
            fieldName: 'Name_data',
            placeholder: '请输入建筑名称',
          });
        } else {
          result.push({
            label: '所属建筑',
            type: 'input',
            fieldValue: queryParam.Building_data ? queryParam.Building_data : '',
            fieldName: 'Building_data',
            placeholder: '请输入所属建筑名称',
          });
        }
      }
      // 默认楼层 | 房间孪生体
      if (chooseTwinData.code === 'FLOOR' || chooseTwinData.code === 'ROOM') {
        result.push({
          label: chooseTwinData.code === 'FLOOR' ? '名称' : '所属楼层',
          type: 'input',
          fieldValue: queryParam.Floor_data ? queryParam.Floor_data : '',
          fieldName: 'Floor_data',
          placeholder: chooseTwinData.code === 'FLOOR' ? '请输入楼层名称' : '请输入所属楼层名称',
        });
      }
      // 默认房间孪生体
      if (chooseTwinData.code === 'ROOM') {
        result.push({
          label: '名称',
          type: 'input',
          fieldValue: queryParam.Name_data ? queryParam.Name_data : '',
          fieldName: 'Name_data',
          placeholder: '请输入名称',
        });
      }
    } else {
      // 园区摆点孪生体
      result.push({
        label: '所属建筑',
        type: 'input',
        fieldValue: queryParam.Building_data ? queryParam.Building_data : '',
        fieldName: 'Building_data',
        placeholder: '请输入所属建筑',
      });
      result.push({
        label: '所属楼层',
        type: 'input',
        fieldValue: queryParam.Floor_data ? queryParam.Floor_data : '',
        fieldName: 'Floor_data',
        placeholder: '请输入所属楼层',
      });
      result.push({
        label: '所属房间',
        type: 'input',
        fieldValue: queryParam.Room_data ? queryParam.Room_data : '',
        fieldName: 'Room_data',
        placeholder: '请输入所属房间',
      });
    }
  }
  searchs.value.forEach((item: any) => {
    const val = item.options.showTime ? queryParam.RiQiXuanZeKuang : queryParam[item.model];
    result.push({
      label: item.label,
      type: item.options.showTime ? 'date' : 'input',
      fieldValue: val || '',
      fieldName: item.options.showTime ? 'RiQiXuanZeKuang' : item.model,
      placeholder: item.options.showTime ? '请选择时间' : '请输入',
    });
  });
  selects.value.forEach((item: any) => {
    result.push({
      label: item.label,
      type: item.type,
      fieldValue: queryParam[item.model] ? queryParam[item.model] : '',
      fieldName: item.model,
      data: item.options.options,
      placeholder: '请选择',
    });
  });
  return result;
};

const conditionBox = (searchs: any) => {
  searchs.forEach((item: any) => {
    queryParam[item.fieldName] = item.fieldValue;
    if (item.label === '所属场景') {
      setSceneIdsParamsAndRefresh();
    }
  });
  getData();
  tableSelects.value.list = [];
  tableSelects.value.keys = [];
  // emits('selectChange', tableSelects.value)
};

const setSceneIdsParamsAndRefresh = () => {
  if (queryParam.scene_id) {
    // 是否是默认孪生体
    const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
    if (isDefault) {
      getAllVersionSceneCodes({ uuid: queryParam.scene_id }).then((res) => {
        queryParam.scene_ids = res.data;
        getData();
      });
    } else {
      getAllVersionSceneIds({ uuid: queryParam.scene_id }).then((res) => {
        queryParam.scene_ids = res.data;
        getData();
      });
    }
  } else {
    queryParam.scene_ids = [];
    getData();
  }
};
// table每行展开
const userDetailCol = ref();
// 初始化
const init = (item: any) => {
  tabData.value = [];
  tableSelects.value = {
    keys: [],
    list: [],
  };
  chooseTwinData = item;
  // 园区
  sourceType.value = [
    { code: 'TWIN', value: '孪生对象摆点' },
    { code: 'ASSET', value: '资产数据摆点' },
    { code: 'ASSET_IMPORT', value: '资产数据导入' },
    { code: 'CAD', value: 'CAD点位转换' },
    { code: 'MMD', value: '模模搭点位转换' },
    { code: 'GIS', value: 'GIS点位转换' },
  ];
  userDetailCol.value = [
    { key: 'current_level', name: '所在层级' },
    { key: 'buildingName', name: '所属建筑' },
    { key: 'floorName', name: '所属楼层' },
    { key: 'roomName', name: '所属房间' },
    { key: 'parent_user_id', name: '所在层级ID' },
    { key: 'wgs84_position', name: 'WGS84坐标系' },
    { key: 'gcj02_position', name: 'GCJ02坐标系' },
    { key: 'gis_height', name: '离地高度' },
    { key: 'position_body', name: '点位数据信息' },
    { key: 'position', name: '位置坐标' },
  ];
  // 获取场景列表
  getSceneListByTwinCode({ twinClassCode: item.code }).then((res) => {
    sceneList.value = res.data;
  });
  getColumns();
  getData();
};

const clickSearch = () => {
  paginationConfig.value.current = 1;
  paginationConfig.value.pageSize = 10;
  getData();
};

// 分页配置
const paginationConfig = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: (total: number) => `共有${total}条`,
  pageSizeOptions: ['10', '20', '50', '100', '200', '500'],
  showSizeChanger: true,
  showQuickJumper: false,
  size: 'small' as any,
});
// 获取点位数据
const getData = async (sort: any = {}) => {
  loading.value = true;
  // 还原table行点击事件
  tableCurrRowUuid.value = '';
  emits('clickRowChange', '');
  // 孪生体code
  const table = chooseTwinData.code;
  // 是否是默认孪生体
  const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
  // 是否包含场景信息
  const hasScene = chooseTwinData.level !== 'MAP' && chooseTwinData.dataType !== 'FORM';
  // join信息是否包含场景
  const joinValue = {};
  if (isDefault) {
    // 新增的菜单/地图矢量数据/地图层级，虽然是default，但是不包含场景信息
    if (chooseTwinData.dataType !== 'FORM') {
      // @ts-ignore
      joinValue['&/Scene_record'] = {
        enable: '1',
      };
    }
    // @ts-ignore
    joinValue['</Sys_user:createUser'] = {};
    // @ts-ignore
    joinValue['</Sys_user:updateUser'] = {};
  } else if (hasScene) {
    // @ts-ignore
    joinValue['</Scene_record'] = {};
    // @ts-ignore
    joinValue['</Sys_user:createUser'] = {};
    // @ts-ignore
    joinValue['</Sys_user:updateUser'] = {};
  } else {
    // @ts-ignore
    joinValue['</Sys_user:createUser'] = {};
    // @ts-ignore
    joinValue['</Sys_user:updateUser'] = {};
  }
  // 查询条件组装
  // if (queryParam.Building_data) {
  //   if (chooseTwinData.code === 'ROOM') {
  //     // @ts-ignore
  //     joinValue['&/Room_data'] = {};
  //     // @ts-ignore
  //     joinValue['&/Floor_data'] = {};
  //   } else if (chooseTwinData.code === 'FLOOR') {
  //     // @ts-ignore
  //     joinValue['&/Floor_data'] = {};
  //   }
  //   // @ts-ignore
  //   joinValue['&/Building_data'] = {
  //     name$: queryParam.Building_data ? `%${queryParam.Building_data}%` : null,
  //     setting_name$: queryParam.Building_data ? `%${queryParam.Building_data}%` : null,
  //     user_id$: queryParam.Building_data ? `%${queryParam.Building_data}%` : null,
  //     '@combine': 'name$ | setting_name$ | user_id$',
  //   };
  // }
  // if (queryParam.Floor_data) {
  //   if (chooseTwinData.code === 'ROOM') {
  //     // @ts-ignore
  //     joinValue['&/Room_data'] = {};
  //   }
  //   // @ts-ignore
  //   joinValue['&/Floor_data'] = {
  //     name$: queryParam.Floor_data ? `%${queryParam.Floor_data}%` : null,
  //     setting_name$: queryParam.Floor_data ? `%${queryParam.Floor_data}%` : null,
  //     user_id$: queryParam.Floor_data ? `%${queryParam.Floor_data}%` : null,
  //     '@combine': 'name$ | setting_name$ | user_id$',
  //   };
  // }
  // if (queryParam.Room_data) {
  //   // @ts-ignore
  //   joinValue['&/Room_data'] = {
  //     name$: queryParam.Room_data ? `%${queryParam.Room_data}%` : null,
  //     setting_name$: queryParam.Room_data ? `%${queryParam.Room_data}%` : null,
  //     user_id$: queryParam.Room_data ? `%${queryParam.Room_data}%` : null,
  //     '@combine': 'name$ | setting_name$ | user_id$',
  //   };
  // }
  let order = '';
  if (sort.sortField && sort.sortRule) {
    let { sortField } = sort;
    if (sort.sortField === 'sceneName') {
      sortField = isDefault ? 'scene_code' : 'scene_id';
    } else if (sort.sortField === 'sceneDataName') {
      sortField = 'user_id';
    } else if (sort.sortField === 'buildingName') {
      sortField = 'user_building_id';
    } else if (sort.sortField === 'floorName') {
      sortField = 'user_floor_id';
    } else if (sort.sortField === 'roomName') {
      sortField = 'user_room_id';
    }
    order = `${sortField}${sort.sortRule === 'ASC' ? '+' : '-'}`;
  } else {
    order = '';
  }

  const search: any = {};
  if (queryParam.searchValue) {
    filelds.forEach((item: any) => {
      if (item.type !== 'uploadFile') {
        search[`${item.model}$`] = `%${queryParam.searchValue}%`;
      }
    });
    search.uuid$ = `%${queryParam.searchValue}%`;
    if (Object.keys(search).length > 1) {
      const combineArr: any = [];
      Object.keys(search).forEach((key) => {
        combineArr.push(key);
      });
      search['@combine'] = combineArr.join(',');
    }
  }

  if (queryParam.data_source) {
    search.data_source = queryParam.data_source;
  }

  if (queryParam.scene_id) {
    if (isDefault) {
      // 所有版本
      // search['scene_code{}'] = queryParam.scene_ids;
      // 当前版本
      search['scene_code{}'] = [queryParam.scene_id];
    } else {
      // 所有版本
      // search['scene_id{}'] = queryParam.scene_ids;
      // 当前版本
      search['scene_id{}'] = [queryParam.scene_id];
    }
  }

  selects.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      if (e.type === 'checkbox') {
        if (queryParam[e.model].length !== 0) {
          search[e.model] = queryParam[e.model].toString();
        }
      } else {
        search[e.model] = queryParam[e.model];
      }
    }
  });

  searchs.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      search[`${e.model}$`] = `%${queryParam[e.model]}%`;
    }
  });
  // 孪生数据查询加上场景查询条件
  if (isDefault) {
    search.scene_code = window.campusManagerIns.curSceneData.sceneCode;
    const campusBuilderIds: string[] = [];
    if (window.app.level.current.query('.Room').length) {
      window.app.level.current.query('.Room').forEach((item: any) => {
        if (window.app.level.current.id === item.parent.id) {
          campusBuilderIds.push(item.id.toString());
        }
      });
    }
    search['user_id{}'] = campusBuilderIds;
  } else {
    const res = await getAllVersionSceneIds({
      uuid: window.campusManagerIns.curSceneData.uuid,
    });
    search['scene_id{}'] = res.data;
    // search.scene_id = this.$store.state.sceneCode;
    search.parent_user_id = window.app.level.current.type === 'Campus' ? 'outdoors' : window.app.level.current.id;
  }
  // 查询条件组装
  // 查询条件组装
  if (queryParam.Name_data) {
    search[`user_id$`] = `%${queryParam.Name_data}%`;
    // search[`name$`] = `%${queryParam.Building_data}%`;
    // search[`setting_name$`] = `%${queryParam.Building_data}%`;
  }
  if (!isDefault) {
    if (queryParam.Building_data) {
      search[`user_building_id$`] = `%${queryParam.Building_data}%`;
      // search[`name$`] = `%${queryParam.Building_data}%`;
      // search[`setting_name$`] = `%${queryParam.Building_data}%`;
    }
    if (queryParam.Floor_data) {
      search[`user_floor_id$`] = `%${queryParam.Floor_data}%`;
      // search[`name$`] = `%${queryParam.Floor_data}%`;
      // search[`setting_name$`] = `%${queryParam.Floor_data}%`;
    }
    if (queryParam.Room_data) {
      search[`user_room_id$`] = `%${queryParam.Building_data}%`;
      // search[`name$`] = `%${queryParam.Building_data}%`;
      // search[`setting_name$`] = `%${queryParam.Building_data}%`;
    }
  }
  // apiJson请求信息
  const requestJson: any = {
    '[]': {
      join: joinValue,
      [table]: {
        ...search,
        '@order': `${order || 'create_time-,uuid-'}`,
      },
      'Sys_user:createUser': {
        'id@': `/${table}/create_user`,
        '@column': 'id;name',
      },
      'Sys_user:updateUser': {
        'id@': `/${table}/update_user`,
        '@column': 'id;name',
      },
      page: paginationConfig.value.current - 1,
      count: paginationConfig.value.pageSize,
      query: 2,
    },
    'total@': '/[]/total',
    'info@': '/[]/info',
  };

  if (hasScene) {
    let sceneJson: any = {
      Scene_record: {
        'uuid@': `/${table}/scene_id`,
        '@column': 'uuid;name;scene_code',
      },
      Building_data: {
        'user_id@': `/${table}/user_building_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
      Floor_data: {
        'user_id@': `/${table}/user_floor_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
      Room_data: {
        'user_id@': `/${table}/user_room_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
    };
    if (chooseTwinData.code !== 'BUILDING' && chooseTwinData.code !== 'PARK') {
      sceneJson = {
        FLOOR: {
          '@order': 'create_time-,uuid-',
        },
        BUILDING: {
          '@order': 'create_time-,uuid-',
        },
        Scene_record: {
          'uuid@': `/${table}/scene_id`,
          '@column': 'uuid;name;scene_code',
        },
        Building_data: {
          'user_id@': `/${table}/user_building_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
        Floor_data: {
          'user_id@': `/${table}/user_floor_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
        Room_data: {
          'user_id@': `/${table}/user_room_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
      };
    }
    requestJson['[]'] = Object.assign(requestJson['[]'], sceneJson);
  }
  if (isDefault) {
    let defaultJson = {};
    if (chooseTwinData.dataType !== 'FORM') {
      defaultJson = {
        Scene_record: {
          'scene_code@': `/${table}/scene_code`,
          enable: '1',
          'status!': '2',
          '@column': 'uuid;name;scene_code',
        },
      };
    }

    // 房间孪生体
    if (chooseTwinData.code === 'ROOM') {
      const roomJson = {
        Building_data: {
          'user_id@': '/FLOOR/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Floor_data: {
          'user_id@': '/ROOM/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Room_data: {
          'user_id@': '/ROOM/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
      };
      defaultJson = Object.assign(defaultJson, roomJson);
    }
    // 楼层孪生体
    if (chooseTwinData.code === 'FLOOR') {
      const floorJson = {
        Building_data: {
          'user_id@': '/FLOOR/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Floor_data: {
          'user_id@': '/FLOOR/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        BUILDING: {
          '@order': 'create_time-,uuid-',
        },
      };
      defaultJson = Object.assign(defaultJson, floorJson);
    }
    // 建筑孪生体
    if (chooseTwinData.code === 'BUILDING') {
      const roomJson = {
        Building_data: {
          'user_id@': '/BUILDING/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
      };
      defaultJson = Object.assign(defaultJson, roomJson);
    }
    // 菜单孪生体
    if (chooseTwinData.code === 'MENU') {
      const menuJson = {
        Sys_menu: {
          'id@': '/MENU/menu_id',
          '@column': 'id;name',
        },
      };
      defaultJson = Object.assign(defaultJson, menuJson);
    }
    // 地图层级和地图矢量数据
    if (chooseTwinData.code === 'MAP_LEVEL' || chooseTwinData.code === 'MAP_VECTOR_DATA') {
      const mapLevelJson = {
        Map_level_data: {
          'id@': `/${chooseTwinData.code}/map_level_id`,
          '@column': 'id;name',
        },
      };
      defaultJson = Object.assign(defaultJson, mapLevelJson);
    }
    requestJson['[]'] = Object.assign(requestJson['[]'], defaultJson);
  }
  //如果是默认孪生体 得建筑，楼层，房间 高级查询
  if (isDefault) {
    let buserids = [];
    let fuserids = [];
    if (queryParam.Building_data) {
      //查询过滤出来的建筑
      const buildJson = {
        '[]': {
          BUILDING: {
            user_id$: `%${queryParam.Building_data}%`,
            '@order': 'create_time-,uuid-',
          },
        },
        'total@': '/[]/total',
        'info@': '/[]/info',
      };
      const firstStep = await getTwinData(buildJson);
      buserids = firstStep['[]']?.map((bitm: any) => bitm.BUILDING).map((build: any) => build.user_id);
      if (!buserids) {
        tabData.value = [];
        loading.value = false;
        return;
      }
      requestJson['[]']['FLOOR']['parent_cbid{}'] = buserids;
    }
    if (queryParam.Floor_data) {
      //查询过滤出来的建筑
      const floorJson = {
        '[]': {
          FLOOR: {
            user_id$: `%${queryParam.Floor_data}%`,
            '@order': 'create_time-,uuid-',
          },
        },
        'total@': '/[]/total',
        'info@': '/[]/info',
      };
      if (queryParam.Building_data) {
        //@ts-ignore
        floorJson['[]']['FLOOR']['parent_cbid{}'] = buserids ? buserids : [];
      }
      const sedStep = await getTwinData(floorJson);
      fuserids = sedStep['[]']?.map((bitm: any) => bitm.FLOOR).map((floor: any) => floor.user_id);
      if (!fuserids) {
        tabData.value = [];
        loading.value = false;
        return;
      }
      requestJson['[]']['ROOM']['parent_cbid{}'] = fuserids;
    }
  }
  console.log('requestJson', requestJson);
  getTwinData(requestJson).then((res: any) => {
    if (res.code === 200 && res['[]']) {
      paginationConfig.value.total = res.info.total;
      let resData = res['[]'];
      resData = resData.map((ele: any) => {
        // 场景数据名称
        let sceneDataName = ele[`${table}`].user_id;
        // 建筑名称
        let buildingName = ele[`${table}`].user_building_id;
        if (buildingName && ele.Building_data) {
          const name = ele.Building_data.setting_name ? ele.Building_data.setting_name : ele.Building_data.name;
          buildingName = `${name}[${ele.Building_data.user_id}]`;
          sceneDataName = buildingName;
        } else if (buildingName && ele.BUILDING) {
          buildingName = `[${ele.BUILDING.user_id}]`;
          sceneDataName = buildingName;
        }
        // 楼层名称
        let floorName = ele[`${table}`].user_floor_id;
        if (floorName && ele.Floor_data) {
          const name = ele.Floor_data.setting_name ? ele.Floor_data.setting_name : ele.Floor_data.name;
          floorName = `${name}[${ele.Floor_data.user_id}]`;
          sceneDataName = floorName;
        } else if (floorName && ele.FLOOR) {
          floorName = `[${ele.FLOOR.user_id}]`;
          sceneDataName = floorName;
        }
        // 地图层级名称
        let mapLevelName;
        if (ele.Map_level_data) {
          mapLevelName = ele.Map_level_data.name;
        }
        // 菜单名称
        let menuName;
        if (ele.Sys_menu) {
          menuName = ele.Sys_menu.name;
        }
        // 房间名称
        let roomName = ele[`${table}`].user_room_id;
        if (roomName && ele.Room_data) {
          const name = ele.Room_data.setting_name ? ele.Room_data.setting_name : ele.Room_data.name;
          roomName = `${name}[${ele.Room_data.user_id}]`;
          sceneDataName = roomName;
        }
        const obj = {
          ...ele[table],
          sceneName: ele.Scene_record ? ele.Scene_record.name : ele[`${table}`].scene_id,
          sceneDataName,
          buildingName,
          floorName,
          roomName,
          mapLevelName,
          menuName,
        };
        sourceType.value.some((item: any) => {
          if (item.code === obj.data_source) {
            obj.data_source_source = obj.data_source;
            obj.data_source = item.value;
            return true;
          }
          return false;
        });
        obj.iconStatus = obj.gcj02_position || obj.wgs84_position;
        obj.create_time = obj.create_time.replace('T', ' ');
        obj.update_time = obj.update_time.replace('T', ' ');
        obj.create_user = ele['Sys_user:createUser'] ? ele['Sys_user:createUser'].name : '';
        obj.update_user = ele['Sys_user:updateUser'] ? ele['Sys_user:updateUser'].name : '';
        obj.loadding = false;
        return obj;
      });
      tabData.value = resData;
      loading.value = false;
    } else {
      tabData.value = [];
    }
    console.log('tabData.value', tabData.value);
    loading.value = false;
    emits('dataChange', tabData.value);
  });
};

// 分页变化
const paginationChange = (current: number, pageSize: number) => {
  paginationConfig.value = Object.assign(paginationConfig.value, {
    current,
    pageSize,
  });
  getData();
};

// 选中的列表记录
const tableSelects = ref<{
  keys: Array<any>;
  list: Array<any>;
}>({
  keys: [],
  list: [],
});
// 行选择改变
const rowSelection = {
  onChange: (selectedRowKeys: Array<any>, selectedRows: Array<any>) => {
    tableSelects.value.keys = selectedRowKeys;
    tableSelects.value.list = selectedRows;
    emits('selectChange', tableSelects.value);
  },
  getCheckboxProps: (record: any) => ({
    disabled: record.groupId === DEFAULT_TWIN_CLASS_GROUP_ID,
  }),
};
const tableChange = (pag: any, filters: any, sorter: any) => {
  let param = {};
  if (sorter.order) {
    param = {
      sortField: sorter.field,
      sortRule: sorter.order === 'descend' ? 'DESC' : 'ASC',
    };
  }
  getData(param);
};

const reset = () => {
  tableCurrRowUuid.value = '';
  queryParam = {
    searchValue: '',
    scene_id: '', // 所属场景
    data_source: '', // 点位数据来源
    Building_data: '', // 所属建筑
    Floor_data: '', // 所属楼层
    Room_data: '', // 房间
    scene_ids: [], // 历史版本所有场景id
  };
  clickSearch();
};

const downloadFileSlotNames = ref<Array<string>>([]); // 文件
const releativeSlotNames = ref<Array<string>>([]); // 关联孪生体
const editorSlotName = ref<Array<string>>([]); // 富文本
const selectOrCheckbox = ref<any>([]); // 多选框
// 获取表头
const getColumns = () => {
  let fixColunm: any = [
    { code: 'uuid', value: '唯一标识' },
    { code: 'data_source', value: '点位数据来源' },
    { code: 'scene_id', value: '所属场景' },
    { code: 'current_level', value: '所在层级' },
    { code: 'user_building_id', value: '所属建筑' },
    { code: 'user_floor_id', value: '所属楼层' },
    { code: 'user_room_id', value: '所属房间' },
    { code: 'wgs84_position', value: 'WGS84坐标系' },
    { code: 'gcj02_position', value: 'GCJ02火星坐标系' },
    { code: 'gis_height', value: '离地高度' },
    { code: 'parent_user_id', value: '所在层级ID' },
    { code: 'position_body', value: '点位数据信息' },
    { code: 'position', value: '位置坐标' },
    { code: 'create_time', value: '创建时间' },
    { code: 'create_user', value: '创建人' },
    { code: 'update_time', value: '更新时间' },
    { code: 'update_user', value: '更新人' },
  ].map((item) => ({ dataIndex: item.code, title: item.value }));

  const tableInfo = JSON.parse(chooseTwinData.structure);
  const formList = chooseTwinData.form ? JSON.parse(chooseTwinData.form) : { list: [] };
  filelds = formList.list;
  getFilterBar(formList.list);

  // 要排除的固定列
  let excludeColumnNames = ['wgs84_position', 'gcj02_position', 'position_body', 'parent_user_id', 'position'];
  // 地图层级的孪生体排除的固定列
  if (chooseTwinData.level === 'MAP') {
    const mapExcludeColumnNames = ['scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id'];
    excludeColumnNames = [...excludeColumnNames, ...mapExcludeColumnNames];
  }
  // 默认孪生体排除的固定列
  if (chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID) {
    const defaultExcludeColumnNames = ['data_source', 'scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id', 'gis_height'];
    excludeColumnNames = [...excludeColumnNames, ...defaultExcludeColumnNames];
  }
  // 表单孪生体排除的固定列
  if (chooseTwinData.dataType === 'FORM') {
    const formExcludeColumnNames = ['data_source', 'scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id', 'gis_height'];
    excludeColumnNames = [...excludeColumnNames, ...formExcludeColumnNames];
  }
  // 获取自定义的列
  const fixedColumnNames = fixColunm.map((e: any) => e.dataIndex);
  const tabColum = tableInfo?.column || tableInfo?.table?.column || [];
  const customizeColumns = tabColum.filter((e: any) => !fixedColumnNames.includes(e.name)).map((e: any) => ({ dataIndex: e.name, title: e.comment })) || [];
  const index = fixColunm.findIndex((e: any) => e.dataIndex === 'create_time');
  if (index !== -1) {
    fixColunm.splice(index, 0, ...customizeColumns);
  }
  // 排除固定列
  fixColunm = fixColunm.filter((e: any) => !excludeColumnNames.includes(e.dataIndex));
  // 给房间默认孪生体添加字段
  if (chooseTwinData.code === 'ROOM') {
    fixColunm.splice(2, 0, { dataIndex: 'user_building_id', title: '所属建筑' }, { dataIndex: 'user_floor_id', title: '所属楼层' });
  }
  // 给楼层默认孪生体添加字段
  if (chooseTwinData.code === 'FLOOR') {
    fixColunm.splice(2, 0, {
      dataIndex: 'user_building_id',
      title: '所属建筑',
    });
  }
  // 给地图层级孪生体和地图矢量孪生体添加字段
  if (chooseTwinData.code === 'MAP_LEVEL' || chooseTwinData.code === 'MAP_VECTOR_DATA') {
    fixColunm.splice(1, 0, {
      dataIndex: 'cb_mapLevel_name',
      title: '地图层级名称',
    });
  }
  // 给xxv菜单孪生体添加字段
  if (chooseTwinData.code === 'MENU') {
    fixColunm.splice(1, 0, { dataIndex: 'cb_menu_name', title: '菜单名称' });
  }
  fixColunm.forEach((ele: any) => {
    // 替换的展示列映射
    const replaceColumnMap: any = {
      scene_id: 'sceneName',
      user_building_id: 'buildingName',
      user_floor_id: 'floorName',
      user_room_id: 'roomName',
      scene_code: 'sceneName',
      user_id: 'sceneDataName',
      cb_mapLevel_name: 'mapLevelName',
      cb_menu_name: 'menuName',
    };

    if (ele.dataIndex === 'scene_code') {
      ele.title = '所属场景';
    }
    if (ele.dataIndex === 'user_id') {
      ele.title = '名称';
    }
    ele.dataIndex = replaceColumnMap[ele.dataIndex] ? replaceColumnMap[ele.dataIndex] : ele.dataIndex;
    ele.sorter = true;
    ele.sortDirections = ['descend', 'ascend'];
    ele.width = 180;
    ele.ellipsis = true;

    formList.list.forEach((item: any) => {
      if (item.model === ele.dataIndex) {
        if (item.type === 'uploadFile') {
          const downloadFileSlotName = `fileDownload-${item.model}`;
          ele.key = `fileDownload-${item.model}`;
          downloadFileSlotNames.value.push(downloadFileSlotName);
        }
        if (item.type === 'releative') {
          const releativeFileSlotName = `releative-${item.model}`;
          ele.key = `releative-${item.model}`;
          releativeSlotNames.value.push(releativeFileSlotName);
        }
        if (item.type === 'editor') {
          const editorFileSlotName = `editor-${item.model}`;
          ele.key = editorFileSlotName;
          editorSlotName.value.push(editorFileSlotName);
        }
        if (item.type === 'select' || item.type === 'checkbox') {
          const selectOrCheckboxSlotName = `selectOrCheckbox-${item.model}`;
          ele.key = selectOrCheckboxSlotName;
          selectOrCheckbox.value.push(selectOrCheckboxSlotName);
        }
        delete ele.sorter;
        delete ele.sortDirections;
      }
    });
  });
  let width = 160;
  // 是否是默认孪生体
  const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
  if (isDefault) {
    width = 60;
  } else {
    width = 110;
  }
  fixColunm.push({
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width,
    fixed: 'right',
  });
  columns.value = fixColunm;

  // 非表单增加离地高度字段
  const formObj = JSON.parse(chooseTwinData.form);
  if (chooseTwinData.level !== 'OTHER' && chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID) {
    formObj.list.splice(1, 0, {
      type: 'number',
      label: '离地高度(m)',
      options: {
        width: '100%',
        defaultValue: 0,
        min: -10000,
        max: 10000,
        precision: 2,
        step: 0.05,
        hidden: false,
        disabled: false,
        placeholder: '请输入',
        unique: false,
      },
      model: 'gis_height',
      key: 'gis_height',
      help: '',
      rules: [{ required: false, message: '必填项' }],
    });
  }
  formJson.value = JSON.stringify(formObj);
};

// 获取需要筛选的组件
const getFilterBar = (list: any) => {
  const select: any = [];
  const search: any = [];
  list.forEach((v: any) => {
    if ((v.type === 'radio' || v.type === 'checkbox' || v.type === 'select') && v.options.isFilter === '1') {
      select.push(v);
    }
    if ((v.type === 'input' || v.type === 'date' || v.type === 'number' || v.type === 'textarea') && v.options.isFilter === '1') {
      search.push(v);
    }
  });
  selects.value = select;
  searchs.value = search;
};

// 更新孪生体数据
const addEditFormRef = ref();
const handleEdit = (data: any) => {
  addEditFormRef.value.init(data, formJson.value, {
    table: chooseTwinData.code,
    dataType: chooseTwinData.dataType,
  });
};
// 删除
const twinDataDelete = (uuids: Array<string>) => {
  const table = chooseTwinData.code;
  deleteTwinData({
    [table]: {
      'uuid{}': uuids,
    },
  }).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '删除成功');
      getData();
    } else {
      useGlobalMessage('success', `删除失败：${res.msg}`);
    }
  });
};

// 富文本预览
const editorDialog = reactive({
  title: '',
  show: false,
  content: '',
});
const showEditor = (text: string) => {
  editorDialog.title = '预览';
  editorDialog.show = true;
  editorDialog.content = text;
};

// 关联孪生体
const reletiveRef = ref();
const showReletive = (record: any, column: any) => {
  try {
    const data = JSON.parse(record[column.dataIndex]);
    if (data.length) {
      reletiveRef.value.init(JSON.parse(record[column.dataIndex]), column.dataIndex, chooseTwinData);
    }
  } catch (err) {
    console.error(err);
  }
};
// 所在层级字典数据
const levelData = ref<any[]>([]);
// 获取所在层级字典
const levelDictionary = () => {
  sysDictTypeDropDown({ code: 'TWIN_DATA_CURRENT_LEVEL' }).then((res) => {
    levelData.value = res.data || [];
  });
};
levelDictionary();
// 设置当前选中的行
const setCurrentRow = (uuid: string) => {
  tableCurrRowUuid.value = uuid;
};
defineExpose({ init, getData, setCurrentRow });
</script>
<style lang="scss" scoped>
.content.keep-px {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--primary-bg-color);

  .search-btns {
    justify-content: end;
  }

  .search-input {
    width: 180px !important;
  }

  .table-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;

    .table-content {
      flex: 1;
      overflow-y: auto;

      .table {
        height: calc(100% - 40px);

        ::v-deep .ant-table-row.active td {
          background-color: var(--table-active-bg-color);
        }
      }

      .pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        text-align: right;
        white-space: nowrap;

        ::v-deep .ant-pagination {
          padding: 0;
        }
      }
    }
  }
}
</style>
