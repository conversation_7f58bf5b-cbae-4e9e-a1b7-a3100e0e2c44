package com.uino.x.pedestal.app.scenex.config;

import cn.hutool.cron.CronUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.uino.x.common.core.factory.ExecutorServiceFactory;
import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.common.datasource.util.DataSourceConfigurationUtils;
import com.uino.x.common.datasource.util.TenantDataSource;
import com.uino.x.common.label.annotation.aop.RedissonLock;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.tool.base.BeanUtils;
import com.uino.x.common.tool.base.CollectionUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.tenant.common.enums.MigrationTypeEnum;
import com.uino.x.pedestal.tenant.dao.mapper.TenantInfoMapper;
import com.uino.x.pedestal.tenant.migrate.TenantMigrationInfo;
import com.uino.x.pedestal.tenant.migrate.factory.TenantMigrationFactory;
import com.uino.x.pedestal.tenant.pojo.entity.TenantInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;

/**
 * 项目初始化配置
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2021/12/3 18:34
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class InitConfigRunner implements ApplicationRunner {

    private final TenantInfoMapper tenantInfoMapper;
    private final JdbcTemplate jdbcTemplate;

    @Override
    public void run(ApplicationArguments args) {
        // 租户数据初始化
        AsyncUtils.asyncExecutor(() -> SpringIocUtils.mustGetBean(this.getClass()).initTenantData());
    }

    @RedissonLock(value = "scenex_init", enableException = false)
    public void initTenantData() {
        if (0 == this.tenantInfoMapper.tableCount()) {

            return;
        }
        final LambdaQueryWrapper<TenantInfo> queryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode());
        // 启动定时任务调度器
        CronUtil.getScheduler()
                // 设置秒级别的启用
                .setMatchSecond(true)
                // 启动定时器执行器
                .start();
        // 查询出所有租户
        final List<TenantInfo> list = tenantInfoMapper.selectList(queryWrapper);
        final int size = list.size();
        final ExecutorService threadPool = ExecutorServiceFactory.createThreadPool(InitConfigRunner.class, size, new ArrayBlockingQueue<>(Math.max(1, size >> 2)));
        AsyncUtils.asyncExecutor(list, tenantInfo -> {
            // 获取租户code
            final String tenantInfoCode = tenantInfo.getCode().toLowerCase();
            // 创建供flyway使用的数据源
            try (TenantDataSource dataSource = DataSourceConfigurationUtils.newTenantDataSource(tenantInfoCode, DataSourceConstant.SCENE_X)) {
                final TenantMigrationInfo tenantMigrationInfo = BeanUtils.copyToBean(tenantInfo, TenantMigrationInfo.class);
                tenantMigrationInfo.setMigrationType(MigrationTypeEnum.INIT.getCode());
                tenantMigrationInfo.setTargetTenant(tenantInfoCode);
                tenantMigrationInfo.setSourceType(DataSourceConstant.SCENE_X);
                // 初始化flyway最新的数据
                TenantMigrationFactory.of(dataSource, tenantMigrationInfo).get().migrate();
                log.info("{} 租户数据初始化成功", tenantInfoCode);
            }
            // 创建租户使用的数据源
            DataSourceConfigurationUtils.newTenantDataSourcesAddRouting(tenantInfoCode, DataSourceConstant.SCENE_X);
        }, threadPool);
        threadPool.shutdown();
        final String schema = DataSourceConstant.SCENE_X;
        // 修正字符排序 utf8mb4_0900_ai_ci -> utf8mb4_general_ci
        try {
            fixCollate(schema, "utf8mb4_0900_ai_ci", "utf8mb4_general_ci", 1000);
        } catch (Exception e) {
            log.error("修正字符排序失败, {}", e.getMessage());
        }
        log.info("{} 初始化成功", schema);
    }

    /**
     * 修正字符排序
     */
    private void fixCollate(String schema, String source, String target, int offset) {
        final String ll = "%" + schema;
        // table
        final String queryCollateSql = String.format("""
                SELECT
                    CONCAT( 'ALTER TABLE `', TABLE_SCHEMA, '`.`', TABLE_NAME, '`', ' COLLATE=%s' ) 'table_collate_sql'
                FROM
                    information_schema.`TABLES`
                WHERE
                    TABLE_COLLATION = '%s' AND TABLE_SCHEMA LIKE '%s' LIMIT ?;
                """, target, source, ll);
        int tableTotal = 0;
        List<String> tableCollateSqlList = jdbcTemplate.queryForList(queryCollateSql, String.class, offset);
        while (CollectionUtils.isNotEmpty(tableCollateSqlList)) {
            tableTotal += tableCollateSqlList.size();
            jdbcTemplate.batchUpdate(tableCollateSqlList.toArray(String[]::new));
            tableCollateSqlList = jdbcTemplate.queryForList(queryCollateSql, String.class, offset);
        }
        log.info("Table collate total count: {}", tableTotal);
        // column
        final String queryColumnCollateSql = String.format("""
                SELECT
                  CONCAT('ALTER TABLE `', TABLE_SCHEMA, '`.`', TABLE_NAME, '`',
                         ' MODIFY `', COLUMN_NAME, '` ', COLUMN_TYPE,
                         ' COLLATE %s',
                         IF(IS_NULLABLE = 'NO', ' NOT NULL', ' NULL'),
                         IF(COLUMN_DEFAULT IS NOT NULL, CONCAT(' DEFAULT ''', COLUMN_DEFAULT, ''''),
                            IF(IS_NULLABLE = 'YES', ' DEFAULT NULL', '')),
                         IF(EXTRA != '', CONCAT(' ', EXTRA), ''),
                         IF(COLUMN_COMMENT != '', CONCAT(' COMMENT ''', REPLACE(COLUMN_COMMENT, "'", "''"), ''''), '')) AS 'column_collate_sql'
                FROM
                  information_schema.`COLUMNS`
                WHERE
                  TABLE_NAME NOT IN (
                    SELECT TABLE_NAME
                    FROM information_schema.`VIEWS`
                    WHERE TABLE_SCHEMA LIKE '%s'
                  )
                  AND COLLATION_NAME = '%s'
                  AND TABLE_SCHEMA LIKE '%s' LIMIT ?;
                """, target, ll, source, ll);
        int columnTotal = 0;
        List<String> columnCollateSqlList = jdbcTemplate.queryForList(queryColumnCollateSql, String.class, offset);
        while (CollectionUtils.isNotEmpty(columnCollateSqlList)) {
            columnTotal += columnCollateSqlList.size();
            jdbcTemplate.batchUpdate(columnCollateSqlList.toArray(String[]::new));
            columnCollateSqlList = jdbcTemplate.queryForList(queryColumnCollateSql, String.class, offset);
        }
        log.info("Column collate total count: {}", columnTotal);
    }
}
