package com.uino.x.pedestal.twin.impl.ddl;


import com.uino.x.common.tool.base.CollectionUtils;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.common.utils.ColumnUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.Getter;

import java.util.List;
import java.util.Optional;

/**
 * Create ddl
 *
 * <AUTHOR>
 * @version 1.0.3
 * @date 2022/1/5 18:19
 */
@Getter
public class CreateJrmDdl extends AbstractJrmDdl implements JrmDdl {

    CreateIndexJrmDdl createIndexJrmDdl;

    public CreateJrmDdl(AutoTableTypeEnum autoTableType, String tableName, List<FormItem> formItem, String sourceStructure) {
        super(autoTableType, tableName, formItem, sourceStructure);
    }

    public CreateJrmDdl(AutoTableTypeEnum autoTableType, String tableName, List<FormItem> formItem) {
        super(autoTableType, tableName, formItem, JrmJsonUtils.createTableStructure(AutoTableTypeEnum.TWIN, tableName, ColumnUtils.parseFormToColumn(formItem)));
    }

    @Override
    public JrmDdl parse() {
        final List<FormItem> indexes = this.formItem.stream().filter(fi -> StringUtils.isNotBlank(fi.getOptions().getIndexType()))
                .toList();
        if (CollectionUtils.isNotEmpty(indexes)) {
            this.createIndexJrmDdl = new CreateIndexJrmDdl(this.autoTableType, this.tableName, indexes);
        }
        return this;
    }

    @Override
    public void invoke() {
        try {
            JrmBean.request(RequestType.CREATE, this.sourceStructure);
            Optional.ofNullable(this.createIndexJrmDdl).ifPresent(cij -> cij.parse().invoke());
        } catch (Exception e) {
            new DropJrmDdl(autoTableType, tableName, formItem, sourceStructure).invoke();
            throw e;
        }
    }
}
