package com.uino.x.edtap.dao.project;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.edtap.pojo.project.entity.SysProject;
import com.uino.x.edtap.pojo.project.param.SysProjectParam;
import com.uino.x.edtap.pojo.project.param.SysProjectQueryParam;
import com.uino.x.edtap.pojo.project.vo.SysProjectCountVo;
import com.uino.x.edtap.pojo.project.vo.SysProjectVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface SysProjectMapper extends BaseMapper<SysProject> {

    @Select({ "<script>" +
            " select sector_id as sectorId,count(sector_id) as count, sdd.value as sectorName" +
            " from sys_project sp left join sys_dict_data sdd on sdd.id = sp.sector_id" +
            " <where>" +
            " <if test='sysProjectParam.enterpriseId!= null and sysProjectParam.enterpriseId !=\"\" '>" +
            " and sp.enterprise_id=#{sysProjectParam.enterpriseId}" +
            " </if>" +
            " <if test='sysProjectParam.name!=null and sysProjectParam.name !=\"\" '>" +
            " and sp.name like concat('%', #{sysProjectParam.name}, '%')" +
            " </if>" +
            " </where>" +
            "group by sector_id with rollup;" +
            "</script>"
    })
    List<SysProjectCountVo> getCountInfo(@Param("sysProjectParam") SysProjectQueryParam sysProjectQueryParam);

    List<SysProject> list(SysProjectParam param);

    List<SysProjectVo> projectList(SysProjectQueryParam param);

    Integer queryCount(SysProjectQueryParam param);

    /**
     * 获取项目场景数量
     *
     * @param tenantCode 租户代码
     * @return 场景数量
     */
    @DS("#tenantCode")
    @Select({"select count(*) from ${tenantCode}_scenex.scene_record where status = 0 and default_name = 'World' and version = 1"})
    Integer getProjectSceneCount(@Param("tenantCode") String tenantCode);


    /**
     * 获取项目用户数量
     *
     * @param tenantCode 租户代码
     * @return 用户数量
     */
    @DS("#tenantCode")
    @Select({"select count(*) from ${tenantCode}_systemx.sys_user where status = 0 and admin_type != 1"})
    Integer getProjectUserCount(@Param("tenantCode") String tenantCode);


    /**
     * 根据企业ID获取项目列表
     *
     * @param enterpriseId 企业ID
     * @return 项目列表
     */
    default List<SysProject> listByEnterpriseId(Long enterpriseId) {
        return selectList(Wrappers.<SysProject>lambdaQuery()
                .eq(SysProject::getEnterpriseId, enterpriseId)
                .ne(SysProject::getStatus, StatusEnum.DELETED.getCode())
                .select(SysProject::getId, SysProject::getName, SysProject::getStatus, SysProject::getCode));
    }
}
