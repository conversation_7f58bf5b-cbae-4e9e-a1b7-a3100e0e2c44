package com.uino.x.pedestal.twin.jrm.sql.definition;


import com.uino.x.pedestal.twin.jrm.core.domain.JsonKey;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.AbstractSqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.structure.AutoIncrementSortList;
import com.uino.x.pedestal.twin.jrm.sql.executor.RedisSqlExecutor;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * ddl alter相关sql描述
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/12 16:47
 */
public class AlterSqlDefinition extends AbstractSqlDefinition {

    public AlterSqlDefinition(DataSource dataSource, JsonDefinition jsonDefinition) {

        super(dataSource, jsonDefinition, RequestType.ALTER);
    }

    @Override
    public void buildSql() {
        super.buildSql();
        final JsonKey old = this.jsonDefinition.getJsonKey("/table/~column/old");
        final JsonKey name = this.jsonDefinition.getJsonKey("/table/~column/name");
        if (Objects.nonNull(old) && Objects.nonNull(name)) {
            if (Objects.equals(old.getValue(), name.getValue())) {
                final String stmts = this.sql.toString();
                this.sql.delete(0, this.sql.length());
                for (String stmt : stmts.split(";")) {
                    final String lowerCase = stmt.toLowerCase();
                    if (!(lowerCase.contains("alter table") && lowerCase.contains("rename column"))) {
                        this.sql.append(stmt.trim()).append(";");
                    }
                }
            }
        }
    }

    @Override
    public AutoIncrementSortList<JsonSql> initJsonSqlList(Integer version) {

        // 通过select查询出 alter json_sql_map 数据
        final String session = jsonDefinition().getSessionId();
        return RedisSqlExecutor
                .selectJsonSqlMapList(dataSource(), RequestType.ALTER, version, session);
    }

}
