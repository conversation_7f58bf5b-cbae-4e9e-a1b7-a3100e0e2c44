<!--
 * @Description: 功能示例在线编辑
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-07-03 18:58:49
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 15:43:23
-->
<template>
  <div class="online-editor keep-px">
    <div class="editor-wrap" :class="{ full: fullScreen }">
      <div class="editor-header">
        <div class="sample-name">{{ sampleDetail.name }}</div>
        <div class="sample-control">
          <div class="control-icon" title="下载" @click="clickDownload" v-if="hasPerm('sys-sample:download-sample')">
            <download-outlined title="下载" />
          </div>
          <div class="control-icon" title="还原" @click="clickReset">
            <RollbackOutlined title="还原" />
          </div>
          <div class="control-icon" title="运行" @click="clickRun">
            <CaretRightOutlined title="运行" />
          </div>
        </div>
      </div>
      <div class="editor-tab">
        <div class="editor-tab-item" :class="{ active: curTab === 'html' }" @click="clickTab('html')">HTML</div>
        <div class="editor-tab-item" :class="{ active: curTab === 'js' }" @click="clickTab('js')">JS</div>
        <div class="editor-tab-item" :class="{ active: curTab === 'css' }" @click="clickTab('css')">CSS</div>
      </div>
      <div class="sample-editor">
        <div v-show="curTab === 'html'" id="sample-editor-html"></div>
        <div v-show="curTab === 'js'" id="sample-editor-js"></div>
        <div v-show="curTab === 'css'" id="sample-editor-css"></div>
      </div>
    </div>
    <div class="preview-wrap" :class="{ full: fullScreen }">
      <iframe id="sample-preview" frameborder="0"></iframe>
      <div class="full-screen">
        <div v-show="!fullScreen" class="control-icon" title="三维全屏" @click="clickFullScreen(true)">
          <fullscreen-outlined />
        </div>
        <div v-show="fullScreen" class="control-icon" title="三维还原" @click="clickFullScreen(false)">
          <fullscreen-exit-outlined />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import JsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import CssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import HtmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import TsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import * as monaco from 'monaco-editor';
import { useRoute } from 'vue-router';
import { nextTick, ref, onBeforeUnmount } from 'vue';
import axios from 'axios';
import { getSampleDetail, downloadSample } from '@/api/portal/developer/sample';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';

const route = useRoute();
// 全屏
const fullScreen = ref(false);
const clickFullScreen = (flag: boolean) => {
  fullScreen.value = flag;
};
const id = ref('');
// 监听路由数据变化
watch(
  () => route.query,
  (newVal: any) => {
    if (newVal && newVal.id) {
      nextTick(() => {
        getDetailData(newVal.id);
        id.value = newVal.id;
      });
    }
  },
  {
    immediate: true,
  }
);
// 功能示例详情
const sampleDetail = ref({
  name: '',
});
// 基础地址
const baseUrl = ref('');
// 获取详情
const getDetailData = (newVal: string) => {
  getSampleDetail({ id: newVal }).then((res: any) => {
    if (res.code === 200) {
      sampleDetail.value = res.data;
      // 处理url结尾的斜杠
      const url = res.data.url.endsWith('/') ? res.data.url.slice(0, -1) : res.data.url;
      baseUrl.value = `${window.config.previewUrl}${url}`;
      initEditor(url);
    }
  });
};
// 切换tab
const clickTab = (newVal: string) => {
  curTab.value = newVal;
};
// 当前选中的tab
const curTab = ref('html');
const iframeData = ref('');
let jsModel: any = null;
let htmlModel: any = null;
let cssModel: any = null;
// 变化的数据
let changeHtmlData = '';
// 原始数据
let originHtmlData = '';
// 变化的数据
let changeJsData = '';
// 原始数据
let originJsData = '';
// 变化的数据
let changeCssData = '';
// 原始数据
let originCssData = '';
// 编辑器实例
let jsEditor: monaco.editor.IStandaloneCodeEditor;
let htmlEditor: monaco.editor.IStandaloneCodeEditor;
let cssEditor: monaco.editor.IStandaloneCodeEditor;

// 销毁编辑器
onBeforeUnmount(() => {
  jsEditor && jsEditor.dispose();
  cssEditor && cssEditor.dispose();
  htmlEditor && htmlEditor.dispose();
});
// @ts-ignore
// eslint-disable-next-line no-restricted-globals
self.MonacoEnvironment = {
  getWorker(_: string, label: string) {
    if (label === 'json') {
      return new JsonWorker();
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new CssWorker();
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new HtmlWorker();
    }
    if (['typescript', 'javascript'].includes(label)) {
      return new TsWorker();
    }
    return new EditorWorker();
  },
};
nextTick(() => {
  monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: true,
    noSyntaxValidation: false,
  });
  monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2016,
    allowNonTsExtensions: true,
  });
});

// 点击运行
const clickRun = () => {
  if (jsEditor) {
    changeJsData = jsEditor.getValue();
  }
  if (cssEditor) {
    changeCssData = cssEditor.getValue();
  }
  if (htmlEditor) {
    changeHtmlData = htmlEditor.getValue();
  }
  getIframeDoc();
};
// 获取iframe src doc数据
const getIframeDoc = () => {
  let result = '';
  if (changeHtmlData) {
    result = changeHtmlData;
    if (changeCssData) {
      const cssTag = '<link rel="stylesheet" href="./css/index.css">';
      const cssIndex = changeHtmlData.indexOf(cssTag);
      if (cssIndex > -1) {
        result = `${changeHtmlData.slice(0, cssIndex)}<style>${changeCssData}</style>${changeHtmlData.slice(cssIndex + cssTag.length)}`;
      }
    }
    if (changeJsData) {
      // @ts-ignore
      // eslint-disable-next-line no-useless-escape
      const jsTag = '<script src="./js/index.js"><\/script>';
      const jsIndex = result.indexOf(jsTag);
      if (jsIndex > -1) {
        // eslint-disable-next-line no-useless-escape
        result = `${result.slice(0, jsIndex)}<script>${changeJsData}<\/script>>${result.slice(jsIndex + jsTag.length)}`;
      }
    }
  }
  iframeData.value = result;
  const iframeDom = document.getElementById('sample-preview');
  // @ts-ignore
  iframeDom.src = '';
  iframeDom.onload = () => {
    // @ts-ignore
    iframeDom.contentWindow.document.write(result);
  };
};

// 点击下载
const clickDownload = () => {
  downloadSample({ id: id.value }).then((res) => {
    downloafFile(res);
  });
};
// 下载文件流
const downloafFile = async (res: any) => {
  try {
    // 创建一个下载链接
    const url = `${window.config.previewUrl}${res.data}`;
    // 创建一个隐藏的<a>标签并设置下载链接，模拟点击下载
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${sampleDetail.value.name}.zip`);
    document.body.appendChild(link);
    link.click();

    // 清理资源
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载文件时出现错误：', error);
  }
};
// 点击还原
const clickReset = () => {
  if (jsEditor) {
    jsEditor.setValue(originJsData);
    changeJsData = originJsData;
  }
  if (cssEditor) {
    cssEditor.setValue(originCssData);
    changeCssData = originCssData;
  }
  if (htmlEditor) {
    htmlEditor.setValue(originHtmlData);
    changeHtmlData = originHtmlData;
  }
  getIframeDoc();
};
// 初始化editor
const initEditor = (url: string) => {
  nextTick(async () => {
    originHtmlData = await getCode(`${window.config.previewUrl}${url}/index.html`, 'html');
    changeHtmlData = originHtmlData;
    htmlModel = monaco.editor.createModel(originHtmlData, 'html');
    htmlEditor = monaco.editor.create(document.getElementById('sample-editor-html') as HTMLElement, {
      model: htmlModel,
      language: 'html', // 语言支持自行查阅demo
      automaticLayout: true, // 自适应布局
      theme: 'vs-dark', // 官方自带三种主题vs, hc-black, or vs-dark
      foldingStrategy: 'indentation',
      renderLineHighlight: 'all', // 行亮
      selectOnLineNumbers: true, // 显示行号
      minimap: {
        enabled: false,
      },
      readOnly: false, // 只读
      fontSize: 16, // 字体大小
      scrollBeyondLastLine: false, // 取消代码后面一大段空白
      overviewRulerBorder: false, // 不要滚动条的边框
    });
    originCssData = await getCode(`${window.config.previewUrl}${url}/css/index.css`, 'css');
    changeCssData = originCssData;
    cssModel = monaco.editor.createModel(originCssData, 'css');
    cssEditor = monaco.editor.create(document.getElementById('sample-editor-css') as HTMLElement, {
      model: cssModel,
      language: 'css', // 语言支持自行查阅demo
      automaticLayout: true, // 自适应布局
      theme: 'vs-dark', // 官方自带三种主题vs, hc-black, or vs-dark
      foldingStrategy: 'indentation',
      renderLineHighlight: 'all', // 行亮
      selectOnLineNumbers: true, // 显示行号
      minimap: {
        enabled: false,
      },
      readOnly: false, // 只读
      fontSize: 16, // 字体大小
      scrollBeyondLastLine: false, // 取消代码后面一大段空白
      overviewRulerBorder: false, // 不要滚动条的边框
    });
    originJsData = await getCode(`${window.config.previewUrl}${url}/js/index.js`, 'js');
    changeJsData = originJsData;
    jsModel = monaco.editor.createModel(originJsData, 'javascript');
    jsEditor = monaco.editor.create(document.getElementById('sample-editor-js') as HTMLElement, {
      model: jsModel,
      language: 'javascript', // 语言支持自行查阅demo
      automaticLayout: true, // 自适应布局
      theme: 'vs-dark', // 官方自带三种主题vs, hc-black, or vs-dark
      foldingStrategy: 'indentation',
      renderLineHighlight: 'all', // 行亮
      selectOnLineNumbers: true, // 显示行号
      minimap: {
        enabled: false,
      },
      readOnly: false, // 只读
      fontSize: 16, // 字体大小
      scrollBeyondLastLine: false, // 取消代码后面一大段空白
      overviewRulerBorder: false, // 不要滚动条的边框
    });
    getIframeDoc();
    // 监听值的变化
    // editor.onDidChangeModelContent((val: any) => {
    //     originData.value = editor.getValue();
    // });
  });
};
// 根据url获取代码,type:html,js,css
const getCode = (url: string, type: string): any => {
  return new Promise((resolve) => {
    if (type === 'html') {
      fetch(url)
        .then((res) => {
          if (res.ok) {
            res.text().then((data) => {
              let result = data;
              if (data) {
                const headIndex = data.indexOf('<head>');
                if (headIndex > -1) {
                  result = `${data.slice(0, headIndex + 6)}\n    <base href='${baseUrl.value}'>${data.slice(headIndex + 6)}`;
                }
              }
              resolve(result);
            });
          } else {
            resolve('');
          }
        })
        .catch((err) => {
          resolve('');
        });
    } else if (type === 'js' || type === 'css') {
      fetch(url)
        .then((res) => {
          if (res.ok) {
            return res.text();
          }
          return '';
        })
        .then((data) => {
          if (data) {
            resolve(data);
          } else {
            resolve('');
          }
        })
        .catch((err) => {
          resolve('');
        });
    }
  });
};
</script>
<style scoped lang="scss">
.online-editor.keep-px {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .editor-wrap {
    width: 50%;
    height: 100%;
    overflow: hidden;
    transition: all 0.5s;

    &.full {
      width: 0;
    }

    .editor-header {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 10px;
      font-size: 16px;
      color: #fff;
      background-color: #1e1e1e;
      border-bottom: 1px solid #ecdede1a;

      .sample-control {
        display: flex;
      }

      .control-icon {
        width: 30px;
        height: 30px;
        font-size: 18px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;

        &:hover {
          background-color: #ecdede1a;
        }
      }
    }

    .editor-tab {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      height: 40px;
      padding: 0 10px;
      font-size: 16px;
      color: #fff;
      background-color: #1e1e1e;
      border-bottom: 1px solid #ecdede1a;

      .editor-tab-item {
        width: 80px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;

        &.active {
          color: orange;
          background-color: #382f2f;
        }
      }
    }

    .sample-editor {
      width: 100%;
      height: calc(100% - 80px);

      #sample-editor-html,
      #sample-editor-js,
      #sample-editor-css {
        width: 100%;
        height: 100%;
      }
    }
  }

  .preview-wrap {
    position: relative;
    width: 50%;
    height: 100%;
    background-color: #000;
    transition: all 0.5s;

    &.full {
      width: 100%;
    }

    #sample-preview {
      width: 100%;
      height: 100%;
    }

    .full-screen {
      position: absolute;
      right: 10px;
      bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      height: 26px;
      font-size: 22px;
      color: #fff;
      cursor: pointer;
      background-color: #291f1f;
    }
  }
}
</style>
