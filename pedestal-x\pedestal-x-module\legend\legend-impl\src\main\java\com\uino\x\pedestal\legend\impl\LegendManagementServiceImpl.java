package com.uino.x.pedestal.legend.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.uino.x.common.core.factory.PageFactory;
import com.uino.x.common.core.util.HttpServletUtils;
import com.uino.x.common.core.util.TenantUtils;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.label.enums.exp.ExpEnumOption;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.base.*;
import com.uino.x.pedestal.file.api.SysFileInfoApi;
import com.uino.x.pedestal.file.dao.mapper.SysFileInfoMapper;
import com.uino.x.pedestal.file.entity.SysFileInfo;
import com.uino.x.pedestal.file.vo.SysFileInfoVo;
import com.uino.x.pedestal.identity.dao.mapper.SysMenuMapper;
import com.uino.x.pedestal.identity.pojo.entity.SysMenu;
import com.uino.x.pedestal.legend.api.service.LegendManagementService;
import com.uino.x.pedestal.legend.common.constant.ConditionalConstant;
import com.uino.x.pedestal.legend.common.exception.LegendManagementException;
import com.uino.x.pedestal.legend.dao.mapper.LegendAndTwinMapper;
import com.uino.x.pedestal.legend.dao.mapper.LegendConditionalMapper;
import com.uino.x.pedestal.legend.dao.mapper.LegendManagementMapper;
import com.uino.x.pedestal.legend.impl.utils.LegendUtil;
import com.uino.x.pedestal.legend.pojo.entity.LegendAndTwin;
import com.uino.x.pedestal.legend.pojo.entity.LegendConditional;
import com.uino.x.pedestal.legend.pojo.entity.LegendManagement;
import com.uino.x.pedestal.legend.pojo.param.LegendConditionalParam;
import com.uino.x.pedestal.legend.pojo.param.LegendManagementBatchParam;
import com.uino.x.pedestal.legend.pojo.param.LegendManagementParam;
import com.uino.x.pedestal.legend.pojo.param.LegendXxvMenuTwinParam;
import com.uino.x.pedestal.legend.pojo.vo.LegendTwinDataVo;
import com.uino.x.pedestal.legend.pojo.vo.LegendTwinDateListVo;
import com.uino.x.pedestal.legend.pojo.vo.LegendXxvMenuTwinFiledVo;
import com.uino.x.pedestal.legend.pojo.vo.LegendXxvMenuTwinVo;
import com.uino.x.pedestal.twin.api.TwinClassApi;
import com.uino.x.pedestal.twin.common.constant.TwinFieldTypeConstant;
import com.uino.x.pedestal.twin.common.enums.TwinConditionsEnum;
import com.uino.x.pedestal.twin.common.utils.FormUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.dao.mapper.TwinBodyDataMapper;
import com.uino.x.pedestal.twin.dao.mapper.TwinClassMapper;
import com.uino.x.pedestal.twin.dao.mapper.TwinXxvMapper;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import com.uino.x.pedestal.twin.pojo.entity.TwinClass;
import com.uino.x.pedestal.twin.pojo.entity.TwinXxv;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @date 2022/5/12 18:02
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LegendManagementServiceImpl extends ServiceImpl<LegendManagementMapper, LegendManagement> implements LegendManagementService {

    /**
     * 图例关联孪生体条件mapper
     */
    private final LegendConditionalMapper legendConditionalMapper;
    /**
     * 图例关联孪生体mapper
     */
    private final LegendAndTwinMapper legendAndTwinMapper;
    private final LegendManagementMapper legendManagementMapper;
    private final TwinXxvMapper twinXxvMapper;
    private final TwinClassMapper twinClassMapper;
    private final SysMenuMapper sysMenuMapper;
    private final SysFileInfoMapper sysFileInfoMapper;
    private final SysFileInfoApi sysFileInfoApi;
    private final TwinBodyDataMapper twinBodyDataMapper;
    private final TwinClassApi twinClassApi;
    private final Executor httpExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLegendManagement(LegendManagementParam legendManagementParam) {
        LegendManagement legendManagement = BeanUtils.copyToBean(legendManagementParam, LegendManagement.class);
        if(CollectionUtils.isNotEmpty(legendManagementParam.getFiles())){
            legendManagement.setFiles(JSONObject.toJSONString(legendManagementParam.getFiles()));
        }

        LambdaQueryWrapper<LegendManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LegendManagement::getCode, legendManagement.getCode()).eq(LegendManagement::getStatus, StatusEnum.ENABLE.getCode());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new LegendManagementException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "图例编码重复"));
        }
        this.save(legendManagement);
        //获取新增的图例

        //新增图例关联孪生体
        legendManagementParam.getListTwin().forEach(e -> {
            LegendAndTwin legendAndTwin = BeanUtils.copyToBean(e, LegendAndTwin.class);
            legendAndTwin.setId(IdUtils.getId());
            legendAndTwin.setLegendId(legendManagement.getId());
            legendAndTwin.setTwinId(e.getTwinId());
            // 从 TwinClass 中获取 twinCode
            TwinClass twinClass = twinClassApi.getById(e.getTwinId());
            legendAndTwin.setTwinCode(twinClass != null ? twinClass.getCode() : "");
            legendAndTwinMapper.insert(legendAndTwin);
            //新增图例关联孪生体的条件
            List<LegendConditionalParam> legendConditionalParams = e.getListConditional();
            int conditionalsSize = legendConditionalParams.size();
            for (int j = 0; j < conditionalsSize; j++) {
                LegendConditional legendConditional = BeanUtils.copyToBean(legendConditionalParams.get(j), LegendConditional.class);
                legendConditional.setId(IdUtils.getId());
                legendConditional.setLegendAndTwinId(legendAndTwin.getId());
                // 填充 orderNum 和 conditionalType ; OrderNum倒序 ; Content内容为空就是逻辑条件
                legendConditional.setOrderNum((long) (conditionalsSize - j));
                legendConditional.setConditionalType(StringUtils.isNotBlank(legendConditional.getFieldName()) ? ConditionalConstant.GENERAL_CONDITIONAL : ConditionalConstant.LOGIC_CONDITIONAL);
                legendConditionalMapper.insert(legendConditional);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer delete(LegendManagementParam legendManagementParam) {

        Integer affected = deleteBatch(LegendManagementBatchParam.builder().ids(Collections.singletonList(legendManagementParam.getId())).build());
        AssertUtils.isTrue(affected <= 1, new LegendManagementException(HttpStatus.BAD_REQUEST, "com.uino.x.basex.modules.twin.service.impl.delete()删除影响行数大于1，可能删除意料之外的数据。"));
        return affected;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteBatch(LegendManagementBatchParam legendManagementBatchParam) {

        List<Long> legendManagementIds = legendManagementBatchParam.getIds();
        // ids为空
        if (CollectionUtils.isEmpty(legendManagementIds)) {
            return 0;
        }

        // 逻辑删除图例
        LegendManagement legendManagement = new LegendManagement();
        legendManagement.setStatus(StatusEnum.DELETED.getCode());
        int update = legendManagementMapper.update(legendManagement, new LambdaQueryWrapper<LegendManagement>().in(LegendManagement::getId, legendManagementIds));

        // 逻辑删除图例孪生体
        // 查询全部需要删除的图例孪生体
        List<LegendAndTwin> legendAndTwins = legendAndTwinMapper.selectList(new LambdaQueryWrapper<LegendAndTwin>().in(LegendAndTwin::getLegendId, legendManagementIds));
        LegendAndTwin legendAndTwin = new LegendAndTwin();
        legendAndTwin.setStatus(StatusEnum.DELETED.getCode());
        legendAndTwinMapper.update(legendAndTwin, new LambdaQueryWrapper<LegendAndTwin>().in(LegendAndTwin::getLegendId, legendManagementIds));

        // 逻辑删除图例孪生体表关系
        ArrayList<Long> legendAndTwinIds = legendAndTwins.stream().collect(ArrayList::new, (list, item) -> list.add(item.getId()), ArrayList::addAll);
        if (CollectionUtils.isEmpty(legendAndTwinIds)) {
            return update;
        }
        LegendConditional legendConditional = new LegendConditional();
        legendConditional.setStatus(StatusEnum.DELETED.getCode());
        legendConditionalMapper.update(legendConditional, new LambdaQueryWrapper<LegendConditional>().in(LegendConditional::getLegendAndTwinId, legendAndTwinIds));
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(LegendManagementParam legendManagementParam) {

        LegendManagement legendManagement = BeanUtils.copyToBean(legendManagementParam, LegendManagement.class);
        List<LegendManagement> legendManagements = legendManagementMapper.selectList(new LambdaQueryWrapper<LegendManagement>().eq(LegendManagement::getCode, legendManagement.getCode()).eq(LegendManagement::getStatus, StatusEnum.ENABLE.getCode()));
        if (legendManagements.size() < 1) {
            throw ThrowUtils.getThrow().badRequest("图例不存在");
        } else if (legendManagements.size() > 1) {
            throw ThrowUtils.getThrow().badRequest("图例编码重复");
        }
        if(CollectionUtils.isNotEmpty(legendManagementParam.getFiles())){
            legendManagement.setFiles(JSONObject.toJSONString(legendManagementParam.getFiles()));
        }
        // 更新图例
        this.updateById(legendManagement);

        // 查询之前图例的LegendAndTwin /ID
        ArrayList<Long> legendAndTwinIds = legendAndTwinMapper.selectList(new LambdaQueryWrapper<LegendAndTwin>().eq(LegendAndTwin::getLegendId, legendManagement.getId())).stream().collect(ArrayList::new, (list, legendAndTwin) -> list.add(legendAndTwin.getId()), ArrayList::addAll);
        //新增图例关联孪生体
        legendManagementParam.getListTwin().forEach(e -> {
            LegendAndTwin legendAndTwin = BeanUtils.copyToBean(e, LegendAndTwin.class);
            legendAndTwin.setLegendId(legendManagement.getId());
            legendAndTwin.setTwinId(e.getTwinId());
            // 从 TwinClass 中获取 twinCode
            TwinClass twinClass = twinClassApi.getById(e.getTwinId());
            legendAndTwin.setTwinCode(twinClass != null ? twinClass.getCode() : "");
            // 存在旧 图例关联孪生体 且 当前的图例关联孪生体存在其中 则更新否则插入
            if (CollectionUtils.isNotEmpty(legendAndTwinIds) && legendAndTwinIds.contains(legendAndTwin.getId())) {
                legendAndTwinMapper.updateById(legendAndTwin);
                legendAndTwinIds.remove(legendAndTwin.getId());
            } else {
                legendAndTwinMapper.insert(legendAndTwin);
            }
            // 查询之前图例孪生体条件 /ID
            ArrayList<Object> legendConditionalIds = legendConditionalMapper.selectList(new LambdaQueryWrapper<LegendConditional>().eq(LegendConditional::getLegendAndTwinId, legendAndTwin.getId())).stream().collect(ArrayList::new, (list, legendConditional) -> list.add(legendConditional.getId()), ArrayList::addAll);
            //新增图例关联孪生体的条件
            List<LegendConditionalParam> legendConditionalParams = e.getListConditional();
            int conditionalsSize = legendConditionalParams.size();
            for (int j = 0; j < conditionalsSize; j++) {
                LegendConditional legendConditional = BeanUtils.copyToBean(legendConditionalParams.get(j), LegendConditional.class);
                legendConditional.setLegendAndTwinId(legendAndTwin.getId());
                // 填充 orderNum 和 conditionalType ; OrderNum倒序 ; Content内容为空就是逻辑条件
                legendConditional.setOrderNum((long) (conditionalsSize - j));
                legendConditional.setConditionalType(StringUtils.isNotBlank(legendConditional.getFieldName()) ? ConditionalConstant.GENERAL_CONDITIONAL : ConditionalConstant.LOGIC_CONDITIONAL);
                // 存在旧 图例关联孪生体
                if (CollectionUtils.isNotEmpty(legendConditionalIds) && legendConditionalIds.contains(legendConditional.getId())) {
                    legendConditionalMapper.updateById(legendConditional);
                    legendConditionalIds.remove(legendConditional.getId());
                } else {
                    legendConditionalMapper.insert(legendConditional);
                }
            }
            // 逻辑删除 不存在旧 图例关联孪生体条件
            LegendConditional legendConditional = new LegendConditional();
            legendConditional.setStatus(StatusEnum.DELETED.getCode());
            if (legendConditionalIds.size() > 0) {
                legendConditionalMapper.update(legendConditional, new LambdaQueryWrapper<LegendConditional>().in(LegendConditional::getId, legendConditionalIds));
            }
        });
        // 逻辑删除 不存在旧 图例关联孪生体
        LegendAndTwin legendAndTwin = new LegendAndTwin();
        legendAndTwin.setStatus(StatusEnum.DELETED.getCode());
        if (legendAndTwinIds.size() > 0) {
            legendAndTwinMapper.update(legendAndTwin, new LambdaQueryWrapper<LegendAndTwin>().in(LegendAndTwin::getId, legendAndTwinIds));
        }
    }

    @Override
    @Cacheable(value = "legend_management", key = "#legendManagementParam", unless = "#result == null")
    public PageResult<LegendManagementParam> select(LegendManagementParam legendManagementParam) {
        Page<LegendManagementParam> backValue = PageFactory.defaultPage();

        // 从请求中获取分页参数
        HttpServletRequest request = HttpServletUtils.getRequest();
        String pageSize = request.getParameter("pageSize");
        String pageNo = request.getParameter("pageNo");

        if (StringUtils.isNotBlank(pageSize) && StringUtils.isNotBlank(pageNo)) {
            int pageSizeInt = Integer.parseInt(pageSize);
            int pageNoInt = Integer.parseInt(pageNo);
            legendManagementParam.setPageSize(pageSizeInt);
            legendManagementParam.setPageNo(pageNoInt);
            // 计算 offset
            legendManagementParam.setOffset((pageNoInt - 1) * pageSizeInt);
        }

        List<LegendManagementParam> legendManagementParams = legendManagementMapper.getTree(legendManagementParam);

        if (CollectionUtils.isEmpty(legendManagementParams)) {
            return new PageResult<>(backValue);
        }
        Set<Long> files = new HashSet<>();
        for (LegendManagementParam item:legendManagementParams){
            if (StringUtils.isBlank(item.getIcons())){
                continue;
            }
            try {
                // 直接解析为Map<Long,String>，避免类型转换问题
                Map<Long,String> jsonObject = JSON.parseObject(item.getIcons(), Map.class);
                Set<Long> fileIds = jsonObject.keySet();
                files.addAll(fileIds);
                log.debug("解析图例项icons成功，文件ID: {}", fileIds);
            } catch (Exception e) {
                log.warn("获取文件描述转json({})异常, error: {}", item.getIcons(), e.getMessage());
            }
        }
        List<SysFileInfoVo> fileInfos = CollectionUtils.isEmpty(files)?Collections.EMPTY_LIST:sysFileInfoApi.getFileInfos(files);
        log.info("查询到的文件ID集合: {}, 文件信息数量: {}", files, fileInfos.size());
        Map<Long, String> fileRelation = new HashMap<>();
        String tenantByRequest = TenantUtils.getTenantByRequest();
        log.info("当前租户: {}", tenantByRequest);
        if (CollectionUtils.isNotEmpty(fileInfos)) {
            Map<String, List<SysFileInfoVo>> collect = fileInfos.stream().collect(Collectors.groupingBy(SysFileInfoVo::getFileBucket));
            log.info("按bucket分组的文件信息: {}", collect.keySet());
            for (Map.Entry<String, List<SysFileInfoVo>> item : collect.entrySet()) {
                String bucketName = sysFileInfoApi.getBucketValueName(item.getKey());
                log.info("处理bucket: {}, bucketName: {}, 文件数量: {}", item.getKey(), bucketName, item.getValue().size());
                item.getValue().stream().filter(e -> StringUtils.isNotBlank(e.getFileBucket())).forEach(e -> {
                    String fileUrl = String.format("%s/%s/%s", tenantByRequest, bucketName, e.getFileObjectName());
                    fileRelation.put(e.getId(), fileUrl);
                    log.info("添加文件映射: {} -> {}", e.getId(), fileUrl);
                });
            }
        } else {
            log.warn("未查询到任何文件信息，files: {}", files);
        }
        log.info("最终构建的fileRelation: {}", fileRelation);
        for (LegendManagementParam item:legendManagementParams){
            if (StringUtils.isNotBlank(item.getIcons())){
                try {
                    Map<Long,String> jsonObject = JSON.parseObject(item.getIcons(),Map.class);
                    item.setFiles(jsonObject);
                    Set<Long> strings = jsonObject.entrySet().stream().map(e -> e.getKey()).collect(Collectors.toSet());
                    log.info("处理图例项: {}, 解析出的文件ID: {}", item.getId(), strings);
                    Map<Long, String> filteredMap = fileRelation.entrySet().stream()
                            .filter(entry -> strings.contains(entry.getKey()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    log.info("图例项: {} 匹配到的文件URL映射: {}", item.getId(), filteredMap);
                    item.setFilesUrl(filteredMap);
                } catch (Exception e) {
                    log.warn("获取文件描述转json({})异常, error: {}", item.getIcons(), e.getMessage());
                }
                item.setIcons(StringUtils.EMPTY);
            }

        }

        // 按照sort字段升序排序，null值排在后面
        legendManagementParams.sort(Comparator.comparing(LegendManagementParam::getSort, Comparator.nullsLast(Comparator.naturalOrder())));

        backValue.setTotal(legendManagementParams.size());
        backValue.setRecords(legendManagementParams);

        return new PageResult<>(backValue);
    }

    @Override
    public LegendTwinDataVo selectLegendTwin(Long legendId, String legendCode) {

        AssertUtils.isTrue(StringUtils.isNotBlank(legendCode) || Objects.nonNull(legendId), HttpStatus.BAD_REQUEST, "参数不能为空");
        LegendManagement legendManagement = null;
        LegendTwinDataVo legendTwinDataVo = null;
        List<LegendTwinDateListVo> twinList = new ArrayList<>();
        try {
            legendManagement = legendManagementMapper.selectOne(new LambdaQueryWrapper<LegendManagement>().eq(StringUtils.isNotBlank(legendCode), LegendManagement::getCode, legendCode).eq(Objects.nonNull(legendId), LegendManagement::getId, legendId).eq(LegendManagement::getStatus, StatusEnum.ENABLE.getCode()));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().badRequest("图例模块数据异常，请联系管理员。");
        }

        // 图例查询结果不为空 继续执行否则返回空数据1
        if (Objects.nonNull(legendManagement)) {

            legendTwinDataVo = BeanUtils.copyToBean(legendManagement, LegendTwinDataVo.class);
            legendTwinDataVo.setTwinList(twinList);
            if (Objects.nonNull(legendTwinDataVo.getFileId())) {

                SysFileInfo sysFileInfo = sysFileInfoMapper.selectOne(new LambdaQueryWrapper<SysFileInfo>().eq(SysFileInfo::getStatus, StatusEnum.ENABLE.getCode()).eq(SysFileInfo::getId, legendTwinDataVo.getFileId()));

                if (Objects.nonNull(sysFileInfo)) {
                    legendTwinDataVo.setFileUrl(sysFileInfoApi.getMappingUrl(sysFileInfo.getFileBucket(),TenantUtils.getTenantByRequest(), sysFileInfo.getFileObjectName()));
                    legendTwinDataVo.setFileName(sysFileInfo.getFileOriginName());
                }
            }

            List<LegendAndTwin> legendAndTwins = legendAndTwinMapper.selectList(new LambdaQueryWrapper<LegendAndTwin>().eq(LegendAndTwin::getLegendId, legendManagement.getId()).eq(LegendAndTwin::getStatus, StatusEnum.ENABLE.getCode()));

            // TODO:这里是倒序查询，前端参数是正序list，需要填充注意
            legendAndTwins.forEach(legendAndTwin -> {
                List<LegendConditional> legendConditionals = legendConditionalMapper.selectList(new LambdaQueryWrapper<LegendConditional>().and(wrapper -> wrapper.eq(LegendConditional::getLegendAndTwinId, legendAndTwin.getId()).or().eq(LegendConditional::getConditionalType, ConditionalConstant.FILL_CONDITIONAL)).eq(LegendConditional::getStatus, StatusEnum.ENABLE.getCode()).orderByDesc(LegendConditional::getOrderNum));

                TwinClass twinClass = Optional.ofNullable(twinClassApi.getById(legendAndTwin.getTwinId())).orElse(new TwinClass());

                LegendTwinDateListVo legendTwinDateListVo = new LegendTwinDateListVo();
                legendTwinDateListVo.setId(legendAndTwin.getId());
                legendTwinDateListVo.setTwinId(twinClass.getId());
                legendTwinDateListVo.setTwinCode(twinClass.getCode());
                legendTwinDateListVo.setTwinName(twinClass.getName());
                legendTwinDateListVo.setTwinDateList(selectTwinResultMap(legendAndTwin, legendConditionals));
                twinList.add(legendTwinDateListVo);
            });
        }
        return legendTwinDataVo;
    }

    private List<HashMap<String, Object>> selectTwinResultMap(LegendAndTwin legendAndTwin, List<LegendConditional> legendConditionals) {

        final List<String> times = Arrays.asList("create_time", "update_time");

        TwinClass twinClass = null;
        try {
            twinClass = twinClassMapper.selectOne(new LambdaQueryWrapper<TwinClass>().eq(TwinClass::getId, legendAndTwin.getTwinId()).eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode()));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().badRequest("图例模块数据异常，请联系管理员。");
        }
        // 兼容没有条件
        if (legendConditionals.size() == 1) {
            legendConditionals.get(0).setFieldName(" 1");
        }
        String tableName = JrmJsonUtils.getTableName(twinClass.getStructure());
        // 根据 legendConditionals 生成 sql Map 参数
        LinkedHashMap<String, String> mapParam = LegendUtil.getSqlParamMapByLegendConditionals(legendConditionals);

//        final String tenantCode = TenantUtils.isMaster() ? "" : TenantUtils.getTenantByRequest().toLowerCase() + StringConstant.UNDERSCORE;
        List<HashMap<String, Object>> hashMaps = twinBodyDataMapper.selectTwinByMapParam(tableName, mapParam);
        hashMaps.forEach(twinDataItem -> times.forEach(time -> {
            Object createTime = twinDataItem.get(time);
            if (Objects.nonNull(createTime) && (createTime instanceof LocalDateTime)) {
                twinDataItem.put(time, ((LocalDateTime) createTime).format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            }
        }));
        return hashMaps;
    }

    @Override
    public List<LegendXxvMenuTwinVo> getXxvTwinFrom(LegendXxvMenuTwinParam param) {

        List<LegendXxvMenuTwinVo> legendXxvMenuTwinVos = new ArrayList<>();

        String xxvMenuCode = param.getXxvMenuCode();
        Long xxvMenuId = param.getXxvMenuId();
        List<Long> twinClassIds = null;
        boolean isAllQuery = true;

        if (StringUtils.isNotBlank(xxvMenuCode) || Objects.nonNull(xxvMenuId)) {
            SysMenu sysMenu = sysMenuMapper.selectOne(new LambdaQueryWrapper<SysMenu>().eq(StringUtils.isNotBlank(xxvMenuCode), SysMenu::getCode, xxvMenuCode).eq(Objects.nonNull(xxvMenuId), SysMenu::getId, xxvMenuId).isNotNull(SysMenu::getCode).eq(SysMenu::getStatus, StatusEnum.ENABLE.getCode()));
            // (错误的 菜单 code 或id)查询不出sysMenu 则查询所有
            if (Objects.nonNull(sysMenu)) {
                isAllQuery = false;
                // 查询 全部的TwinXxv TwinId不为空
                twinClassIds = twinXxvMapper.selectList(new LambdaQueryWrapper<TwinXxv>().eq(StringUtils.isNotBlank(sysMenu.getCode()), TwinXxv::getXxvCode, sysMenu.getCode()).isNotNull(TwinXxv::getTwinId).eq(TwinXxv::getStatus, StatusEnum.ENABLE.getCode())).stream().map(TwinXxv::getTwinId).collect(Collectors.toList());
            }
        }
        // 查询全局
        if (isAllQuery) {
            twinClassIds = twinClassMapper.selectList(new LambdaQueryWrapper<TwinClass>().eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode())).stream().map(TwinClass::getId).collect(Collectors.toList());
        }
        // 至此twinClassIds为null 直接返回null
        if (Objects.isNull(twinClassIds)) {
            return null;
        }
        // 处理 TwinXxv
        twinClassIds.forEach(e -> {
            LegendXxvMenuTwinVo legendXxvMenuTwinVo = buildTwinFormVo(e);
            legendXxvMenuTwinVos.add(legendXxvMenuTwinVo);
        });

        return legendXxvMenuTwinVos;
    }

    @Override
    public LegendXxvMenuTwinVo buildTwinFormVo(Long twinClassId) {
        LegendXxvMenuTwinVo legendXxvMenuTwinVo = new LegendXxvMenuTwinVo();

        // 查询 TwinClass
        TwinClass twinClass = twinClassMapper.selectOne(new LambdaQueryWrapper<TwinClass>().eq(TwinClass::getId, twinClassId).eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode()));
        List<LegendXxvMenuTwinFiledVo> legendXxvMenuTwinFiledVos = new ArrayList<>();
        FormUtils.parseForm(twinClass.getForm()).getList().forEach(fieldInfo -> {
            // 输入框 单选框 复选框
            if (TwinFieldTypeConstant.inputType.equals(fieldInfo.getType()) || TwinFieldTypeConstant.radioType.equals(fieldInfo.getType()) || TwinFieldTypeConstant.checkboxType.equals(fieldInfo.getType())) {
                LegendXxvMenuTwinFiledVo legendXxvMenuTwinFiledVo = new LegendXxvMenuTwinFiledVo();
                addFieldVoByFieldInfo(legendXxvMenuTwinFiledVos, fieldInfo, legendXxvMenuTwinFiledVo);
            }
        });
        legendXxvMenuTwinVo.setTwinId(twinClass.getId());
        legendXxvMenuTwinVo.setTwinCode(twinClass.getCode());
        legendXxvMenuTwinVo.setTwinName(twinClass.getName());
        legendXxvMenuTwinVo.setLegendXxvMenuTwinFiledVos(legendXxvMenuTwinFiledVos);

        return legendXxvMenuTwinVo;
    }

    private void addFieldVoByFieldInfo(List<LegendXxvMenuTwinFiledVo> legendXxvMenuTwinFiledVos, FormItem fieldInfo, LegendXxvMenuTwinFiledVo legendXxvMenuTwinFiledVo) {
        legendXxvMenuTwinFiledVo.setModel(fieldInfo.getModel());
        legendXxvMenuTwinFiledVo.setKey(fieldInfo.getKey());
        legendXxvMenuTwinFiledVo.setLabel(fieldInfo.getLabel());
        legendXxvMenuTwinFiledVo.setType(fieldInfo.getType());
        legendXxvMenuTwinFiledVo.setConditions(TwinConditionsEnum.getConditions(fieldInfo.getType()));
        legendXxvMenuTwinFiledVo.setOptionalValues(fieldInfo.getOptions().getOptions());
        legendXxvMenuTwinFiledVos.add(legendXxvMenuTwinFiledVo);
    }
}
