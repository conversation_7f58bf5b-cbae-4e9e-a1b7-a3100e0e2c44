<template>
  <div class="login">
    <div class="left">
      <div class="text">{{ loginDictInfo.loginTitle1 }}</div>
      <div class="title">{{ loginDictInfo.loginTitle2 }}</div>
      <div class="sub-title">{{ loginDictInfo.loginTitle3 }}</div>
      <img class="img" :src="loginDictInfo.loginBg" alt="背景图" />
    </div>
    <div class="right">
      <div class="right-content">
        <div class="title"><img :src="loginDictInfo.loginLogo" alt="logo" />{{ loginDictInfo.loginTitle }}</div>
        <div class="sub-title"></div>
        <a-form ref="loginRef" :model="data" name="login" autocomplete="off">
          <a-form-item v-if="multiTenant" class="common-form-item" name="tenantCode" :rules="[{ required: true, message: '请输入租户标识' }]">
            <a-input v-model:value="data.tenantCode" class="input" placeholder="租户标识" @blur="getCaptmage" />
          </a-form-item>
          <a-form-item class="common-form-item" name="userName" :rules="[{ required: true, message: '请输入用户名' }]">
            <a-input v-model:value="data.userName" class="input" placeholder="用户名" />
          </a-form-item>
          <a-form-item class="common-form-item" name="password" :rules="[{ required: true, message: '请输入密码' }]">
            <a-input-password v-model:value="data.password" class="input" placeholder="密码" />
          </a-form-item>
          <a-form-item class="common-form-item" name="code" :rules="[{ required: true, message: '请输入验证码' }]">
            <a-input-number v-model:value="data.code" class="input code-input" placeholder="请输入验证码" />
            <img :src="captimageData.captimage.codeData" class="code-img" @click="getCaptmage" />
          </a-form-item>
          <a-form-item>
            <div class="login-btn" @click="clickLogin">登录</div>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import loginBg from '@/assets/img/login/login-bg.png';
import loginLogo from '@/assets/img/header/ti.png';
import { usePermissionStore } from '@/store/permission';
import { login, getCode } from '@/api/login/index';
import { disposeUserInfo } from '@/hooks/usePermission';
import { useCookies } from 'vue3-cookies';
import { getTokenAndTenant, generateRequestId } from '@/utils/util';
import { useSettingStore } from '@/store/setting';
const { cookies } = useCookies();
const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
const permissionStore = usePermissionStore();
// 是否开启多租户
const multiTenant = computed(() => {
  return permissionStore.multiTenant;
});
const router = useRouter();
const confirmLoading = ref(false);
const data = reactive<{
  tenantCode: any;
  userName: string;
  password: string;
  code: number | string;
}>({
  tenantCode: '',
  userName: '',
  password: '',
  code: '',
});
const loginRef = ref();
const captimageData = reactive({
  captimage: {
    codeData: '',
    uuid: '',
  },
});
const clickLogin = () => {
  if (confirmLoading.value) return;
  loginRef.value.validate().then(() => {
    const param = {
      account: data.userName,
      code: data.code,
      password: data.password,
      uuid: captimageData.captimage.uuid,
      ldapStatus: 0,
      tenantCode: data.tenantCode,
    };
    confirmLoading.value = true;
    sessionStorage.setItem('TENANT', data.tenantCode);
    login(param)
      .then(
        (res: any) => {
          // 工作台跳转-清空项目名称逻辑
          if (projectName.value === 'master' && data.tenantCode !== 'master') {
            sessionStorage.setItem('PROJECT_NAME', '');
          }
          confirmLoading.value = false;
          cookies.set(`TENANT_${data.tenantCode}`, data.tenantCode, '', '/', window.location.hostname, false, 'Lax');
          cookies.set(`ACCESS_TOKEN_${data.tenantCode}`, res.data, '', '/', window.location.hostname, false, 'Lax');
          disposeUserInfo(data.tenantCode).then(() => {
            router.push(`/${data.tenantCode}/welcome`);
          });
        },
        () => {
          confirmLoading.value = false;
          getCaptmage();
        }
      )
      .catch(() => {
        confirmLoading.value = false;
        getCaptmage();
      });
  });
};

// 获取验证码
const getCaptmage = () => {
  getCode({ tenantCode: data.tenantCode }).then((res: any) => {
    const codeData = res.data;
    const imgData: any = {
      codeData: `data:image/gif;base64,${codeData.img}`,
      uuid: codeData.uuid,
    };
    captimageData.captimage = imgData;
  });
  getLoginDictByTenant(data.tenantCode || 'master');
};
const keyUp = (e: any) => {
  if (e.keyCode === 13) {
    clickLogin();
  }
};
// 监听多租户改变
watch(
  () => permissionStore.multiTenant,
  (val) => {
    if (!val) {
      data.tenantCode = 'master';
      sessionStorage.setItem('TENANT', data.tenantCode);
    } else {
      data.tenantCode = '';
    }
  },
  {
    immediate: true,
  }
);
// 获取登录页开放的一些配置
const loginDictInfo = reactive({
  loginTitle1: '一站式提供',
  loginTitle2: '数字孪生项目数据资源管理',
  loginTitle3: '孪生中心、园区资源、城市资源、大屏资源、模型图标、数据对接',
  loginBg,
  loginLogo,
  loginTitle: '项目底座',
});
// 根据租户获取配置
const getLoginDictByTenant = async (tenant: string) => {
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=XI_TONG_BIAO_TI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginTitle = res.data?.data !== '无' ? res.data?.data : '';
      sessionStorage.setItem(`${tenant}_XI_TONG_BIAO_TI`, loginDictInfo.loginTitle);
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=YI_JI_BIAO_TI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginTitle1 = res.data?.data !== '无' ? res.data?.data : '';
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=ER_JI_BIAO_TI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginTitle2 = res.data?.data !== '无' ? res.data?.data : '';
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=SAN_JI_BIAO_TI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginTitle3 = res.data?.data !== '无' ? res.data?.data : '';
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=DENG_LU_YE_LOGO`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginLogo = res.data?.data !== '无' ? res.data?.data : '';
      sessionStorage.setItem(`${tenant}_XI_TONG_LOGO`, loginDictInfo.loginLogo);
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=DENG_LU_YE_BEI_JING_TU`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      loginDictInfo.loginBg = res.data?.data !== '无' ? res.data?.data : '';
    });
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=XI_TONG_JIA_MI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      useSettingStore().setSensitiveConfig(res.data?.data || 'Y');
      sessionStorage.setItem('SensitiveConfig', res.data?.data);
    });
};
onUnmounted(() => {
  window.removeEventListener('keyup', keyUp, false);
});
onMounted(() => {
  const { Stenant, Stoken } = getTokenAndTenant(useRoute().path);
  if (Stoken) {
    data.tenantCode = Stenant;
  } else {
    data.tenantCode = sessionStorage.getItem('TENANT') || '';
  }
  getCaptmage();
  window.addEventListener('keyup', keyUp);
});
</script>
<style scoped lang="scss">
// :deep(.ant-input-number-handler-wrap) {
//   display: none;
// }

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

input:-internal-autofill-selected {
  background-color: transparent;
}

.login {
  display: flex;
  align-items: center;
  height: 100%;
  overflow: hidden;
  background-color: #f3f6fa;

  // background: #f7f8fa;

  .left {
    width: 60%;
    max-height: 100%;
    text-align: center;

    .text {
      margin-bottom: 16px;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 24px;
      font-weight: 400;
      color: #1d1f24;
      letter-spacing: 2px;
    }

    .title {
      font-family: PingFangSC-Medium, 'PingFang SC';
      font-size: 38px;
      font-weight: 500;
      color: #1d1f24;
      letter-spacing: 2px;
    }

    .sub-title {
      font-family: PingFang-SC-Medium, PingFang-SC;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #1d1f24;
    }

    .img {
      width: 70.5%;
      margin-top: 10px;

      // max-width: 893px;
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40%;
    height: 100%;
    background: #fff;

    .right-content {
      .common-form-item {
        .code-img {
          position: absolute;
          top: 1px;
          right: 1px;
          height: 41px;
          cursor: pointer;
        }
      }

      .title {
        font-family: DINAlternate-Bold;
        font-size: 30px;
        font-weight: 700;
        text-align: center;

        img {
          width: 30px;
          height: 30px;
          margin-right: 5px;
        }
      }

      .sub-title {
        margin-top: 12px;
        margin-bottom: 50px;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        color: #1d1f24;
        text-align: center;
        letter-spacing: 1px;
      }

      .input {
        width: 352px;
        height: 42px;
        padding: 0 20px;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 18px;
        font-weight: 400;
        color: #1d1f24;
        background: rgb(240 242 245 / 80%);
        border-radius: 6px;

        &.code-input {
          padding-right: 200px;

          :deep(input) {
            width: 100%;
            height: 40px;
            padding: 0;
            background: transparent;
          }
        }

        :deep(input) {
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 16px;
          font-weight: 400;
          color: #1d1f24;
          background: rgb(240 242 245 / 80%);
        }
      }

      .login-btn {
        width: 352px;
        height: 42px;
        margin: 0 auto;
        font-family: MicrosoftYaHeiSemibold;
        font-size: 22px;
        font-weight: 500;
        line-height: 42px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: var(--theme-color);
        border-radius: 6px;
      }
    }
  }
}
</style>
