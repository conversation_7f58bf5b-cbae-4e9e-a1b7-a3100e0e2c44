<template>
  <a-modal
    :title="`${type === 'add' ? '新增' : '编辑'}图例`"
    :body-style="{ maxHeight: '650px', overflow: 'auto' }"
    wrap-class-name="cus-modal"
    :width="676"
    :open="visible"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formAddRef" :model="formState" :rules="rules" label-align="left">
        <a-form-item label="图例名称:" has-feedback name="name">
          <a-input v-model:value="formState.name" placeholder="请输入图例名称" :maxlength="30" @keyup="toPinyin()" />
        </a-form-item>
        <a-form-item label="唯一编码:" has-feedback name="code">
          <a-input v-model:value="formState.code" :disabled="isEdit" placeholder="请输入唯一编码" :maxlength="30" />
        </a-form-item>

        <a-form-item label="所属XXV菜单" :validate-first="true" has-feedback name="menuId">
          <a-tree-select
            v-model:value="formState.menuId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
            :tree-data="orgTree"
            placeholder="请选择所属XXV菜单"
            tree-default-expand-all
            @change="initrdeptName"
          />
        </a-form-item>
        <a-form-item name="sort" label="排序" has-feedback>
          <a-input-number v-model:value="formState.sort" style="width: 100%" placeholder="请输入排序" :min="1" :max="1000" />
        </a-form-item>
        <a-form-item name="color" label="选择颜色">
          <!-- <color-picker v-model:rgba="color" @change="changeColor"></color-picker> -->
          <pick-colors v-model:value="color" :theme="theme" :size="24" format="rgb" show-alpha @change="changeColor" />
        </a-form-item>
        <a-form-item name="files" label="上传图标:">
          <div class="icon-upload-container">
            <div v-for="(icon, index) in dynamicIcons.list" :key="icon.key" class="icon-item">
              <div class="icon-info">
                <a-input v-model:value="icon.name" placeholder="请输入图标名称" :maxlength="30" size="small" />
              </div>
              <div class="icon-upload-section">
                <div class="icon-upload-wrapper">
                  <delayed-image-upload
                    :ref="(el) => setIconUploadRef(el, index)"
                    v-model="icon.fileId"
                    bucket-name="legend"
                    :existing-file-url="icon.existingUrl"
                    upload-text="图标"
                    @file-change="(file, previewUrl) => handleIconChange(file, previewUrl, index)"
                  />
                </div>
              </div>
              <div class="icon-actions">
                <a-button type="text" size="small" danger title="删除图标" @click="removeIconItem(index)">
                  <template #icon>
                    <delete-outlined />
                  </template>
                </a-button>
              </div>
            </div>
            <div class="add-icon-btn">
              <a-button type="dashed" block @click="addIconItem">
                <icon-font type="icon-add" class="icon-add" />
                添加图标
              </a-button>
            </div>
          </div>
        </a-form-item>
        <!-- name="listTwin" -->
        <a-form-item label="自定义筛选">
          <a-row>
            <a-col :span="10">
              <a-form-item label="孪生体对象" class="lsdx">
                <a-tooltip title="自定义筛选所使用的孪生体对象在【菜单管理】页面，配置的XXV菜单中进行维护" placement="right" style="margin-left: 0">
                  <question-circle-outlined style="margin-top: 10px; color: #ef7b1a" />
                </a-tooltip>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="自定义筛选条件" />
            </a-col>
          </a-row>
          <a-row v-for="(item, index) in dynamicForm.list" :key="item.key" :gutter="5">
            <a-col :span="8">
              <a-form-item>
                <a-select v-model:value="item.twinId" placeholder="请选择孪生体对象" @change="twinChange(item, index)">
                  <a-select-option v-for="e in area" :key="e.twinId" :value="e.twinId">
                    {{ e.twinName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="14">
              <a-form-item>
                <conditional-input
                  :conditionals="item.listConditional"
                  :twins="area"
                  :twin-id="item.twinId"
                  :conditional-relation="item.conditionalRelation"
                  @ok="(conditionals, relation) => conditionalInputOk(conditionals, relation, index)"
                />
              </a-form-item>
            </a-col>
            <a-col :span="1">
              <a-form-item>
                <delete-outlined title="删除" @click="removeDynamicItem(index)" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item>
            <a-button type="dashed" block @click="addDynamicItem">
              <icon-font type="icon-add" class="icon-add" />
              添加配置
            </a-button>
          </a-form-item>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, computed } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import PickColors from 'vue-pick-colors';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { addLegend, getXXVFrom, getXxvMenu, editItemApi } from '@/api/business/legend';
import { detailFileBatch } from '@/api/develop/attachmentManage';
import { chineseToChar, handelInputChar } from '@/utils/util';
import ConditionalInput from './ConditionalInput.vue';
import { useSettingStore } from '@/store/setting';
import DelayedImageUpload from '@/components/DelayedImageUpload.vue';
import { FormFileUploadHandler } from '@/utils/delayedUpload';

const settingStore = useSettingStore();
const emits = defineEmits(['ok']);

// 当前主题
const theme = computed(() => settingStore.modeName);
const visible = ref(false);
const confirmLoading = ref(false);
const orgTree = ref<any>([]);
const type = ref('');

// 延迟上传相关
const imageUploadRef = ref();
const existingImageUrl = ref('');
const uploadHandler = new FormFileUploadHandler();

// 图标上传相关
const iconUploadRefs = ref<any[]>([]);
const dynamicIcons = ref({
  list: [] as Array<{
    key: string;
    name: string;
    fileId: string;
    existingUrl: string;
  }>,
});

// 设置图标上传组件的引用
const setIconUploadRef = (el: any, index: number) => {
  if (el) {
    iconUploadRefs.value[index] = el;
  }
};

const color = ref('rgb(226, 83, 77)');
// const colorRgba = ref('rgba(255,255,255)')
const changeColor = (e: any) => {
  // colorRgba.value = e.rgba
  // formState.color = e.rgba
  formState.color = e;
};

/** 筛选下拉菜单数据 */
const area: any = ref([]);

const isEdit = ref(false);

const dynamicForm: any = ref({
  list: [],
});

const id: any = ref('');

const twinChange = (item: any, index: number) => {
  dynamicForm.value.list[index].listConditional = [];
};
const addDynamicItem = () => {
  dynamicForm.value.list.push({
    twinId: undefined,
    conditionalRelation: 'and',
    conditionalDesc: undefined,
    listConditional: [],
  });
};

const removeDynamicItem = (index: number) => {
  dynamicForm.value.list.splice(index, 1);
};

// 图标管理方法
const addIconItem = () => {
  const key = Date.now().toString();
  dynamicIcons.value.list.push({
    key,
    name: '',
    fileId: '',
    existingUrl: '',
  });
};

const removeIconItem = (index: number) => {
  // 移除上传队列中的文件
  uploadHandler.removeFile(`icon_${index}`);
  // 重置对应的上传组件
  if (iconUploadRefs.value[index]) {
    iconUploadRefs.value[index].reset();
  }
  dynamicIcons.value.list.splice(index, 1);
  // 清理引用数组
  iconUploadRefs.value.splice(index, 1);
};

// 处理图标变化
const handleIconChange = (file: File | null, previewUrl: string, index: number) => {
  const key = `icon_${index}`;
  if (file) {
    // 添加到上传队列
    uploadHandler.addFile(file, key, 'legend');
  } else {
    // 移除上传队列中的文件
    uploadHandler.removeFile(key);
  }
  // 触发表单验证
  formAddRef.value?.validateFields('icons');
};

// 回滚已上传的文件
const rollbackUploadedFiles = async (fileIds: string[]) => {
  if (fileIds.length === 0) return;

  try {
    await detailFileBatch({ ids: fileIds });
    console.log('🗑️ 已删除提交失败时的文件:', fileIds);
  } catch (error) {
    console.error('❌ 删除文件失败:', error);
  }
};

// 处理图片变化（保留原有方法，以防其他地方使用）
const handleImageChange = (file: File | null, previewUrl: string) => {
  if (file) {
    // 添加到上传队列
    uploadHandler.addFile(file, 'fileId', 'legend');
  } else {
    // 移除上传队列中的文件
    uploadHandler.removeFile('fileId');
  }
  // 触发表单验证
  formAddRef.value?.validateFields('fileId');
};
// 编码拼音
const toPinyin = () => {
  formState.name = handelInputChar(formState.name); // 去除火星文和空格
  if (isEdit.value) {
    return;
  }
  const codeVal = chineseToChar(formState.name);
  if (codeVal.charAt(codeVal.length - 1) === '_') {
    const res = codeVal.slice(0, codeVal.length - 1);
    formState.code = res.length > 30 ? res.slice(0, 30) : res;
  } else {
    formState.code = codeVal.length > 30 ? codeVal.slice(0, 30) : codeVal;
  }
};

const extOrgPos: any = ref([]);

const add = (operation: string, deptParam: Record<string, any>, id?: string) => {
  type.value = operation;
  getXxvMenu({ flag: true }).then((res: any) => {
    if (!res.success) {
      return;
    }
    const datas = [];
    datas.push({
      id: '0',
      pid: '0',
      title: '全部',
      disabled: true,
      value: '0',
      children: [
        {
          id: '-1',
          pid: '0',
          title: '全局',
          value: '-1',
          children: [],
        },
      ],
    });
    const xxvData = res.data;
    if (xxvData) {
      xxvData.disabled = true;
      datas[0].children.push(xxvData);
    }
    orgTree.value = datas;
    if (operation === 'add') {
      formState.menuId = deptParam.deptId;
      formState.deptName = deptParam.deptName;
    }
  });

  if (operation === 'add') {
    getXXVFrom({}).then((res: any) => {
      if (res.code === 200) {
        area.value = res.data;
      }
    });
  }

  visible.value = true;
};

const conditionalInputOk = (conditionals: any, relationss: any, index: number) => {
  dynamicForm.value.list[index].listConditional = conditionals;
  dynamicForm.value.list[index].conditionalRelation = relationss;
  formState.listTwin = dynamicForm.value.list;
  formAddRef.value.validate();
};
// 移除旧的上传相关代码，现在使用延迟上传

// 表单相关
interface FormState {
  code: string;
  name: string;
  menuId: string;
  deptName: string;
  files: Array<{
    name: string;
    fileId: string;
  }>;
  sort: number;
  color: string;
  listTwin: Array<any>;
}
const formAddRef = ref();

const formState = reactive<FormState>({
  code: '',
  name: '',
  menuId: '',
  sort: 100,
  deptName: '',
  files: [],
  color: '',
  listTwin: [],
});
// 编码验证
const twinsCodeCheckFunc = (rule: Rule, value: string) => {
  const reg = /^[A-Z_]+$/;
  if (!value) {
    // eslint-disable-next-line
    return Promise.reject('请输入孪生体分类编码！');
  }
  if (!reg.test(value)) {
    // eslint-disable-next-line
    return Promise.reject('请输入大写字母或下划线的编码！');
  }
  if (value.toString().length > 50) {
    // eslint-disable-next-line
    return Promise.reject('孪生体分类编码长度不超过50！');
  }
  return Promise.resolve();
};
// 验证自定义筛选
const validateConditionals = (rule: Rule, value: Array<any>, callback: any) => {
  if (!value) {
    callback('请正确配置自定义筛选条件!');
  }
  if (value.length === 0) {
    callback('请正确配置自定义筛选条件!');
  }
  value.forEach((e: any) => {
    if (!e.listConditional) {
      callback('请正确配置自定义筛选条件!');
    }
    if (e.listConditional.length === 0) {
      callback('请正确配置自定义筛选条件!');
    }
  });
  callback();
};
// 验证文件上传（保留原有方法以兼容）
const validateFileUpload = (rule: Rule, value: string) => {
  // 如果有已存在的文件ID，或者有待上传的文件，则验证通过
  if (value || uploadHandler.hasFile('fileId')) {
    return Promise.resolve();
  }
  return Promise.reject(new Error('请上传图标！'));
};

// 验证图标上传
const validateIconsUpload = (rule: Rule, value: Array<any>) => {
  // 检查是否至少有一个图标
  if (!dynamicIcons.value.list || dynamicIcons.value.list.length === 0) {
    return Promise.reject(new Error('请至少添加一个图标！'));
  }

  // 检查每个图标是否都有名称和文件
  for (let i = 0; i < dynamicIcons.value.list.length; i++) {
    const icon = dynamicIcons.value.list[i];
    if (!icon.name || icon.name.trim() === '') {
      return Promise.reject(new Error(`请输入第${i + 1}个图标的名称！`));
    }
    if (!icon.fileId && !uploadHandler.hasFile(`icon_${i}`)) {
      return Promise.reject(new Error(`请上传第${i + 1}个图标的文件！`));
    }
  }

  return Promise.resolve();
};

const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: '请输入图例名称！' }],
  color: [{ required: true, message: '请选择颜色!' }],
  code: [
    {
      required: true,
      message: '请输入大写字母或下划线的编码！',
      validator: twinsCodeCheckFunc,
      trigger: 'blur',
    },
  ],
  menuId: [{ required: true, message: '请选所属XXV菜单！' }],
  files: [{ required: true, validator: validateIconsUpload }],
  // listTwin: [{ required: true, validator: validateConditionals }],
};
// 保存
const handleSubmit = async () => {
  if (confirmLoading.value) return;
  confirmLoading.value = true;

  let uploadedFileIds: Record<string, string> = {};
  try {
    // 表单验证
    const values = await formAddRef.value?.validateFields();
    // 处理上传文件
    if (uploadHandler.getFileCount() > 0) {
      uploadedFileIds = await uploadHandler.uploadAll();
    }

    const data = { id: '', ...values };

    // 处理自定义筛选条件
    const listTwin: any = [];
    dynamicForm.value.list.forEach((e: any) => {
      const temp = { ...e };
      const relation = temp.conditionalRelation;
      const conditionals = temp.listConditional;

      const saveConditionals = [];
      for (let i = 0; i < conditionals.length; i += 1) {
        saveConditionals.push(conditionals[i]);
        if (i !== conditionals.length - 1) {
          saveConditionals.push({ content: relation, condition: 'equals' });
        }
      }
      temp.listConditional = saveConditionals;
      listTwin.push(temp);
    });

    data.listTwin = listTwin;
    data.menuId = formState.menuId;

    // 处理图标数据
    const icons: Array<{ name: string; fileId: string }> = [];
    const newUploadedFileIds: string[] = []; // 记录本次上传的文件ID

    dynamicIcons.value.list.forEach((icon, index) => {
      const iconFileId = uploadedFileIds[`icon_${index}`] || icon.fileId;
      if (iconFileId) {
        icons.push({
          name: icon.name,
          fileId: iconFileId,
        });

        // 如果是本次新上传的文件，记录下来
        if (uploadedFileIds[`icon_${index}`]) {
          newUploadedFileIds.push(iconFileId);
        }
      }
    });
    // 将 icons 数组转换为 {文件id: 名字} 的对象形式
    data.files = icons.reduce(
      (acc, icon) => {
        acc[icon.fileId] = icon.name;
        return acc;
      },
      {} as Record<string, string>
    );
    if (type.value === 'add' || type.value === 'copy') {
      const res = await addLegend(data);
      if (res.code === 200) {
        emits('ok', data);
        handleCancel();
        useGlobalMessage('success', '图例新增成功');
      } else {
        // 新增失败，回滚已上传的文件
        if (newUploadedFileIds.length > 0) {
          await rollbackUploadedFiles(newUploadedFileIds);
        }
        useGlobalMessage('error', res.message || '图例新增失败');
      }
    } else {
      data.id = id.value;
      const res = await editItemApi(data);
      if (res.code === 200) {
        emits('ok', data);
        handleCancel();
        useGlobalMessage('success', '图例编辑成功');
      } else {
        // 编辑失败，回滚已上传的文件
        if (newUploadedFileIds.length > 0) {
          await rollbackUploadedFiles(newUploadedFileIds);
        }
        useGlobalMessage('error', res.message || '图例编辑失败');
      }
    }
  } catch (error: any) {
    console.error('提交失败:', error);

    // 提取本次上传的文件ID进行回滚
    const newUploadedFileIds: string[] = [];
    Object.keys(uploadedFileIds).forEach((key) => {
      if (key.startsWith('icon_')) {
        newUploadedFileIds.push(uploadedFileIds[key]);
      }
    });

    // 回滚已上传的文件
    if (newUploadedFileIds.length > 0) {
      await rollbackUploadedFiles(newUploadedFileIds);
    }
    if (!error?.errorFields) {
      useGlobalMessage('error', type.value === 'add' ? '图例新增失败' : type.value === 'copy' ? '图例复制失败' : '图例编辑失败');
    }
  } finally {
    confirmLoading.value = false;
  }
};
const handleCancel = () => {
  if (confirmLoading.value) return;
  formAddRef.value?.resetFields();
  confirmLoading.value = false;
  extOrgPos.value = [];
  existingImageUrl.value = '';
  uploadHandler.clear();
  imageUploadRef.value?.reset();
  dynamicForm.value.list = [];
  // 重置图标数据
  dynamicIcons.value.list = [];
  iconUploadRefs.value = [];
  visible.value = false;
};

const initrdeptName = async (value: any, label: any) => {
  formState.deptName = label[0];

  await getXXVFrom({ menuId: value }).then((res: any) => {
    if (res.code === 200) {
      nextTick(() => {
        area.value = res.data;
      });
    }
  });
};
// 获取拼接地址
const getUrl = (item: any) => {
  if (item.fileUrl) {
    return `${window.baseConfig.previewResourceUrl}${item.fileUrl}`;
  }
  return '';
};
const init = async (data: any, type: any) => {
  visible.value = true;
  isEdit.value = false;
  uploadHandler.clear();

  if (type === 'add') {
    id.value = '';
    formState.name = '';
    formState.code = '';
    formState.menuId = '';
    formState.sort = 100;
    formState.files = [];
    formState.color = 'rgb(226, 83, 77)';
    formState.listTwin = [];
    existingImageUrl.value = '';
    color.value = 'rgb(226, 83, 77)';
    dynamicForm.value.list = [];
    dynamicIcons.value.list = [];
    iconUploadRefs.value = [];
    // 添加一个默认的图标项
    addIconItem();
    imageUploadRef.value?.reset();
  } else if (type === 'edit' || type === 'copy') {
    isEdit.value = type === 'copy' ? false : true;
    id.value = type === 'copy' ? '' : data.id;
    formState.name = type === 'copy' ? `${data.name}_copy` : data.name;
    formState.code = type === 'copy' ? `${data.code}_COPY` : data.code;
    formState.sort = data.sort;
    formState.menuId = data.menuId;
    formState.color = data.color;
    color.value = data.color;
    existingImageUrl.value = getUrl(data);

    // 初始化图标数据
    dynamicIcons.value.list = [];
    iconUploadRefs.value = [];
    if (data.files && typeof data.files === 'object') {
      // 如果有多个图标数据 - 处理对象格式的 data.files
      // 遍历对象格式的 data.files {文件id: 名字}
      Object.entries(data.files).forEach(([fileId, name]) => {
        dynamicIcons.value.list.push({
          key: Date.now().toString() + Math.random(),
          name: (name as string) || '',
          fileId: fileId || '',
          existingUrl: data.filesUrl?.[fileId] ? `${window.baseConfig.previewResourceUrl}${data.filesUrl[fileId]}` : '',
        });
      });
    } else if (data.fileId) {
      // 兼容原有的单个图标数据
      dynamicIcons.value.list.push({
        key: Date.now().toString(),
        name: data.name || '默认图标',
        fileId: data.fileId,
        existingUrl: getUrl(data),
      });
    }

    formState.files = dynamicIcons.value.list.map((icon) => ({
      name: icon.name,
      fileId: icon.fileId,
    }));

    // 设置已存在的文件信息到上传组件
    imageUploadRef.value?.setExistingFile(data.fileId, getUrl(data));

    await getXXVFrom({ menuId: data.menuId }).then((res: any) => {
      if (res.code === 200) {
        nextTick(() => {
          area.value = res.data;
        });
      }
    });
    const list: any = [];
    data.listTwin.forEach((e: any) => {
      const temp = { ...e };
      temp.listConditional = temp.listConditional.filter((t: any) => t.fieldName);
      list.push(temp);
    });
    formState.listTwin = list;
    dynamicForm.value.list = list;
  }
};
defineExpose({
  add,
  init,
});
</script>
<style scoped lang="scss">
// 设置form表单的label宽度
::v-deep .ant-form-item-label {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.lsdx {
  ::v-deep .ant-form-item-label {
    width: 77px;
    min-width: 75px;
    max-width: 75px;
  }
}

.btn {
  border-radius: 4px;
}

// 图标上传容器样式
.icon-upload-container {
  .icon-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
    }

    .icon-info {
      width: 300px;
      margin-right: 20px;

      .icon-name-label {
        margin-bottom: 6px;
        font-size: 12px;
        font-weight: 500;
        color: #666;
      }

      ::v-deep .ant-input {
        border-radius: 6px;
      }
    }

    .icon-upload-section {
      width: 80px;
      margin-right: 16px;
      text-align: center;

      .icon-upload-label {
        margin-bottom: 6px;
        font-size: 12px;
        font-weight: 500;
        color: #666;
      }

      .icon-upload-wrapper {
        // 覆盖上传组件的默认样式，使其更紧凑
        ::v-deep .ant-upload.ant-upload-select-picture-card {
          width: 64px !important;
          height: 64px !important;
          margin: 0 !important;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;

          &:hover {
            border-color: #40a9ff;
          }
        }

        ::v-deep .ant-upload-list-picture-card {
          display: flex;
          flex-wrap: nowrap;

          .ant-upload-list-item {
            width: 64px !important;
            height: 64px !important;
            margin: 0 !important;
            border-radius: 8px;
          }

          .ant-upload-list-item-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
          }
        }

        ::v-deep .ant-upload-text {
          margin-top: 2px;
          font-size: 11px;
          color: #999;
        }

        ::v-deep .anticon {
          font-size: 16px;
          color: #bfbfbf;
        }
      }
    }

    .icon-actions {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;

      ::v-deep .ant-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 6px;
        box-shadow: none;

        &:hover {
          color: white;
          background-color: #ff4d4f;
        }

        .anticon {
          font-size: 14px;
        }
      }
    }
  }

  .add-icon-btn {
    margin-top: 12px;
  }
}

// .ext-org {
//     display: flex;

//     .delete-icon {
//         width: 10%;
//         text-align: center;
//         align-self: center;
//         cursor: pointer;
//     }
// }
</style>
