{"groups": [{"name": "jrm", "type": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties"}, {"name": "jrm.confine", "type": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties", "sourceMethod": "getConfine()"}], "properties": [{"name": "jrm.confine.default-query-count", "type": "java.lang.Integer", "description": "默认查询数量,当没有指定count时默认", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 30000}, {"name": "jrm.confine.max-array-count", "type": "java.lang.Integer", "description": "最大json数组数量", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 150}, {"name": "jrm.confine.max-object-count", "type": "java.lang.Integer", "description": "最大json对象数量", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 150}, {"name": "jrm.confine.max-query-count", "type": "java.lang.Integer", "description": "最大查询数量", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 2000000}, {"name": "jrm.confine.max-query-depth", "type": "java.lang.Integer", "description": "最大查询深度", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 150}, {"name": "jrm.confine.max-query-page", "type": "java.lang.Integer", "description": "最大分页页数", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 30000}, {"name": "jrm.confine.max-sql-count", "type": "java.lang.Integer", "description": "最大sql执行数量", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 1000}, {"name": "jrm.confine.max-update-count", "type": "java.lang.Integer", "description": "最大更新数量", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties$JrmConfine", "defaultValue": 30000}, {"name": "jrm.db-table-mapping-list", "type": "java.util.List<com.uino.x.pedestal.twin.jrm.core.domain.DbTableMapping>", "description": "数据库与表名规则的映射关系列表", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties"}, {"name": "jrm.head-table-map", "type": "java.util.Map<java.lang.String,java.lang.String>", "description": "请求头和数据表前缀映射", "sourceType": "com.uino.x.pedestal.twin.jrm.properties.JrmProperties"}], "hints": [], "ignored": {"properties": []}}