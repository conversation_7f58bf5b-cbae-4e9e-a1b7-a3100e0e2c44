package com.uino.x.pedestal.twin.jrm;


import apijson.JSON;
import apijson.Log;
import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.twin.jrm.apijson.JrmParser;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.exception.JrmException;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.AbstractSqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.executor.SqlExecutor;
import com.uino.x.pedestal.twin.jrm.json.definition.DefaultJsonDefinition;
import com.uino.x.pedestal.twin.jrm.json.serialize.GsonSerialize;
import com.uino.x.pedestal.twin.jrm.sql.executor.RedisSqlExecutor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.logging.LogLevel;
import org.springframework.lang.Nullable;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * json 关系映射
 * <p>
 * Json Relational Mapping
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/24 10:10
 */
@Data
@Slf4j
public class Jrm {

    /**
     * 响应过滤key
     */
    private static final List<String> RESPONSE_FILTER_KEY = Arrays.asList("sql:generate|cache|execute|maxExecute",
            "trace:stack",
            "time:start|duration|end",
            "trace:throw",
            "time:start|duration|end|parse|sql",
            "debug:info|help",
            "depth:count|max");
    /**
     * 请求类型
     */
    private final RequestType requestType;

    private final SqlExecutor sqlExecutor;

    /**
     * 构造方法
     *
     * @param requestType 请求类型
     * @param sqlExecutor sql执行器
     * <AUTHOR>
     * @date 2021/4/24 10:10
     */
    private Jrm(RequestType requestType, @Nullable SqlExecutor sqlExecutor) {

        AssertUtils.isNotNull(requestType, new JrmException("RequestType must not be null"));
        AssertUtils.isTrue(requestType.isApiJson() || Objects.nonNull(sqlExecutor), new JrmException("SqlExecutor must not be null"));
        this.requestType = requestType;
        this.sqlExecutor = sqlExecutor;
    }


    /**
     * 静态工厂获取{@link Jrm}实例
     *
     * @param requestType 请求类型
     * @return {@link Jrm}
     * <AUTHOR>
     * @date 2021/4/24 10:10
     */
    public static Jrm getInstance(RequestType requestType, @Nullable SqlExecutor sqlExecutor) {

        return new Jrm(requestType, sqlExecutor);
    }

    /**
     * 运行原生sql
     *
     * @param dataSource  数据源
     * @param requestType 请求类型
     * @param executeSql  sql字符串
     * @return sql执行器
     * <AUTHOR>
     * @date 2021/10/13 10:20
     */
    public static SqlExecutor execute(DataSource dataSource,
                                      RequestType requestType,
                                      String executeSql) {

        // 默认查询json描述
        final JsonDefinition jsonDefinition = DefaultJsonDefinition.of(
                "{" +
                        "\"" + requestType + "\":\"" + executeSql + "\""
                        + "}",
                new GsonSerialize());
        return RedisSqlExecutor.of(new AbstractSqlDefinition(dataSource, jsonDefinition, requestType) {
            @Override
            public void buildSql() {

                append(executeSql);
            }
        });
    }

    /**
     * 运行原生sql查询列表json
     *
     * @param dataSource 数据源
     * @param executeSql sql字符串
     * @return 数据集json
     * <AUTHOR>
     * @date 2021/10/13 10:20
     */
    public static String query(DataSource dataSource, String executeSql) {

        return execute(dataSource, RequestType.SELECT, executeSql).query();
    }


    /**
     * 运行原生sql查询列表
     *
     * @param dataSource 数据源
     * @param executeSql sql字符串
     * @return 数据集
     * <AUTHOR>
     * @date 2021/10/13 10:20
     */
    public static List<Map<String, ?>> queryList(DataSource dataSource, String executeSql) {

        return execute(dataSource, RequestType.SELECT, executeSql).queryList();
    }

    /**
     * 开始请求
     *
     * @return 响应结果
     * <AUTHOR>
     * @date 2021/4/24 10:10
     */
    public String request(String jr) {
        log.info("jrm request ===> \n{}\n{}", requestType, jr);
        // 判断是否是apijson请求
        if (requestType.isApiJson()) {
            // 通过日志等级配置来决定apijson的日志输出
            final String level = SpringEnvUtils.getConfig("logging.level.apijson", String.class);
            Log.DEBUG = Objects.nonNull(level) && LogLevel.DEBUG.equals(LogLevel.valueOf(level.toUpperCase()));
            // 移除不必要的响应内容
            final Map<String, Object> responseObject = new JrmParser().setMethod(requestType.getApiJsonRequest()).parseResponse(jr);
            responseObject.entrySet().removeIf(entry -> {
                final String key = entry.getKey();
                final Object value = entry.getValue();
                final boolean contains = RESPONSE_FILTER_KEY.contains(entry.getKey());
                if (Log.DEBUG && contains) {
                    log.debug("{} : {}", key, value);
                }
                return RESPONSE_FILTER_KEY.contains(entry.getKey());
            });
            return JSON.toJSONString(responseObject);
        }
        return sqlExecutor.run();
    }
}
