<!--
 * @Description: 新增
 * @Version: 1.0
 * @Autor: lcm
 * @Date: 2023-03-16 10:13:31
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 09:36:02
-->
<template>
  <a-modal
    :width="800"
    :title="currentStatus === 'edit' ? '编辑示例' : '新增示例'"
    :body-style="{ maxHeight: '600px', overflow: 'auto' }"
    wrap-class-name="cus-modal"
    :open="visible"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    ok-text="确认"
    cancel-text="取消"
    @ok="handleSubmit()"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="form" :rules="rules" label-align="left">
        <a-row :gutter="24">
          <a-col :md="24" :sm="24">
            <a-form-item name="expName" label="名称" has-feedback>
              <a-input v-model:value="form.expName" placeholder="请输入示例名称" :maxlength="30" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item name="templateTags" label="标签" has-feedback>
              <a-cascader
                v-model:value="form.templateTags"
                :defaultValue="form.templateTags"
                :show-checked-strategy="Cascader.SHOW_CHILD"
                style="width: 100%"
                multiple
                max-tag-count="responsive"
                :options="caoptions"
                placeholder="请选择标签"
                :checkStrictly="true"
              >
                <template #tagRender="data">
                  <a-tag :key="data.value" :color="getTagColor(data.label)">{{ data.label }}</a-tag>
                </template>
              </a-cascader>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-form-item name="expNotes" label="示例说明" has-feedback>
              <QuillEditor ref="quillEditorRef" v-model.content="form.expNotes" style="height: 180px" :options="options" content-type="html" @update:content="textChange" />
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24" class="form-item">
            <a-form-item name="expFile" label="示例文件" has-feedback>
              <a-upload
                v-model:fileList="fileList"
                :before-upload="handleBeforeUploadFile"
                :show-upload-list="true"
                accept=".zip"
                :multiple="false"
                :max-count="1"
                :custom-request="customRequest"
                @remove="handleRemoveFile"
              >
                <a-button type="primary"> 点击上传 </a-button>
              </a-upload>
              <div v-show="uploadInfo.progressFlag">
                <a-progress
                  :percent="uploadInfo.percent"
                  size="small"
                  :stroke-color="{
                    from: '#108ee9',
                    to: themeColor,
                  }"
                />
              </div>
            </a-form-item>
            <span class="form-item-notice keep-px">
              <a-tooltip placement="right">
                <template #title>
                  <div>
                    支持文件格式: zip, 大小限制: 1000M,
                    <a style="color: #ef7b1a" @click="handleDownload" v-if="hasPerm('sys-sample:sample-url')">下载示例模板</a>
                  </div>
                  <div>
                    <p>上传格式如下图所示：</p>
                    <img src="@/assets/img/default/sample-zip.png" style="width: 100%" />
                  </div>
                </template>
                <question-circle-outlined style="color: #ef7b1a" />
              </a-tooltip>
            </span>
          </a-col>
          <a-col :md="24" :sm="24" class="form-item">
            <a-form-item name="previewId" label="封面图" has-feedback>
              <a-upload
                v-model:fileList="picList"
                :before-upload="beforeUpload"
                accept=".png, .jpg, .jpeg, .gif, .webp, .apng"
                :show-upload-list="false"
                list-type="picture-card"
                :multiple="false"
                :max-count="1"
                :custom-request="customRequestImg"
                @remove="handleRemovePic"
              >
                <img v-if="imageUrl" :src="imageUrl" alt="avatar" class="avatar-img" />
                <div v-else>
                  <plus-outlined />
                  <div class="ant-upload-text">上传</div>
                </div>
              </a-upload>
            </a-form-item>
            <span class="form-item-notice keep-px">
              <a-tooltip title="支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M" placement="right">
                <question-circle-outlined style="color: #ef7b1a" />
              </a-tooltip>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, nextTick, toRaw } from 'vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import { QuillEditor } from '@vueup/vue-quill';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { getImgBase64 } from '@/utils/util';
import { getSysPage } from '@/api/operator/system/developConfig';
// 保存示例
import { saveExample, editExample, sampleGroup, getDownload } from '@/api/operator/sourceManage/examples';
// 上传图片
import { upload } from '@/api/operator/companyManage/index';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { useUserStore } from '@/store/user';
import { useSettingStore } from '@/store/setting';
const store = useSettingStore();
const { themeColor } = storeToRefs(store);
// const props = defineProps({
//     review: Number,
// });
const emit = defineEmits(['ok']);
const visible = ref(false);
const confirmLoading = ref(false);
const formRef = ref();
const form = ref({
  expName: '',
  expNotes: '',
  file: '',
  previewId: '',
  id: '',
  templateTags: [],
  status: 0,
});
const editorKey = ref(0);
let tags: { [key: string]: any } = {};
// 标签参数
const caoptions = ref<CascaderProps['options']>([]);
// 自定义图片粘贴事件
const handleCuctomMatcher = (node: any, Delta: any) => {
  const ops: any = [];
  Delta.ops.forEach((item: any) => {
    if (item.insert && typeof item.insert === 'string') {
      ops.push({
        insert: item.insert,
      });
    }
  });
  Delta.ops = ops;
  return Delta;
};
const options = ref({
  modules: {
    clipboard: {
      matchers: [['img', handleCuctomMatcher]],
    },
  },
  placeholder: '请输入示例说明',
  readOnly: false,
  theme: 'snow',
});
const fileList: any = ref([]);
const picList: any = ref([]);
let currentStatus = '';
const colorMap = ref(new Map());
const tagParents = ref([]);
const tagsParentMap = ref(new Map());
// 获取标签数据
const getSampleGroup = () => {
  return new Promise((resolve) => {
    sampleGroup().then((res) => {
      tags = {};
      const data = res.data.map((item: any) => {
        tags[item.id] = item.tags;
        tagParents.value.push(item.id);
        return {
          value: item.id,
          label: `${item.groupName}`,
          children: item.tags?.map((ele: any) => {
            // const childArr = tagsMap.value.get(item.id);
            // childArr.push(ele.id);
            colorMap.value.set(`${ele.tagName}`, ele.color);
            tagsParentMap.value.set(ele.id, item.id);
            return {
              value: ele.id,
              label: `${ele.tagName}`,
            };
          }),
        };
      });

      caoptions.value = data.filter((item: any) => item.children);
      resolve(caoptions.value);
    });
  });
};
// 获取标签颜色
const getTagColor = (val: string) => {
  return colorMap.value.get(val) || 'blue';
};
// 校验文件格式
const validateExpFile = (rule: any) => {
  const value = fileList.value[0];

  return new Promise<void>((resolve, reject) => {
    if (value) {
      const fileType = value.name.split('.').pop().toLowerCase();
      const allowedFormats = ['zip'];
      const maxSize = 1000 * 1024 * 1024;

      if (allowedFormats.includes(fileType)) {
        if (value.size) {
          if (value.size <= maxSize) {
            // 封面图上传成功且满足格式和大小要求
            resolve();
          } else {
            reject(new Error('示例文件大小不能超过1000MB！'));
          }
        } else {
          resolve();
        }
      } else {
        reject(new Error('只支持zip格式的文件！'));
      }
    } else {
      reject(new Error('请上传示例文件！'));
    }
  });
};
// 校验tag长度
const checkTagLength = (rule: any, value: any, callback: Function) => {
  let count = 0;
  value.forEach((item: any) => {
    if (item.length === 1) {
      if (tagParents.value.includes(item[0])) {
        const parent = caoptions.value.find((o) => o.value === item[0]);
        if (parent && parent.children) {
          count += parent.children.length;
        }
      } else {
        count++;
      }
    } else {
      count = count + item.length - 1;
    }
  });
  if (count > 10) {
    return Promise.reject(new Error('最多选择10个标签'));
  }
  return Promise.resolve();
};
const rules: any = {
  templateTags: [
    { required: true, message: '请选择标签' },
    { required: true, validator: checkTagLength },
  ],
  expName: [{ required: true, message: '请输入示例名称！', trigger: 'blur' }],
  expNotes: [{ required: true, message: '请输入示例说明！', trigger: 'blur' }],
  expFile: [{ required: true, validator: validateExpFile, trigger: 'change' }],
  previewId: [{ required: true, message: '请上传封面图!', trigger: 'blur' }],
};
// 上传之前限制
const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif' || file.type === 'image/webp' || file.type === 'image/apng';
  if (!isJpgOrPng) {
    useGlobalMessage('error', '支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片');
  }
  const isLt2M = file.size / 1024 / 1024 < 5;
  if (!isLt2M) {
    useGlobalMessage('error', '图片大小不超过5M');
  }
  return isJpgOrPng && isLt2M;
};
// 富文本编辑器
const quillEditorRef = ref();
// 初始化
const init = async (type: string, record: any, pid: string) => {
  visible.value = true;
  currentStatus = type;

  await getSampleGroup();
  if (type === 'add') {
    // @ts-ignore
    form.value = {
      expName: '',
      expNotes: '',
      file: '',
      previewId: '',
      id: '',
      templateTags: [],
    };
    fileList.value = [];
    picList.value = [];
    visible.value = true;
    nextTick(() => {
      formRef.value.resetFields();
    });
  } else if (type === 'edit') {
    form.value.expName = record.name;
    form.value.expNotes = record.remark;
    form.value.file = record.url;
    form.value.id = record.id;
    form.value.previewId = record.previewId;
    form.value.status = record.status;
    form.value.templateTags = [];
    const arr: string | any[] | Blob = [];
    record.functionExampleTags.forEach((item: any) => {
      if (tagsParentMap.value.get(item.tagId)) arr.push([tagsParentMap.value.get(item.tagId), item.tagId]);
    });
    nextTick(() => {
      quillEditorRef.value.setHTML(form.value.expNotes);
      form.value.templateTags = arr;
    });
    if (record.url) {
      fileList.value[0] = {
        name: `${record.url?.split('/')[record.url.split('/').length - 2]}.zip`,
        uid: Math.floor(Math.random() * (999 - 100 + 1)) + 100,
      };
    }

    imageUrl.value = getUrl(record);
  }
};
// 获取拼接地址
const getUrl = (item: any) => {
  return `${window.config.previewUrl}${item.previewUrl}`;
};
// 取消
const handleCancel = () => {
  formRef.value.resetFields();
  visible.value = false;
  imageUrl.value = '';
  fileList.value = [];
  picList.value = [];
  confirmLoading.value = false;
  form.value = {
    expName: '',
    expNotes: '',
    file: '',
    previewId: '',
    id: '',
    templateTags: [],
    status: 0,
  };
  quillEditorRef.value.setHTML('');
  editorKey.value += 1;
};
const uploadInfo = ref({
  percent: 0,
  progressFlag: false,
});
const progresshandel = (data: any) => {
  if (data && data.loaded && data.total) {
    // uploadInfo.value.percent = Number(((data.loaded / data.total) * 90).toFixed(2));
    uploadInfo.value.percent = Math.round((data.loaded * 100) / data.total);
  }
};
// 提交
const handleSubmit = () => {
  confirmLoading.value = true;

  formRef.value
    .validate()
    .then(() => {
      const { expName, expNotes, file, previewId, id, templateTags, status } = form.value;
      const formData = new FormData();

      const arr: string | any[] | Blob = [];

      templateTags.forEach((val: any) => {
        if (val[1]) {
          const { id, tagName, color } = tags[val[0]].find((item: any) => {
            return item.id === val[1];
          });
          arr.push({
            tagId: id,
            tagName,
            color,
          });
        } else if (val[0]) {
          tags[val[0]].forEach((item: any) => {
            const { id, tagName, color } = item;
            arr.push({
              tagId: id,
              tagName,
              color,
            });
          });
        }
      });
      // 添加要传递的参数
      formData.append('name', expName);
      formData.append('file', file);
      formData.append('previewId', previewId);
      formData.append('remark', expNotes);
      formData.append('sampleDetails', '');
      formData.append('functionExampleValue', JSON.stringify(arr));
      // @ts-ignore
      formData.append('status', status || 3);
      formData.append('createUser', useUserStore().userInfo.id);
      uploadInfo.value.percent = 0;
      uploadInfo.value.progressFlag = true;
      if (currentStatus === 'add') {
        saveExample(formData, progresshandel)
          .then((oo: any) => {
            if (oo.code === 200) {
              useGlobalMessage('success', '功能示例新增成功');
              confirmLoading.value = false;
              visible.value = false;
              emit('ok');
            } else {
              useGlobalMessage('error', oo.message);
              confirmLoading.value = false;
            }
            // formRef.value.resetFields();
            handleCancel();
            uploadInfo.value.percent = 0;
            uploadInfo.value.progressFlag = false;
          })
          .catch(() => {
            confirmLoading.value = false;
            uploadInfo.value.percent = 0;
            uploadInfo.value.progressFlag = false;
          });
      } else {
        if (typeof file === 'string' || file === null || file === undefined) {
          formData.delete('file');
        }
        formData.append('id', id);
        uploadInfo.value.percent = 0;
        uploadInfo.value.progressFlag = true;
        editExample(formData, progresshandel)
          .then((oo: any) => {
            if (oo.code === 200) {
              useGlobalMessage('success', '功能示例修改成功');
              confirmLoading.value = false;
              visible.value = false;
              emit('ok');
            } else {
              useGlobalMessage('error', oo.message);
              confirmLoading.value = false;
            }
            // formRef.value.resetFields();
            handleCancel();
            uploadInfo.value.percent = 0;
            uploadInfo.value.progressFlag = false;
          })
          .catch(() => {
            confirmLoading.value = false;
            uploadInfo.value.percent = 0;
            uploadInfo.value.progressFlag = false;
          });
      }
    })
    .catch((error: any) => {
      confirmLoading.value = false;
    });
};
const handleBeforeUploadFile = (file: any) => {
  const fileName = file.name;
  const suffixName = fileName.substring(fileName.lastIndexOf('.'));
  if (suffixName !== '.zip') {
    fileList.value = [];
    useGlobalMessage('error', '请上传.zip格式的文件');
    return false;
  }

  // 清空旧文件，只保留新文件
  fileList.value = [file];
  form.value.file = file;
  // 返回 false 阻止文件上传，返回 true 允许文件上传
  return true;
};
// 上传文件
const customRequest = async (data: any) => {
  const { file } = data;
  fileList.value[0] = file;
  form.value.file = file;
};

// 下载
const handleDownload = () => {
  getSysPage({
    code: 'SHI_LI_MO_BAN_DI_ZHI',
  }).then((res: any) => {
    const val = res.data?.rows[0]?.value;
    if (val) {
      window.open(val, '_blank');
    } else {
      useGlobalMessage('error', '请先配置示例模板下载地址');
    }
  });
  // getDownload().then((res) => {
  //   const link = document.createElement('a');
  //   const url = `${window.config.previewUrl}${res.data}`;
  //   link.href = url;
  //   // link.download = 'example.txt';
  //   link.click();
  //   URL.revokeObjectURL(url);
  // });
};

const textChange = (e: any) => {
  form.value.expNotes = e;
};
// 预览logo图片地址
const imageUrl = ref();
// 上传封面图
const customRequestImg = async (data: any) => {
  const { file } = data;
  picList.value[0] = file;
  if (picList.value[0]) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bucketName', 'twinfile');
    const res = await upload(formData);
    if (res.code === 200) {
      form.value.previewId = res.data?.id;
      // 上传图片预览
      getImgBase64(file, (base64Url: string) => {
        imageUrl.value = base64Url;
      });
    } else {
      useGlobalMessage('error', res.message);
      form.value.previewId = '';
    }
  }
};

// 移除上传文件
const handleRemoveFile = () => {
  fileList.value = [];
  form.value.file = '';
};

// 移除上传图片
const handleRemovePic = () => {
  form.value.previewId = '';
};
defineExpose({ init });
</script>
<style lang="scss" scoped>
// 设置form表单的label宽度
::v-deep .ant-form-item-label {
  width: 100px !important;
  min-width: 100px;
  max-width: 100px !important;
}

::v-deep .dark-image {
  margin-left: 135px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.form-item {
  position: relative;

  .form-item-notice.keep-px {
    // margin-left: 5px;
    position: absolute;
    top: 8px;
    left: 80px;
  }

  .download-btn {
    position: absolute;
    top: 0;
    left: 140px;
  }

  :deep(.ant-radio-wrapper) {
    margin-right: 6px;
  }
}

.update-box {
  // width: 200px;
  display: inline-block;
  height: 30px;
  padding: 0 20px;
  background-color: #52c41a;
  border-radius: 8px;
}

header {
  display: flex;
  align-items: center;
  width: 100%;

  .i-box {
    width: 50%;

    &:nth-of-type(2) {
      padding-left: 10px;
    }
  }
}
</style>
