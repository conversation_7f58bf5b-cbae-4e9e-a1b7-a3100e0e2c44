<template>
  <div class="content">
    <!-- 搜索栏 -->
    <div class="search-wrap">
      <div class="search-content">
        <div class="search-item">
          <span class="search-label">关键词</span>
          <a-input v-model:value="queryParam.searchValue" allow-clear placeholder="请输入设备编码" class="search-input" @keyup.enter="clickSearch()" />
        </div>
        <div class="search-btns">
          <a-button type="primary" class="search-btn" @click="clickSearch()">查询</a-button>
          <a-button class="search-btn" @click="reset()">重置</a-button>
          <ConditionBox ref="conditionBoxRef" :assembly-filter-group="assemblyFilterGroup" @call-back="conditionBox" />
        </div>
      </div>
      <div class="table-handle">
        <a-button v-if="!isMap && hasPerm('twin-class:add')" type="primary" :disabled="chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID" class="handle-btn" @click="handelAdd">
          新增孪生体
        </a-button>
        <div>
          <a-button v-if="hasPerm('twin-body-data:import-excel')" class="handle-btn" @click="handleImport"><upload-outlined />导入</a-button>
          <a-popconfirm placement="topRight" ok-text="是" cancel-text="否" @confirm="batchExport">
            <template #title>
              <p>该操作将对选中的孪生体数据进行下载</p>
              <p>若未选中，将下载全部，是否继续？</p>
            </template>
            <a-button v-if="hasPerm('twin-body-data:export-excel')" class="handle-btn" :loading="downLoading"><download-outlined />导出</a-button>
          </a-popconfirm>
          <a-button v-if="hasPerm('twin-body-data:delete-twin-body-point-info')" class="handle-btn" :disabled="tableSelects.keys.length <= 0" @click="handleDelete">
            <template #icon>
              <DeleteOutlined />
            </template>
            批量删除
          </a-button>
        </div>
      </div>
    </div>
    <div class="table-wrap">
      <div ref="table" class="table-content">
        <a-table
          class="table"
          :scroll="{ x: 'max-content', y: 'calc(100% - 40px)' }"
          :custom-row="customRow"
          :pagination="false"
          size="small"
          :loading="loading"
          :row-selection="rowSelection"
          :row-key="(record: any) => record.uuid"
          :columns="columns"
          :data-source="tabData"
          @change="tableChange"
        >
          <!-- <template #expandedRowRender="{ record }">
                            <div>
                                <p>设备名称：{{ record.deviceName }}</p>
                            </div>
                        </template> -->
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'action'">
              <div class="table-actions">
                <a v-if="hasPerm('twin-class:modify-form')" @click.stop="handleEdit(record)">更新</a>
                <a-popconfirm
                  v-if="!(chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID && chooseTwinData.dataType !== 'FORM')"
                  placement="topRight"
                  title="确认删除？"
                  @confirm="() => twinDataDelete([record.uuid])"
                >
                  <a v-if="hasPerm('twin-body-data:delete-twin-body-point-info')" @click.stop>删除</a>
                </a-popconfirm>
                <a v-show="isMap && !checkMapData" @click.stop="flyToThing(record)">
                  <span v-if="record.wgs84_position || record.gcj02_position"> 定位 </span>
                  <span v-else>
                    <a-tooltip placement="topLeft" title="请先在地图上对孪生体进行摆点，再进行定位操作"> 定位 </a-tooltip>
                  </span>
                </a>
              </div>
            </template>
            <template v-if="column.dataIndex === 'iconStatus'">
              <span style="display: inline-block; width: 10px; height: 10px; margin-right: 8px; border-radius: 5px" :style="{ backgroundColor: text ? '#1DBE53' : '#C3C4C7' }"></span>
              <span>{{ text ? '已标注' : '未标注' }}</span>
            </template>
            <!-- 处理单选框和多选框显示 -->
            <template v-for="item in selectOrCheckbox">
              <template v-if="item === column.key">
                <span :key="item">{{ jsonParseArr(text) }}</span>
              </template>
            </template>
            <!-- 处理富文本 -->
            <template v-for="item in editorSlotName">
              <template v-if="item === column.key">
                <a :key="item" @click="showEditor(text)">详情</a>
              </template>
            </template>
            <!-- 处理文件 -->
            <template v-for="item in downloadFileSlotNames">
              <template v-if="item === column.key">
                <preview-and-download :key="item" :item="item" :record="record" :text="text" />
              </template>
            </template>
            <!-- 处理关联孪生体 -->
            <template v-for="item in releativeSlotNames">
              <template v-if="item === column.key">
                <a v-if="relativeLength(record, column.dataIndex)" :key="item" @click="showReletive(record, column)">详情</a>
                <span v-else :key="item + '1'">--</span>
              </template>
            </template>
          </template>
          <template v-if="chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID && chooseTwinData.dataType !== 'FORM'" #expandedRowRender="{ record }">
            <span v-for="(item, i) in userDetailCol" :key="i" style="display: block"> {{ item.name }}:{{ record[item.key] }} </span>
          </template>
        </a-table>
        <div class="pagination">
          <a-pagination v-if="tabData.length > 0" v-bind="paginationConfig" @change="paginationChange" />
        </div>
      </div>
    </div>
  </div>
  <!-- 孪生体数据新增和更新 -->
  <AddEditForm ref="addEditFormRef" @success="editAndAddCb" />
  <!-- 孪生体对象导入 -->
  <upload ref="uploadRef" @ok="uploadCb" />
  <!-- 富文本预览 -->
  <a-modal
    :title="editorDialog.title"
    :body-style="{ maxHeight: '80vh', paddingBottom: '40px', overflowY: 'auto', overflowX: 'hidden' }"
    wrap-class-name="cus-modal"
    :footer="null"
    :width="900"
    :open="editorDialog.show"
    :mask-closable="false"
    @cancel="editorDialog.show = false"
  >
    <div class="editor-html" v-html="editorDialog.content"></div>
  </a-modal>
  <!-- 关联孪生体 -->
  <Reletive ref="reletiveRef" />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { Modal } from 'ant-design-vue';
import { deleteTwinData, exportTwinData, getAllVersionSceneCodes, getAllVersionSceneIds, getSenceByTwinCode, getTwinData, getUserIdMapping } from '@/api/business/twinObject';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import useTableScrollY from '@/hooks/useTableScrollY';
import { downloadfile } from '@/utils/util';
import AddEditForm from './AddEditForm.vue';
import upload from './upload.vue';
import PreviewAndDownload from './PreviewAndDownload.vue';
import ConditionBox from './ConditionBox.vue';
import Reletive from './Reletive.vue';
import { useSceneStore } from '@/store/scene';
import { UserInfoCache } from '@/utils/UserInfoCache';

const sceneStore = useSceneStore();
const emits = defineEmits(['setAssMapOpenEdit', 'refreshMap']);
const props = defineProps({
  // 地图查看数据
  checkMapData: {
    type: Boolean,
    default: false,
  },
});

// 处理关联孪生体边界问题
const relativeLength = (record: any, dataIndex: any) => {
  try {
    const cVal = record[dataIndex];
    if (cVal) {
      return JSON.parse(cVal).length;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const queryParam = reactive<any>({
  searchValue: '',
  scene_id: '', // 所属场景
  data_source: '', // 点位数据来源
  Building_data: '', // 所属建筑
  Floor_data: '', // 所属楼层
  Room_data: '', // 房间
  scene_ids: [], // 历史版本所有场景id
  Name_data: '', //名称
});

const levelObj = {
  PARK: '园区',
  MAP: '地图',
  OTHER: '其他',
};

const DEFAULT_TWIN_CLASS_GROUP_ID = '1476825472376508418';

const loading = ref(false);
const columns = ref<any>([]);
const tabData = ref([]);
const selects = ref<any>([]);
const searchs = ref<any>([]);
let chooseTwinData: any = {};
let filelds: any = {};
const formJson = ref<any>({});
const isMap = ref(false);
const sceneList = ref<any>([]);
const sourceType = ref<any>([]);
const userDetailCol = ref<any>([]);

// 用户信息缓存实例
const userInfoCache = new UserInfoCache(2000);

// 初始化用户信息缓存
const initUserInfoCache = async () => {
  try {
    const res = await getUserIdMapping({ id: 1, pageSize: 100 });
    // res.code maybe string '200'
    if (res.data) {
      userInfoCache.setBatch(res.data);
    }
  } catch (error) {
    // 静默处理错误
  }
};

// 获取用户信息（优先从缓存获取）
const getUserInfo = async (userId: number | string) => {
  if (!userId) return '';

  const stringUserId = String(userId);

  // 从缓存获取
  const cachedUser = userInfoCache.get(stringUserId);
  if (cachedUser) {
    return cachedUser.name || '';
  }

  try {
    const res = await getUserIdMapping({ id: Number(stringUserId), pageSize: 10 });
    if (res.code === 200 && res.data) {
      userInfoCache.setBatch(res.data);
      const user = userInfoCache.get(stringUserId);
      return user?.name || '';
    }
  } catch (error) {
    // 静默处理错误
  }

  return '';
};

// 格式化数组对象
const jsonParseArr = (data: string) => {
  try {
    return JSON.parse(data).toString();
  } catch (err) {
    return data;
  }
};
// 编辑和新增回调
const editAndAddCb = () => {
  getData();
  emits('refreshMap');
};

// 上传回调
const uploadCb = () => {
  getData();
  emits('refreshMap');
};

// 自定义点击行事件
const customRow = (record: any) => {
  return {
    onClick: () => {
      // 是在地图上导入的点位，还未摆点
      if (isMap.value && !record.iconStatus && record.data_source_source === 'ASSET_IMPORT') {
        emits('setAssMapOpenEdit', record);
      }
    },
  };
};

const assemblyFilterGroup = () => {
  const result = [];
  // 排除地图和表单
  if (!isMap.value && chooseTwinData.dataType !== 'FORM') {
    result.push({
      label: '所属场景',
      type: 'select',
      fieldValue: queryParam.scene_id ? queryParam.scene_id : '',
      fieldName: 'scene_id',
      data: sceneList.value,
      placeholder: '请选择孪生体点位所属场景',
    });
  }
  // 排除默认孪生体和表单
  if (chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID && chooseTwinData.dataType !== 'FORM') {
    result.push({
      label: '点位数据来源',
      type: 'select',
      fieldValue: queryParam.data_source ? queryParam.data_source : '',
      fieldName: 'data_source',
      data: sourceType.value,
      placeholder: '请选择孪生体',
    });
  }
  // 园区孪生体
  if (chooseTwinData.dataType !== 'FORM' && chooseTwinData.level === 'PARK') {
    if (chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID) {
      // 默认建筑孪生 | 楼层 | 房间孪生体
      if (chooseTwinData.code === 'BUILDING' || chooseTwinData.code === 'FLOOR' || chooseTwinData.code === 'ROOM') {
        if (chooseTwinData.code === 'BUILDING') {
          result.push({
            label: '名称',
            type: 'input',
            fieldValue: queryParam.Name_data ? queryParam.Name_data : '',
            fieldName: 'Name_data',
            placeholder: '请输入建筑名称',
          });
        } else {
          result.push({
            label: '所属建筑',
            type: 'input',
            fieldValue: queryParam.Building_data ? queryParam.Building_data : '',
            fieldName: 'Building_data',
            placeholder: '请输入所属建筑名称',
          });
        }
      }
      // 默认楼层 | 房间孪生体
      if (chooseTwinData.code === 'FLOOR' || chooseTwinData.code === 'ROOM') {
        result.push({
          label: chooseTwinData.code === 'FLOOR' ? '名称' : '所属楼层',
          type: 'input',
          fieldValue: queryParam.Floor_data ? queryParam.Floor_data : '',
          fieldName: 'Floor_data',
          placeholder: chooseTwinData.code === 'FLOOR' ? '请输入楼层名称' : '请输入所属楼层名称',
        });
      }
      // 默认房间孪生体
      if (chooseTwinData.code === 'ROOM') {
        result.push({
          label: '名称',
          type: 'input',
          fieldValue: queryParam.Name_data ? queryParam.Name_data : '',
          fieldName: 'Name_data',
          placeholder: '请输入名称',
        });
      }
    } else {
      // 园区摆点孪生体
      result.push({
        label: '所属建筑',
        type: 'input',
        fieldValue: queryParam.Building_data ? queryParam.Building_data : '',
        fieldName: 'Building_data',
        placeholder: '请输入所属建筑',
      });
      result.push({
        label: '所属楼层',
        type: 'input',
        fieldValue: queryParam.Floor_data ? queryParam.Floor_data : '',
        fieldName: 'Floor_data',
        placeholder: '请输入所属楼层',
      });
      result.push({
        label: '所属房间',
        type: 'input',
        fieldValue: queryParam.Room_data ? queryParam.Room_data : '',
        fieldName: 'Room_data',
        placeholder: '请输入所属房间',
      });
    }
  }
  searchs.value.forEach((item: any) => {
    const val = item.options.showTime ? queryParam.RiQiXuanZeKuang : queryParam[item.model];
    result.push({
      label: item.label,
      type: item.options.showTime ? 'date' : 'input',
      fieldValue: val || '',
      fieldName: item.options.showTime ? 'RiQiXuanZeKuang' : item.model,
      placeholder: item.options.showTime ? '请选择时间' : '请输入',
    });
  });
  selects.value.forEach((item: any) => {
    result.push({
      label: item.label,
      type: item.type,
      fieldValue: queryParam[item.model] ? queryParam[item.model] : '',
      fieldName: item.model,
      data: item.options.options,
      placeholder: '请选择',
    });
  });
  return result;
};
// 高级筛选
const conditionBox = (searchs: any) => {
  let hasBlongScene = false;
  searchs.forEach((item: any) => {
    queryParam[item.fieldName] = item.fieldValue;
    if (item.label === '所属场景') {
      hasBlongScene = true;
    }
  });
  if (hasBlongScene) {
    setSceneIdsParamsAndRefresh();
  } else {
    getData();
  }
  tableSelects.value.list = [];
  tableSelects.value.keys = [];
};

const setSceneIdsParamsAndRefresh = () => {
  if (queryParam.scene_id) {
    // 是否是默认孪生体
    const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
    if (isDefault) {
      // 查所有版本场景
      getAllVersionSceneCodes({ uuid: queryParam.scene_id }).then((res: any) => {
        queryParam.scene_ids = res.data;
        getData();
      });
    } else {
      // 查所有版本场景
      getAllVersionSceneIds({ uuid: queryParam.scene_id }).then((res: any) => {
        queryParam.scene_ids = res.data;
        getData();
      });
    }
  } else {
    queryParam.scene_ids = [];
    getData();
  }
};

// 飞到地图点位
const flyToThing = (record: any) => {
  const thing = window.app?.query(`#${record.uuid}`)[0];
  if (thing) {
    if (sceneStore.thingjsVersion === 1) {
      window.app?.camera.earthFlyTo({
        object: thing,
        pitch: 45,
        height: 600,
      });
    } else if (sceneStore.thingjsVersion === 2) {
      // @ts-ignore
      const position = thing.position ? THING.EARTH.Utils.convertWorldToLonlat(thing.position) : thing.coordinates[0][0];
      const [lng, lat] = position;
      // @ts-ignore
      window.app?.camera.earthFlyTo({
        lonlat: [lng, lat],
        height: 600,
      });
    }
  }
};

// 初始化
const init = async (item: any) => {
  chooseTwinData = item;
  isMap.value = item.level === 'MAP';

  // 初始化用户信息缓存
  await initUserInfoCache();
  if (isMap.value) {
    // 地图
    sourceType.value = [
      { code: 'TWIN', value: '孪生对象摆点' },
      { code: 'ASSET_IMPORT', value: '资产数据导入' },
      { code: 'API_PUSH', value: '接口数据推送' },
      { code: 'ASSET', value: '资产数据摆点' },
    ];
    userDetailCol.value = [
      { key: 'wgs84_position', name: 'WGS84坐标系' },
      { key: 'gcj02_position', name: 'GCJ02坐标系' },
      { key: 'gis_height', name: '离地高度' },
      { key: 'position_body', name: '点位数据信息' },
    ];
  } else {
    // 园区
    sourceType.value = [
      { code: 'TWIN', value: '孪生对象摆点' },
      { code: 'ASSET', value: '资产数据摆点' },
      { code: 'ASSET_IMPORT', value: '资产数据导入' },
      { code: 'CAD', value: 'CAD点位转换' },
      { code: 'MMD', value: '模模搭点位转换' },
      { code: 'GIS', value: 'GIS点位转换' },
    ];
    userDetailCol.value = [
      { key: 'current_level', name: '所在层级' },
      { key: 'buildingName', name: '所属建筑' },
      { key: 'floorName', name: '所属楼层' },
      { key: 'roomName', name: '所属房间' },
      { key: 'parent_user_id', name: '场景层级中的物体ID' },
      { key: 'wgs84_position', name: 'WGS84坐标系' },
      { key: 'gcj02_position', name: 'GCJ02坐标系' },
      { key: 'gis_height', name: '离地高度' },
      { key: 'position_body', name: '点位数据信息' },
      { key: 'position', name: '位置坐标' },
    ];
    // 获取场景列表
    getSenceByTwinCode().then((res: any) => {
      sceneList.value = res.data;
    });
  }
  if (item.dataType === 'FORM') {
    userDetailCol.value = [];
  }
  getColumns();
  getData();
};

const table = ref();
const { scrollY } = useTableScrollY(table);

const clickSearch = () => {
  paginationConfig.value.current = 1;
  paginationConfig.value.pageSize = 10;
  getData();
};

// 分页配置
const paginationConfig = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: (total: number) => `共有${total}条`,
  showSizeChanger: true,
  showQuickJumper: true,
  size: 'small' as any,
});

const getData = async (sorts: any = {}) => {
  const sort = sortParam.value;
  loading.value = true;
  const table = chooseTwinData.code;

  // 是否是默认孪生体
  const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
  // 是否包含场景信息
  const hasScene = chooseTwinData.level !== 'MAP' && chooseTwinData.dataType !== 'FORM';
  // join信息是否包含场景
  const joinValue = {};
  if (isDefault) {
    // 新增的菜单/地图矢量数据/地图层级，虽然是default，但是不包含场景信息
    if (chooseTwinData.dataType !== 'FORM') {
      // @ts-ignore
      joinValue['&/Scene_record'] = {
        enable: '1',
      };
    }
  } else if (hasScene) {
    // @ts-ignore
    joinValue['</Scene_record'] = {};
  }
  // // 查询条件组装
  // if (queryParam.Building_data) {
  //   if (chooseTwinData.code === 'ROOM') {
  //     // @ts-ignore
  //     joinValue['&/Room_data'] = {};
  //     // @ts-ignore
  //     joinValue['&/Floor_data'] = {};
  //   } else if (chooseTwinData.code === 'FLOOR') {
  //     // @ts-ignore
  //     joinValue['&/Floor_data'] = {};
  //   }
  //   // @ts-ignore
  //   joinValue['&/Building_data'] = {
  //     name$: queryParam.Building_data ? `%${queryParam.Building_data}%` : null,
  //     setting_name$: queryParam.Building_data ? `%${queryParam.Building_data}%` : null,
  //     '@combine': 'name$ | setting_name$',
  //   };
  // }
  // if (queryParam.Floor_data) {
  //   if (chooseTwinData.code === 'ROOM') {
  //     // @ts-ignore
  //     joinValue['&/Room_data'] = {};
  //   }
  //   // @ts-ignore
  //   joinValue['&/Floor_data'] = {
  //     name$: queryParam.Floor_data ? `%${queryParam.Floor_data}%` : null,
  //     setting_name$: queryParam.Floor_data ? `%${queryParam.Floor_data}%` : null,
  //     '@combine': 'name$ | setting_name$',
  //   };
  // }
  // if (queryParam.Room_data) {
  //   // @ts-ignore
  //   joinValue['&/Room_data'] = {
  //     name$: queryParam.Room_data ? `%${queryParam.Room_data}%` : null,
  //     setting_name$: queryParam.Room_data ? `%${queryParam.Room_data}%` : null,
  //     '@combine': 'name$ | setting_name$',
  //   };
  // }
  let order = '';
  if (sort.sortField && sort.sortRule) {
    let { sortField } = sort;
    if (sort.sortField === 'sceneName') {
      sortField = isDefault ? 'scene_code' : 'scene_id';
    } else if (sort.sortField === 'sceneDataName') {
      sortField = 'user_id';
    } else if (sort.sortField === 'buildingName') {
      sortField = 'user_building_id';
    } else if (sort.sortField === 'floorName') {
      sortField = 'user_floor_id';
    } else if (sort.sortField === 'roomName') {
      sortField = 'user_room_id';
    }
    order = `${sortField}${sort.sortRule === 'ASC' ? '+' : '-'}`;
  } else {
    order = '';
  }

  const search: any = {};
  if (queryParam.searchValue) {
    filelds.forEach((item: any) => {
      if (item.type !== 'uploadFile') {
        search[`${item.model}$`] = `%${queryParam.searchValue}%`;
      }
    });
    search.uuid$ = `%${queryParam.searchValue}%`;
    if (Object.keys(search).length > 1) {
      const combineArr: any = [];
      Object.keys(search).forEach((key) => {
        combineArr.push(key);
      });
      search['@combine'] = combineArr.join(',');
    }
  }

  if (queryParam.data_source) {
    search.data_source = queryParam.data_source;
  }

  if (queryParam.scene_id) {
    if (isDefault) {
      // 主场景+子场景
      search['scene_code{}'] = queryParam.scene_ids;
      // 主场景
      // search['scene_code{}'] = [queryParam.scene_id];
    } else {
      // 主场景+子场景
      search['scene_id{}'] = queryParam.scene_ids;
      // 主场景
      // search['scene_id{}'] = [queryParam.scene_id];
    }
  }

  selects.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      if (e.type === 'checkbox') {
        if (queryParam[e.model].length !== 0) {
          search[e.model] = queryParam[e.model].toString();
        }
      } else {
        search[e.model] = queryParam[e.model];
      }
    }
  });

  searchs.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      search[`${e.model}$`] = `%${queryParam[e.model]}%`;
    }
  });
  // 查询条件组装
  if (queryParam.Name_data) {
    search[`user_id$`] = `%${queryParam.Name_data}%`;
    // search[`name$`] = `%${queryParam.Building_data}%`;
    // search[`setting_name$`] = `%${queryParam.Building_data}%`;
  }
  if (!isDefault) {
    if (queryParam.Building_data) {
      search[`user_building_id$`] = `%${queryParam.Building_data}%`;
      // search[`name$`] = `%${queryParam.Building_data}%`;
      // search[`setting_name$`] = `%${queryParam.Building_data}%`;
    }
    if (queryParam.Floor_data) {
      search[`user_floor_id$`] = `%${queryParam.Floor_data}%`;
      // search[`name$`] = `%${queryParam.Floor_data}%`;
      // search[`setting_name$`] = `%${queryParam.Floor_data}%`;
    }
    if (queryParam.Room_data) {
      search[`user_room_id$`] = `%${queryParam.Room_data}%`;
      // search[`name$`] = `%${queryParam.Building_data}%`;
      // search[`setting_name$`] = `%${queryParam.Building_data}%`;
    }
  }

  // apiJson请求信息
  const requestJson = {
    '[]': {
      join: joinValue,
      [table]: {
        ...search,
        '@order': `${order || 'create_time-,uuid-'}`,
      },

      page: paginationConfig.value.current - 1,
      count: paginationConfig.value.pageSize,
      query: 2,
    },
    'total@': '/[]/total',
    'info@': '/[]/info',
  };

  if (hasScene) {
    let sceneJson: any = {
      Scene_record: {
        'uuid@': `/${table}/scene_id`,
        '@column': 'uuid;name;scene_code',
      },
      Building_data: {
        'user_id@': `/${table}/user_building_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
      Floor_data: {
        'user_id@': `/${table}/user_floor_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
      Room_data: {
        'user_id@': `/${table}/user_room_id`,
        'parent_scene_record_uuid@': `/${table}/scene_id`,
        '@column': 'uuid;user_id;name;setting_name',
      },
    };
    if (chooseTwinData.code !== 'BUILDING' && chooseTwinData.code !== 'PARK') {
      sceneJson = {
        FLOOR: {
          '@order': 'create_time-,uuid-',
        },
        BUILDING: {
          '@order': 'create_time-,uuid-',
        },
        Scene_record: {
          'uuid@': `/${table}/scene_id`,
          '@column': 'uuid;name;scene_code',
        },
        Building_data: {
          'user_id@': `/${table}/user_building_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
        Floor_data: {
          'user_id@': `/${table}/user_floor_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
        Room_data: {
          'user_id@': `/${table}/user_room_id`,
          'parent_scene_record_uuid@': `/${table}/scene_id`,
          '@column': 'uuid;user_id;name;setting_name',
        },
      };
    }
    requestJson['[]'] = Object.assign(requestJson['[]'], sceneJson);
  }
  if (isDefault) {
    let defaultJson: any = {};
    if (chooseTwinData.dataType !== 'FORM') {
      defaultJson = {
        Scene_record: {
          'scene_code@': `/${table}/scene_code`,
          enable: '1',
          'status!': '2',
          '@column': 'uuid;name;scene_code',
        },
      };
    }
    // 房间孪生体
    if (chooseTwinData.code === 'ROOM') {
      const roomJson = {
        Building_data: {
          'user_id@': '/FLOOR/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Floor_data: {
          'user_id@': '/ROOM/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Room_data: {
          'user_id@': '/ROOM/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
      };
      defaultJson = Object.assign(defaultJson, roomJson);
    }
    // 楼层孪生体
    if (chooseTwinData.code === 'FLOOR') {
      const floorJson = {
        Building_data: {
          'user_id@': '/FLOOR/parent_cbid',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
        Floor_data: {
          'user_id@': '/FLOOR/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
      };
      defaultJson = Object.assign(defaultJson, floorJson);
    }
    // 建筑孪生体
    if (chooseTwinData.code === 'BUILDING') {
      const roomJson = {
        Building_data: {
          'user_id@': '/BUILDING/user_id',
          'parent_scene_record_uuid@': '/Scene_record/uuid',
          '@column': 'uuid;user_id;name;setting_name;config_camInfo;parent_cbid',
        },
      };
      defaultJson = Object.assign(defaultJson, roomJson);
    }
    // 菜单孪生体
    if (chooseTwinData.code === 'MENU') {
      const menuJson = {
        Sys_menu: {
          'id@': '/MENU/menu_id',
          '@column': 'id;name',
        },
      };
      defaultJson = Object.assign(defaultJson, menuJson);
    }
    // 地图层级和地图矢量数据
    if (chooseTwinData.code === 'MAP_LEVEL' || chooseTwinData.code === 'MAP_VECTOR_DATA') {
      const mapLevelJson = {
        Map_level_data: {
          'id@': `/${chooseTwinData.code}/map_level_id`,
          '@column': 'id;name',
        },
      };
      defaultJson = Object.assign(defaultJson, mapLevelJson);
    }
    requestJson['[]'] = Object.assign(requestJson['[]'], defaultJson);
  }
  //如果是默认孪生体 得建筑，楼层，房间 高级查询
  if (isDefault) {
    let buserids = [];
    let fuserids = [];
    if (queryParam.Building_data) {
      //查询过滤出来的建筑
      const buildJson = {
        '[]': {
          BUILDING: {
            user_id$: `%${queryParam.Building_data}%`,
            '@order': 'create_time-,uuid-',
          },
        },
        'total@': '/[]/total',
        'info@': '/[]/info',
      };
      const firstStep = await getTwinData(buildJson);
      buserids = firstStep['[]']?.map((bitm: any) => bitm.BUILDING).map((build: any) => build.user_id);
      if (!buserids) {
        tabData.value = [];
        loading.value = false;
        return;
      }
      //@ts-ignore
      requestJson['[]']['FLOOR']['parent_cbid{}'] = buserids;
    }
    if (queryParam.Floor_data) {
      //查询过滤出来的建筑
      const floorJson = {
        '[]': {
          FLOOR: {
            user_id$: `%${queryParam.Floor_data}%`,
            '@order': 'create_time-,uuid-',
          },
        },
        'total@': '/[]/total',
        'info@': '/[]/info',
      };
      if (queryParam.Building_data) {
        //@ts-ignore
        floorJson['[]']['FLOOR']['parent_cbid{}'] = buserids ? buserids : [];
      }
      const sedStep = await getTwinData(floorJson);
      fuserids = sedStep['[]']?.map((bitm: any) => bitm.FLOOR).map((floor: any) => floor.user_id);
      if (!fuserids) {
        tabData.value = [];
        loading.value = false;
        return;
      }
      //@ts-ignore
      requestJson['[]']['ROOM']['parent_cbid{}'] = fuserids;
    }
  }
  console.log('requestJson', requestJson);
  getTwinData(requestJson).then(async (res: any) => {
    if (res.code === 200) {
      if (res['[]']) {
        paginationConfig.value.total = res.info?.total || 0;
        let resData = res['[]'];
        resData = await Promise.all(
          resData.map(async (ele: any) => {
            // 场景数据名称
            let sceneDataName = ele[`${table}`].user_id;
            // 建筑名称
            let buildingName = ele[`${table}`].user_building_id;
            // 原始名称
            let originName = '';
            let originId = '';
            if (ele.Building_data) {
              const name = ele.Building_data.setting_name ? ele.Building_data.setting_name : ele.Building_data.name;
              buildingName = isDefault ? `${ele.Building_data.user_id}` : `${name}[${ele.Building_data.user_id}]`;
              // sceneDataName = buildingName;
              // 建筑名称
              originName = name;
              originId = ele.Building_data.uuid;
            } else if (ele.BUILDING) {
              buildingName = `[${ele.BUILDING.user_id}]`;
              // sceneDataName = buildingName;
              // 建筑名称
              originName = '';
              originId = ele.BUILDING.uuid;
            }
            // 楼层名称
            let floorName = ele[`${table}`].user_floor_id;
            if (ele.Floor_data) {
              const name = ele.Floor_data.setting_name ? ele.Floor_data.setting_name : ele.Floor_data.name;
              floorName = isDefault ? ele.Floor_data.user_id : `${name}[${ele.Floor_data.user_id}]`;
              // sceneDataName = floorName;
              // 建筑名称
              originName = name;
              originId = ele.Floor_data.uuid;
            } else if (ele.FLOOR) {
              floorName = `[${ele.FLOOR.user_id}]`;
              // sceneDataName = floorName;
              // 楼层名称
              originName = '';
              originId = ele.FLOOR.uuid;
            }
            // 地图层级名称
            let mapLevelName;
            if (ele.Map_level_data) {
              mapLevelName = ele.Map_level_data.name;
            }
            // 菜单名称
            let menuName;
            if (ele.Sys_menu) {
              menuName = ele.Sys_menu.name;
            }
            // 房间名称
            let roomName = ele[`${table}`].user_room_id;
            if (ele.Room_data) {
              const name = ele.Room_data.setting_name ? ele.Room_data.setting_name : ele.Room_data.name;
              roomName = `${name}[${ele.Room_data.user_id}]`;
              // sceneDataName = roomName;
              // 建筑名称
              originName = name;
              originId = ele.Room_data.uuid;
            }
            const obj = {
              ...ele[table],
              sceneName: ele.Scene_record ? ele.Scene_record.name : ele[`${table}`].scene_id,
              sceneDataName,
              buildingName,
              floorName,
              roomName,
              mapLevelName,
              menuName,
              originName,
              originId,
            };
            sourceType.value.some((item: any) => {
              if (item.code === obj.data_source) {
                obj.data_source_source = obj.data_source;
                obj.data_source = item.value;
                return true;
              }
              return false;
            });
            obj.iconStatus = obj.gcj02_position || obj.wgs84_position;
            obj.create_time = obj.create_time.replace('T', ' ');
            obj.update_time = obj.update_time.replace('T', ' ');
            // 通过缓存获取用户信息
            obj.create_user = await getUserInfo(obj.create_user);
            obj.update_user = await getUserInfo(obj.update_user);
            // @ts-ignore
            obj.current_level = levelObj[obj.current_level];
            return obj;
          })
        );
        console.log('resData', resData);
        tabData.value = resData || [];
      } else {
        tabData.value = [];
      }
    } else {
      tabData.value = [];
      useGlobalMessage('warning', res.msg);
    }
    loading.value = false;
  });
};

// 分页变化
const paginationChange = (current: number, pageSize: number) => {
  paginationConfig.value = Object.assign(paginationConfig.value, {
    current,
    pageSize,
  });
  getData();
};

// 选中的列表记录
const tableSelects = ref<{
  keys: Array<any>;
  list: Array<any>;
}>({
  keys: [],
  list: [],
});
const rowSelection = {
  onChange: (selectedRowKeys: Array<any>, selectedRows: Array<any>) => {
    tableSelects.value.keys = selectedRowKeys;
    tableSelects.value.list = selectedRows;
  },
  getCheckboxProps: (record: any) => ({
    disabled: record.groupId === DEFAULT_TWIN_CLASS_GROUP_ID,
  }),
};

const sortParam = ref<any>({});
const tableChange = (pag: any, filters: any, sorter: any) => {
  let param = {};
  if (sorter.order) {
    param = {
      sortField: sorter.field,
      sortRule: sorter.order === 'descend' ? 'DESC' : 'ASC',
    };
  }
  sortParam.value = param;
  getData(param);
};
const conditionBoxRef = ref();
const reset = () => {
  queryParam.searchValue = '';
  paginationConfig.value.current = 1;
  paginationConfig.value.pageSize = 10;
  conditionBoxRef.value.clear();
};

const downloadFileSlotNames = ref<Array<string>>([]); // 文件
const releativeSlotNames = ref<Array<string>>([]); // 关联孪生体
const editorSlotName = ref<Array<string>>([]); // 富文本
const selectOrCheckbox = ref<any>([]); // 多选框
// 获取表头
const getColumns = () => {
  let fixColunm: any = [
    { code: 'uuid', value: '唯一标识' },
    { code: 'data_source', value: '点位数据来源' },
    { code: 'scene_id', value: '所属场景' },
    { code: 'current_level', value: '所在层级' },
    { code: 'user_building_id', value: '所属建筑' },
    { code: 'user_floor_id', value: '所属楼层' },
    { code: 'user_room_id', value: '所属房间' },
    { code: 'wgs84_position', value: 'WGS84坐标系' },
    { code: 'gcj02_position', value: 'GCJ02火星坐标系' },
    { code: 'gis_height', value: '离地高度' },
    { code: 'parent_user_id', value: '所在层级ID' },
    { code: 'position_body', value: '点位数据信息' },
    { code: 'position', value: '位置坐标' },
    { code: 'create_time', value: '创建时间' },
    { code: 'create_user', value: '创建人' },
    { code: 'update_time', value: '更新时间' },
    { code: 'update_user', value: '更新人' },
  ].map((item) => ({ dataIndex: item.code, title: item.value }));
  // const tableInfo = JSON.parse(chooseTwinData.structure);
  const tableInfo = JSON.parse(chooseTwinData.readStructure ? chooseTwinData.readStructure : '{}');
  const formList = chooseTwinData.form ? JSON.parse(chooseTwinData.form) : { list: [] };
  filelds = formList.list;
  getFilterBar(formList.list);
  // 要排除的固定列
  let excludeColumnNames = ['wgs84_position', 'gcj02_position', 'position_body', 'parent_user_id', 'position'];
  // 地图层级的孪生体排除的固定列
  if (chooseTwinData.level === 'MAP') {
    const mapExcludeColumnNames = ['scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id'];
    excludeColumnNames = [...excludeColumnNames, ...mapExcludeColumnNames];
  }
  // 默认孪生体排除的固定列
  if (chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID) {
    const defaultExcludeColumnNames = ['data_source', 'scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id', 'gis_height'];
    excludeColumnNames = [...excludeColumnNames, ...defaultExcludeColumnNames];
  }
  // 表单孪生体排除的固定列
  if (chooseTwinData.dataType === 'FORM') {
    const formExcludeColumnNames = ['data_source', 'scene_id', 'current_level', 'user_building_id', 'user_floor_id', 'user_room_id', 'gis_height'];
    excludeColumnNames = [...excludeColumnNames, ...formExcludeColumnNames];
  }
  // 获取自定义的列
  const fixedColumnNames = fixColunm.map((e: any) => e.dataIndex);
  const tabColum = tableInfo?.column || tableInfo?.table?.column || [];
  const customizeColumns = tabColum.filter((e: any) => !fixedColumnNames.includes(e.name)).map((e: any) => ({ dataIndex: e.name, title: e.comment })) || [];

  const index = fixColunm.findIndex((e: any) => e.dataIndex === 'create_time');
  if (index !== -1) {
    fixColunm.splice(index, 0, ...customizeColumns);
  }
  // 排除固定列
  fixColunm = fixColunm.filter((e: any) => !excludeColumnNames.includes(e.dataIndex));
  // 给房间默认孪生体添加字段
  if (chooseTwinData.code === 'ROOM') {
    fixColunm.splice(2, 0, { dataIndex: 'user_building_id', title: '所属建筑' }, { dataIndex: 'user_floor_id', title: '所属楼层' });
  }
  // 给楼层默认孪生体添加字段
  if (chooseTwinData.code === 'FLOOR') {
    fixColunm.splice(2, 0, {
      dataIndex: 'user_building_id',
      title: '所属建筑',
    });
  }
  // 给地图层级孪生体和地图矢量孪生体添加字段
  if (chooseTwinData.code === 'MAP_LEVEL' || chooseTwinData.code === 'MAP_VECTOR_DATA') {
    fixColunm.splice(1, 0, {
      dataIndex: 'cb_mapLevel_name',
      title: '地图层级名称',
    });
  }
  // 给xxv菜单孪生体添加字段
  if (chooseTwinData.code === 'MENU') {
    fixColunm.splice(1, 0, { dataIndex: 'cb_menu_name', title: '菜单名称' });
  }
  // 是否是默认孪生体
  const isDefault = chooseTwinData.groupId === DEFAULT_TWIN_CLASS_GROUP_ID;
  fixColunm.forEach((ele: any) => {
    // 替换的展示列映射
    const replaceColumnMap: any = {
      scene_id: 'sceneName',
      user_building_id: 'buildingName',
      user_floor_id: 'floorName',
      user_room_id: 'roomName',
      scene_code: 'sceneName',
      user_id: 'sceneDataName',
      cb_mapLevel_name: 'mapLevelName',
      cb_menu_name: 'menuName',
    };

    if (ele.dataIndex === 'scene_code') {
      ele.title = '所属场景';
    }
    if (ele.dataIndex === 'user_id') {
      ele.title = '名称';
    }

    ele.dataIndex = replaceColumnMap[ele.dataIndex] ? replaceColumnMap[ele.dataIndex] : ele.dataIndex;
    const sortNo = isDefault && (ele.dataIndex === 'buildingName' || ele.dataIndex === 'floorName' || ele.dataIndex === 'roomName');
    ele.sorter = sortNo ? false : true;
    ele.sortDirections = ['descend', 'ascend'];
    ele.width = 180;
    ele.ellipsis = true;

    formList.list.forEach((item: any) => {
      if (item.model === ele.dataIndex) {
        if (item.type === 'uploadFile') {
          const downloadFileSlotName = `fileDownload-${item.model}`;
          ele.key = `fileDownload-${item.model}`;
          downloadFileSlotNames.value.push(downloadFileSlotName);
        }
        if (item.type === 'releative') {
          const releativeFileSlotName = `releative-${item.model}`;
          ele.key = `releative-${item.model}`;
          releativeSlotNames.value.push(releativeFileSlotName);
        }
        if (item.type === 'editor') {
          const editorFileSlotName = `editor-${item.model}`;
          ele.key = editorFileSlotName;
          editorSlotName.value.push(editorFileSlotName);
        }
        if (item.type === 'select' || item.type === 'checkbox') {
          const selectOrCheckboxSlotName = `selectOrCheckbox-${item.model}`;
          ele.key = selectOrCheckboxSlotName;
          selectOrCheckbox.value.push(selectOrCheckboxSlotName);
        }
        delete ele.sorter;
        delete ele.sortDirections;
      }
    });
  });
  let width = 160;

  if (isDefault) {
    width = 60;
  } else if (isMap.value) {
    width = 160;
  } else {
    width = 110;
  }
  fixColunm.push({
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width,
    fixed: 'right',
  });

  // 如果是地图摆点,需要添加是否标记标识
  if (isMap.value && !props.checkMapData) {
    fixColunm.splice(1, 0, {
      title: '摆点',
      dataIndex: 'iconStatus',
      key: 'iconStatus',
      width: 160,
    });
  }
  columns.value = fixColunm;
  // 非表单增加离地高度字段
  const formObj = JSON.parse(chooseTwinData.form);
  if (chooseTwinData.level !== 'OTHER' && chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID) {
    formObj.list.splice(1, 0, {
      type: 'number',
      label: '离地高度(m)',
      options: {
        width: '100%',
        defaultValue: 0,
        min: -10000,
        max: 10000,
        precision: 2,
        step: 0.05,
        hidden: false,
        disabled: false,
        placeholder: '请输入',
        unique: false,
      },
      model: 'gis_height',
      key: 'gis_height',
      help: '',
      rules: [{ required: false, message: '必填项' }],
    });
  }
  console.log('formObj', formObj);
  formJson.value = JSON.stringify(formObj);
};

// 获取需要筛选的组件
const getFilterBar = (list: any) => {
  const select: any = [];
  const search: any = [];
  list.forEach((v: any) => {
    if ((v.type === 'radio' || v.type === 'checkbox' || v.type === 'select') && v.options.isFilter === '1') {
      select.push(v);
    }
    if ((v.type === 'input' || v.type === 'date' || v.type === 'number' || v.type === 'textarea') && v.options.isFilter === '1') {
      search.push(v);
    }
  });
  selects.value = select;
  searchs.value = search;
};
const addGisHeight = (formObj: any) => {
  if (chooseTwinData.level !== 'OTHER' && chooseTwinData.groupId !== DEFAULT_TWIN_CLASS_GROUP_ID) {
    formObj.list.splice(1, 0, {
      type: 'number',
      label: '离地高度(m)',
      options: {
        width: '100%',
        defaultValue: 0,
        min: -10000,
        max: 10000,
        precision: 2,
        step: 0.05,
        hidden: false,
        disabled: false,
        placeholder: '请输入',
        unique: false,
      },
      model: 'gis_height',
      key: 'gis_height',
      help: '',
      rules: [{ required: false, message: '必填项' }],
    });
  }
  return JSON.stringify(formObj);
};
// 新增孪生体数据
const addEditFormRef = ref();
const handelAdd = () => {
  if (!chooseTwinData.addForm) {
    useGlobalMessage('warning', '没有权限新增数据，请联系管理员');
    return;
  }
  const addForm = JSON.parse(chooseTwinData.addForm);
  const addFormStr = addGisHeight(addForm);
  addEditFormRef.value.init({}, addFormStr, {
    table: chooseTwinData.code,
    dataType: chooseTwinData.dataType,
  });
};
const handleEdit = (data: any) => {
  // addEditFormRef.value.init(data, formJson.value, {
  if (!chooseTwinData.editForm) {
    useGlobalMessage('warning', '没有权限操作该数据，请联系管理员');
    return;
  }
  const editForm = JSON.parse(chooseTwinData.editForm);
  const editFormStr = addGisHeight(editForm);

  addEditFormRef.value.init(data, editFormStr, {
    table: chooseTwinData.code,
    dataType: chooseTwinData.dataType,
  });
};
// 删除
const twinDataDelete = (uuids: Array<string>) => {
  const table = chooseTwinData.code;
  deleteTwinData({
    [table]: {
      'uuid{}': uuids,
    },
  }).then((res: any) => {
    if (res.code === 200) {
      useGlobalMessage('success', '孪生体数据删除成功');
      getData();
      emits('refreshMap');
      const obj = window.app?.query(`#${uuids[0]}`)[0];
      if (obj) {
        const placeObj = window.mapPlacementIns.twinObject?.id === obj.id ? true : false;
        if (placeObj) {
          window.mapPlacementIns.reset();
        }
        obj.destroy();
      }
    } else {
      useGlobalMessage('error', `删除失败：${res.msg}`);
    }
  });
};

const handleDelete = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗 ?',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      const table = chooseTwinData.code;
      deleteTwinData({
        [table]: {
          'uuid{}': tableSelects.value.keys,
        },
      }).then((res: any) => {
        if (res.code === 200) {
          useGlobalMessage('success', '孪生体数据删除成功');
          getData();
          emits('refreshMap');
        } else {
          useGlobalMessage('error', `删除失败：${res.msg}`);
        }
      });
      tableSelects.value.keys = [];
    },
  });
};

// 导出
const downLoading = ref(false);
const batchExport = () => {
  downLoading.value = true;
  const matchJson: any = {};
  if (queryParam.data_source) {
    matchJson.data_source = queryParam.data_source;
  }
  searchs.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      matchJson[`${e.model}`] = `${queryParam[e.model]}`;
    }
  });
  selects.value.forEach((e: any) => {
    if (queryParam[e.model]) {
      matchJson[`${e.model}`] = `${queryParam[e.model]}`;
    }
  });
  console.log('batchExport', queryParam);
  exportTwinData({
    id: chooseTwinData.id,
    dataIds: tableSelects.value.keys.join(','),
    idFlag: true,
    keyword: queryParam.searchValue,
    // sceneId: [queryParam.scene_id], // 当前版本
    sceneId: queryParam.scene_ids, // 主场景+子场景
    matchJson,
    roomName: queryParam.Room_data,
    floorName: queryParam.Floor_data,
    buildingName: queryParam.Building_data,
  })
    .then((res: any) => {
      if (paginationConfig.value.total > 10000 && tableSelects.value.keys.length === 0) {
        useGlobalMessage('warning', '需导出的数据量较大 系统正努力下载中');
      } else {
        downLoading.value = false;
        downloadfile(res);
      }
    })
    .finally(() => {
      downLoading.value = false;
    });
};

// 导入
const uploadRef = ref();
const handleImport = () => {
  uploadRef.value.init(chooseTwinData);
};

// 富文本预览
const editorDialog = reactive({
  title: '',
  show: false,
  content: '',
});
const showEditor = (text: string) => {
  editorDialog.title = '预览';
  editorDialog.show = true;
  editorDialog.content = text;
};

// 关联孪生体
const reletiveRef = ref();
const showReletive = (record: any, column: any) => {
  try {
    const data = JSON.parse(record[column.dataIndex]);
    if (data.length) {
      reletiveRef.value.init(JSON.parse(record[column.dataIndex]), column.dataIndex, chooseTwinData);
    }
  } catch (err) {
    console.error(err);
  }
};
defineExpose({ init, getData });
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--primary-bg-color);

  .search-wrap {
    justify-content: end;

    .table-handle {
      margin-bottom: 10px;
    }
  }

  .table-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;

    .table-handle {
      padding: 16px 0;

      .handle-btn {
        margin-right: 10px;
        border-radius: 4px;
      }
    }

    .table-content {
      flex: 1;
      overflow-y: auto;

      .table {
        height: calc(100% - 40px);
      }

      .pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        text-align: right;
      }
    }
  }
}
</style>
