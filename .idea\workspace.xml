<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7b4b309d-6f94-410d-910a-6ed8e1f0f634" name="Changes" comment="feat(sql): 优化insert跨库获取">
      <change beforePath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/java/com/uino/campus/sys/CbOfflineApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/java/com/uino/campus/sys/CbOfflineApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/resources/application-offline.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/resources/application-offline.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/cb/cb_offline/cb-offline-sys/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/interactive-feedback-mcp/.python-version" beforeDir="false" afterPath="$PROJECT_DIR$/interactive-feedback-mcp/.python-version" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-app/scene-x/src/main/java/com/uino/x/pedestal/app/scenex/config/InitConfigRunner.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-app/scene-x/src/main/java/com/uino/x/pedestal/app/scenex/config/InitConfigRunner.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-common/src/main/java/com/uino/x/pedestal/twin/common/utils/JrmJsonUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-common/src/main/java/com/uino/x/pedestal/twin/common/utils/JrmJsonUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/AbstractSqlBuilderProcessor.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/AbstractSqlBuilderProcessor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/definition/AbstractSqlDefinition.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/definition/AbstractSqlDefinition.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/executor/AbstractSqlExecutor.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/core/sql/executor/AbstractSqlExecutor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/AlterSqlDefinition.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/AlterSqlDefinition.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/CreateSqlDefinition.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/CreateSqlDefinition.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/DropSqlDefinition.java" beforeDir="false" afterPath="$PROJECT_DIR$/pedestal-x/pedestal-x-module/twin/twin-jrm/src/main/java/com/uino/x/pedestal/twin/jrm/sql/definition/DropSqlDefinition.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project-base-web/typings/components.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/project-base-web/typings/components.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qinghai-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/qinghai-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qinghai-server/src/main/resources/model.zip" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/u-work-base/src/config/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/u-work-base/src/config/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vue3-form-ant3/.husky/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/vue3-form-ant3/.husky/.gitignore" afterDir="false" />
    </list>
    <list id="bdb920d6-301c-4a85-824d-373d7815a9f3" name="feat(config): 修改group by&amp;新增达梦flyway" comment="feat(config): 修改group by&amp;新增达梦flyway" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/component/common-x" value="prod" />
        <entry key="$PROJECT_DIR$/component/dependency-x" value="prod" />
        <entry key="$PROJECT_DIR$/database-migration-tool" value="master" />
        <entry key="$PROJECT_DIR$/edtap-server" value="assemble_feat" />
        <entry key="$PROJECT_DIR$/framework/APIJSON" value="master" />
        <entry key="$PROJECT_DIR$/gateway-x" value="feat-dameng" />
        <entry key="$PROJECT_DIR$/plumelog" value="master" />
        <entry key="$PROJECT_DIR$/project-base-web" value="dev-syq" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/pedestal-x" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/baomidou/mybatis-plus-core/3.5.12/mybatis-plus-core-3.5.12-sources.jar!/com/baomidou/mybatisplus/core/toolkit/StringPool.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1-sources.jar!/net/sf/jsqlparser/parser/CCJSqlParserUtil.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1-sources.jar!/net/sf/jsqlparser/statement/StatementVisitor.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1-sources.jar!/net/sf/jsqlparser/statement/drop/Drop.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1-sources.jar!/net/sf/jsqlparser/statement/select/LateralSubSelect.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/github/jsqlparser/jsqlparser/5.1/jsqlparser-5.1-sources.jar!/net/sf/jsqlparser/statement/upsert/Upsert.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/org/mybatis/mybatis/3.5.19/mybatis-3.5.19-sources.jar!/org/apache/ibatis/type/ClobTypeHandler.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/org/mybatis/mybatis/3.5.19/mybatis-3.5.19-sources.jar!/org/apache/ibatis/type/NClobTypeHandler.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/org/springframework/cloud/spring-cloud-openfeign-core/4.3.0/spring-cloud-openfeign-core-4.3.0-sources.jar!/org/springframework/cloud/openfeign/encoding/FeignAcceptGzipEncodingAutoConfiguration.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/org/springframework/cloud/spring-cloud-openfeign-core/4.3.0/spring-cloud-openfeign-core-4.3.0-sources.jar!/org/springframework/cloud/openfeign/encoding/FeignClientEncodingProperties.java" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/effectpackage/effectpackage-pojo/src/main/resources/db/dm/migration/effectpackage/1.0.0/V2025.06.03.16.33__新增效果包是否默认字段.sql" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/effectpackage/effectpackage-pojo/src/main/resources/db/mysql/migration/effectpackage/1.0.0/V2025.06.03.16.33__新增效果包是否默认字段.sql" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings_un.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="uino-dev,uino-i18n,aliyun" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zD99MP9ZdOFW2SznPdyw3Gbgag" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="abbreviatePackageNames" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.DatabaseMigrationToolApplication.executor": "Run",
    "Application.Main.executor": "Debug",
    "Application.PlumelogServerApplication.executor": "Debug",
    "Application.SysUserController.executor": "Run",
    "JUnit.DmSqlParserTest.parseGroupConcat.executor": "Run",
    "JUnit.DmSqlParserTest.parseReplaceInto.executor": "Run",
    "JUnit.DmSqlParserTest.parseSelectTableType.executor": "Run",
    "JUnit.DmSqlParserTest.parseShowCreate.executor": "Run",
    "JUnit.SqlTransformationEngineTest (1).executor": "Run",
    "JUnit.SqlTransformationEngineTest.executor": "Run",
    "JUnit.SqlTransformationEngineTest.parseGroupConcat.executor": "Run",
    "JUnit.SqlTransformationEngineTest.parseSelectTableType.executor": "Run",
    "JUnit.SqlTransformationEngineTest.parseShowCreate.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testBacktickQuoteTransformation.executor": "Debug",
    "JUnit.SqlTransformationEngineTest.testBatchReplaceIntoTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testCommentAndEmptyBoundary.executor": "Debug",
    "JUnit.SqlTransformationEngineTest.testCommentedSqlTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testCreateDatabaseTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testDrop.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testDropDatabaseTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testGroupConcatTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testJsonOperatorTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testMultipleQuotesTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testNestedQueryTransformation.executor": "Run",
    "JUnit.SqlTransformationEngineTest.testOnDuplicateKeyUpdateTransformation.executor": "Debug",
    "JUnit.SqlTransformationEngineTest.testReplaceIntoTransformation.executor": "Debug",
    "JUnit.SqlUtilsTest.dmToDm.executor": "Run",
    "JUnit.SqlUtilsTest.dmToDmTable.executor": "Run",
    "JUnit.SqlUtilsTest.dmToDmView.executor": "Run",
    "JUnit.SqlUtilsTest.dmToMysql.executor": "Run",
    "JUnit.SqlUtilsTest.dmToMysqlSchema.executor": "Run",
    "JUnit.SqlUtilsTest.dmToMysqlTable.executor": "Debug",
    "JUnit.SqlUtilsTest.dmToMysqlView.executor": "Debug",
    "JUnit.SqlUtilsTest.executor": "Run",
    "JUnit.SqlUtilsTest.export.executor": "Debug",
    "JUnit.SqlUtilsTest.exportSchema.executor": "Run",
    "JUnit.SqlUtilsTest.importSchema.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToDm.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToDmSchema.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToDmTable.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToDmView.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToMysql.executor": "Run",
    "JUnit.SqlUtilsTest.mysqlToMysqlTable.executor": "Run",
    "JUnit.SqlUtilsTest.testConvertSqlSchema.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToDmSchema.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToDmTable.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToDmView.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToMysqlSchema.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToMysqlTable.executor": "Run",
    "JUnit.SqlUtilsTest.testDmToMysqlView.executor": "Run",
    "JUnit.SqlUtilsTest.testEncodeAndDecode.executor": "Run",
    "JUnit.SqlUtilsTest.testExecuteSql.executor": "Run",
    "JUnit.SqlUtilsTest.testExportSchemaParameterValidation.executor": "Run",
    "JUnit.SqlUtilsTest.testExportSchemaWithModuleTable.executor": "Run",
    "JUnit.SqlUtilsTest.testExportWithDifferentInsertCounts.executor": "Run",
    "JUnit.SqlUtilsTest.testFileExtensions.executor": "Run",
    "JUnit.SqlUtilsTest.testMysqlToDmSchema.executor": "Run",
    "JUnit.SqlUtilsTest.testMysqlToDmTable.executor": "Run",
    "JUnit.SqlUtilsTest.testMysqlToDmView.executor": "Run",
    "JUnit.SqlUtilsTest.testMysqlToMysqlSchema.executor": "Debug",
    "JUnit.SqlUtilsTest.testMysqlToMysqlTable.executor": "Run",
    "JUnit.SqlUtilsTest.testMysqlToMysqlView.executor": "Run",
    "JUnit.SqlUtilsTest.testPrimaryKeyIndexDuplicationFix.executor": "Run",
    "Maven.alarm [install].executor": "Run",
    "Maven.annotate-impl [clean].executor": "Run",
    "Maven.annotate-impl [deploy].executor": "Run",
    "Maven.common-x [clean].executor": "Run",
    "Maven.common-x [compile].executor": "Run",
    "Maven.common-x [deploy].executor": "Run",
    "Maven.common-x [install].executor": "Run",
    "Maven.common-x [validate].executor": "Run",
    "Maven.common-x [versions:set...].executor": "Run",
    "Maven.common-x-boot2 [clean].executor": "Run",
    "Maven.common-x-boot2 [compile].executor": "Run",
    "Maven.common-x-boot2 [install].executor": "Run",
    "Maven.common-x-boot2 [package].executor": "Run",
    "Maven.common-x-boot2-sql [compile].executor": "Run",
    "Maven.common-x-boot2-sql [deploy].executor": "Run",
    "Maven.common-x-boot2-sql [install].executor": "Run",
    "Maven.common-x-config [clean].executor": "Run",
    "Maven.common-x-config [compile].executor": "Run",
    "Maven.common-x-config [deploy].executor": "Run",
    "Maven.common-x-config [install].executor": "Run",
    "Maven.common-x-core [clean].executor": "Run",
    "Maven.common-x-core [deploy].executor": "Run",
    "Maven.common-x-core [install].executor": "Run",
    "Maven.common-x-dameng [deploy].executor": "Run",
    "Maven.common-x-dameng [install].executor": "Run",
    "Maven.common-x-datasource [clean].executor": "Run",
    "Maven.common-x-datasource [compile].executor": "Run",
    "Maven.common-x-datasource [deploy].executor": "Run",
    "Maven.common-x-datasource [install].executor": "Run",
    "Maven.common-x-decryptutil [install].executor": "Run",
    "Maven.common-x-label [clean].executor": "Run",
    "Maven.common-x-label [install].executor": "Run",
    "Maven.common-x-mybatis [deploy].executor": "Run",
    "Maven.common-x-redis [deploy].executor": "Run",
    "Maven.common-x-redis [install].executor": "Run",
    "Maven.common-x-security [install].executor": "Run",
    "Maven.common-x-sql [clean].executor": "Run",
    "Maven.common-x-sql [deploy].executor": "Run",
    "Maven.common-x-sql [install].executor": "Run",
    "Maven.common-x-sql [package].executor": "Run",
    "Maven.common-x-sql-boot2 [clean].executor": "Run",
    "Maven.common-x-sql-boot2 [install].executor": "Run",
    "Maven.common-x-sql-boot2 [package].executor": "Run",
    "Maven.common-x-tool [install].executor": "Run",
    "Maven.common-x-tool-base [install].executor": "Run",
    "Maven.common-x-tool-lang [install].executor": "Run",
    "Maven.common-x-tool-spring [clean].executor": "Run",
    "Maven.commonx-x-sql-boot2 [clean].executor": "Run",
    "Maven.commonx-x-sql-boot2 [install].executor": "Run",
    "Maven.database-migration-tool [clean].executor": "Run",
    "Maven.database-migration-tool [compile].executor": "Run",
    "Maven.database-migration-tool [package].executor": "Run",
    "Maven.dependency-x [clean].executor": "Run",
    "Maven.dependency-x [deploy].executor": "Run",
    "Maven.dependency-x [install].executor": "Run",
    "Maven.dependency-x [versions:set...].executor": "Run",
    "Maven.dependency-x-pojo [deploy].executor": "Run",
    "Maven.dependency-x-pojo [install].executor": "Run",
    "Maven.dict [install].executor": "Run",
    "Maven.dict-dao [deploy].executor": "Run",
    "Maven.edtap-app [clean].executor": "Run",
    "Maven.edtap-app [package].executor": "Run",
    "Maven.edtap-app [validate].executor": "Run",
    "Maven.edtap-common [clean].executor": "Run",
    "Maven.edtap-common [install].executor": "Run",
    "Maven.edtap-impl [clean].executor": "Run",
    "Maven.edtap-impl [compile].executor": "Run",
    "Maven.edtap-server [clean].executor": "Run",
    "Maven.edtap-server [compile].executor": "Run",
    "Maven.edtap-server [deploy].executor": "Run",
    "Maven.edtap-server [install].executor": "Run",
    "Maven.edtap-server [package].executor": "Run",
    "Maven.edtap-server [versions:set...].executor": "Run",
    "Maven.effectpackage-pojo [clean].executor": "Run",
    "Maven.effectpackage-pojo [deploy].executor": "Run",
    "Maven.file-pojo [install].executor": "Run",
    "Maven.gateway-x [clean].executor": "Run",
    "Maven.gateway-x [deploy].executor": "Run",
    "Maven.gateway-x [install].executor": "Run",
    "Maven.gateway-x [package].executor": "Run",
    "Maven.gateway-x [versions:set...].executor": "Run",
    "Maven.identity [clean].executor": "Run",
    "Maven.identity [compile].executor": "Run",
    "Maven.identity [install].executor": "Run",
    "Maven.identity-impl [clean].executor": "Run",
    "Maven.identity-impl [deploy].executor": "Run",
    "Maven.identity-impl [install].executor": "Run",
    "Maven.identity-pojo [clean].executor": "Run",
    "Maven.identity-pojo [deploy].executor": "Run",
    "Maven.legend-impl [deploy].executor": "Run",
    "Maven.model-impl [deploy].executor": "Run",
    "Maven.model-pojo [deploy].executor": "Run",
    "Maven.pedestal-x [clean].executor": "Run",
    "Maven.pedestal-x [compile].executor": "Run",
    "Maven.pedestal-x [deploy].executor": "Run",
    "Maven.pedestal-x [install].executor": "Run",
    "Maven.pedestal-x [package].executor": "Run",
    "Maven.pedestal-x [site].executor": "Run",
    "Maven.pedestal-x [versions:set...].executor": "Run",
    "Maven.pedestal-x-app [clean].executor": "Run",
    "Maven.pedestal-x-app [install].executor": "Run",
    "Maven.pedestal-x-app [package].executor": "Run",
    "Maven.pedestal-x-module [clean].executor": "Run",
    "Maven.pedestal-x-module [deploy].executor": "Run",
    "Maven.pedestal-x-module [install].executor": "Run",
    "Maven.pedestal-x-module [site].executor": "Run",
    "Maven.placement [install].executor": "Run",
    "Maven.placement-dao [deploy].executor": "Run",
    "Maven.placement-impl [deploy].executor": "Run",
    "Maven.placement-pojo [deploy].executor": "Run",
    "Maven.plumelog [clean].executor": "Run",
    "Maven.plumelog [install].executor": "Run",
    "Maven.projection [install].executor": "Run",
    "Maven.scene-x [clean].executor": "Run",
    "Maven.scene-x [dependency:analyze].executor": "Run",
    "Maven.scene-x [install].executor": "Run",
    "Maven.scene-x [package].executor": "Run",
    "Maven.system-x [clean].executor": "Run",
    "Maven.system-x [package].executor": "Run",
    "Maven.tenant [install].executor": "Run",
    "Maven.tenant-dao [deploy].executor": "Run",
    "Maven.tenant-impl [clean].executor": "Run",
    "Maven.tenant-impl [deploy].executor": "Run",
    "Maven.tenant-impl [install].executor": "Run",
    "Maven.tenant-migrate [clean].executor": "Run",
    "Maven.tenant-migrate [deploy].executor": "Run",
    "Maven.twin [deploy].executor": "Run",
    "Maven.twin [install].executor": "Run",
    "Maven.twin-common [deploy].executor": "Run",
    "Maven.twin-common [install].executor": "Run",
    "Maven.twin-dao [deploy].executor": "Run",
    "Maven.twin-impl [clean].executor": "Run",
    "Maven.twin-impl [compile].executor": "Run",
    "Maven.twin-impl [deploy].executor": "Run",
    "Maven.twin-impl [install].executor": "Run",
    "Maven.twin-jrm [clean].executor": "Run",
    "Maven.twin-jrm [deploy].executor": "Run",
    "Maven.twin-jrm [install].executor": "Run",
    "Maven.twin-jrm [package].executor": "Run",
    "Maven.twin-pojo [deploy].executor": "Run",
    "Maven.twin-pojo [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.EdtapServerApplication.executor": "Debug",
    "Spring Boot.GatewayXApplication.executor": "Debug",
    "Spring Boot.SceneXApplication.executor": "Debug",
    "Spring Boot.SystemXApplication.executor": "Debug",
    "git-widget-placeholder": "feat",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/x/pedestal-x/pedestal-x-module/identity/identity-pojo/src/main/resources/db/dm/migration/identity/1.0.1",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "onboarding.tips.debug.path": "D:/x/component/common-x/common-x-boot2/common-x-boot2-label/src/main/java/com/uino/x/Main.java",
    "prettierjs.PrettierConfiguration.Package": "D:\\x\\u-work-base\\node_modules\\prettier",
    "project.structure.last.edited": "Libraries",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "MavenSettings",
    "ts.external.directory.path": "D:\\x\\u-work-base\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\x\pedestal-x\pedestal-x-module\identity\identity-pojo\src\main\resources\db\dm\migration\identity\1.0.1" />
      <recent name="D:\x\pedestal-x\pedestal-x-module\twin\twin-pojo\src\main\resources\db\dm\migration\twin\1.0.0" />
      <recent name="D:\x\pedestal-x\pedestal-x-module\placement\placement-pojo\src\main\resources\db\dm\migration\placement\1.0.0" />
      <recent name="D:\x\pedestal-x\pedestal-x-module\effectpackage\effectpackage-pojo\src\main\resources\db\dm\migration\effectpackage\1.0.0" />
      <recent name="D:\x\pedestal-x\pedestal-x-module\effectpackage\effectpackage-pojo\src\main\resources\db\mysql\migration\effectpackage\1.0.0" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\x\component\common-x\common-x-tool\common-x-tool-spring\src\main\resources\META-INF" />
      <recent name="D:\x\component\common-x\common-x-security\src\main\resources\META-INF" />
      <recent name="D:\x\component\common-x\common-x-dameng\src\main\resources\META-INF" />
      <recent name="D:\x\component\common-x\common-x-config\src\main\resources\META-INF" />
      <recent name="D:\x\edtap-server\edtap-app\src\main\resources\db\dm\edtap-migration" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.uino.x.common.boot2.sql.transformation" />
      <recent name="com.uino.x.common.datasource.util" />
      <recent name="com.uino.x.common.datasource.sql.transformation" />
      <recent name="com.uino.x.common.datasource.sql.transformation.strategy" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.uino.x.common.sql" />
      <recent name="com.uino.x.pedestal.twin.impl.ddl" />
      <recent name="com.uino.x.pedestal.twin.jrm.json.serialize" />
      <recent name="com.uino.x.common.core" />
      <recent name="com.uino.x.common.config" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn dependency:analyze" />
      <command value="mvn versions:set -DnewVersion=2.2.2-SNAPSHOT" />
      <command value="mvn versions:set -DnewVersion=2.2.3-SNAPSHOT" />
      <command value="mvn versions:set -DnewVersion=2.2.4-SNAPSHOT" />
      <command value="mvn versions:set -DnewVersion=1.0.2-SNAPSHOT" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SystemXApplication">
    <configuration name="DatabaseMigrationToolApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uino.x.migration.DatabaseMigrationToolApplication" />
      <module name="database-migration-tool" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.uino.x.migration.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uino.x.common.rsautiltools.Main" />
      <module name="rsautil-tools" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.uino.x.common.rsautiltools.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SysUserController" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uino.x.pedestal.app.systemx.controller.identity.SysUserController" />
      <module name="system-x" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.uino.x.pedestal.app.systemx.controller.identity.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SqlTransformationEngineTest (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="common-x-boot2-sql" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.uino.x.common.sql.transformation.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.uino.x.common.sql.transformation" />
      <option name="MAIN_CLASS_NAME" value="com.uino.x.common.sql.transformation.SqlTransformationEngineTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SqlTransformationEngineTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="common-x-boot2-sql" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.uino.x.common.boot2.sql.transformation.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.uino.x.common.boot2.sql.transformation" />
      <option name="MAIN_CLASS_NAME" value="com.uino.x.common.boot2.sql.transformation.SqlTransformationEngineTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EdtapServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <envs>
        <env name="NACOS_IP" value="***********" />
        <env name="SERVER_PROFILES" value="pedestal-x" />
      </envs>
      <module name="edtap-app" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uino.x.EdtapServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayXApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <envs>
        <env name="NACOS_IP" value="***********" />
        <env name="SERVER_PROFILES" value="pedestal-x" />
      </envs>
      <module name="gateway-x-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uino.x.GatewayXApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SceneXApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <envs>
        <env name="NACOS_IP" value="***********" />
        <env name="SERVER_PROFILES" value="pedestal-x" />
      </envs>
      <module name="scene-x" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uino.x.pedestal.app.scenex.SceneXApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SystemXApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <envs>
        <env name="NACOS_IP" value="***********" />
        <env name="SERVER_PROFILES" value="pedestal-x" />
      </envs>
      <module name="system-x" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.uino.x.pedestal.app.systemx.SystemXApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.Main" />
      <item itemvalue="Application.SysUserController" />
      <item itemvalue="Application.DatabaseMigrationToolApplication" />
      <item itemvalue="JUnit.SqlTransformationEngineTest" />
      <item itemvalue="JUnit.SqlTransformationEngineTest (1)" />
      <item itemvalue="Spring Boot.SceneXApplication" />
      <item itemvalue="Spring Boot.EdtapServerApplication" />
      <item itemvalue="Spring Boot.GatewayXApplication" />
      <item itemvalue="Spring Boot.SystemXApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.DatabaseMigrationToolApplication" />
        <item itemvalue="Application.Main" />
        <item itemvalue="Application.SysUserController" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="Physical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7b4b309d-6f94-410d-910a-6ed8e1f0f634" name="Changes" comment="" />
      <changelist id="bdb920d6-301c-4a85-824d-373d7815a9f3" name="feat(config): 修改group by&amp;新增达梦flyway" comment="feat(config): 修改group by&amp;新增达梦flyway" />
      <created>1739257819920</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1739257819920</updated>
      <workItem from="1751254627978" duration="1161000" />
      <workItem from="1751262021719" duration="17765000" />
      <workItem from="1751279890378" duration="12868000" />
      <workItem from="1751349218713" duration="19023000" />
      <workItem from="1751372462653" duration="200000" />
      <workItem from="1751447537034" duration="66372000" />
      <workItem from="1751850836212" duration="30611000" />
      <workItem from="1751944656360" duration="50785000" />
      <workItem from="1752110127895" duration="58455000" />
      <workItem from="1752542255171" duration="60233000" />
      <workItem from="1752715348574" duration="53605000" />
      <workItem from="1753061487790" duration="77325000" />
      <workItem from="1753319728921" duration="29676000" />
      <workItem from="1753408050443" duration="2014000" />
      <workItem from="1753412991916" duration="3857000" />
      <workItem from="1753428519473" duration="733000" />
      <workItem from="1753667612850" duration="107635000" />
      <workItem from="1754272593608" duration="21622000" />
      <workItem from="1754382137960" duration="83869000" />
      <workItem from="1754875569088" duration="3304000" />
      <workItem from="1754880374332" duration="807000" />
      <workItem from="1754881205910" duration="799000" />
      <workItem from="1754882021609" duration="330000" />
      <workItem from="1754882370708" duration="1135000" />
      <workItem from="1754883527768" duration="3042000" />
      <workItem from="1754890585566" duration="42484000" />
      <workItem from="1755134172120" duration="24116000" />
      <workItem from="1755220982555" duration="28401000" />
      <workItem from="1755479904388" duration="45868000" />
      <workItem from="1755757274695" duration="29224000" />
      <workItem from="1755849295771" duration="6898000" />
      <workItem from="1756085166135" duration="37254000" />
      <workItem from="1756191244857" duration="85813000" />
    </task>
    <task id="LOCAL-00005" summary="feat(pom): 版本升级到2.2.1">
      <option name="closed" value="true" />
      <created>1751277013533</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751277013533</updated>
    </task>
    <task id="LOCAL-00006" summary="feat(pom): 版本升级到2.2.1">
      <option name="closed" value="true" />
      <created>1751277035114</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751277035114</updated>
    </task>
    <task id="LOCAL-00007" summary="feat(datasource): 重构sql转换">
      <option name="closed" value="true" />
      <created>1751358845478</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751358845478</updated>
    </task>
    <task id="LOCAL-00008" summary="feat(pom): 升级版本到1.0.1-SNAPSHOT">
      <option name="closed" value="true" />
      <created>1751358981511</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751358981511</updated>
    </task>
    <task id="LOCAL-00009" summary="feat(dm): JSONB -&gt; TEXT">
      <option name="closed" value="true" />
      <created>1751359212902</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1751359212902</updated>
    </task>
    <task id="LOCAL-00010" summary="feat(pom): 升级版本到1.0.1-SNAPSHOT">
      <option name="closed" value="true" />
      <created>1751360073626</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1751360073626</updated>
    </task>
    <task id="LOCAL-00011" summary="feat(dm): flyway sql format">
      <option name="closed" value="true" />
      <created>1751363612363</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1751363612363</updated>
    </task>
    <task id="LOCAL-00012" summary="feat(datasource): 重构sql转换">
      <option name="closed" value="true" />
      <created>1751368581804</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1751368581804</updated>
    </task>
    <task id="LOCAL-00013" summary="feat(datasource): 重构sql转换">
      <option name="closed" value="true" />
      <created>1751369142899</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1751369142899</updated>
    </task>
    <task id="LOCAL-00014" summary="feat(datasource): 初始化数据库关闭TestWhileIdle">
      <option name="closed" value="true" />
      <created>1751525072933</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1751525072933</updated>
    </task>
    <task id="LOCAL-00015" summary="feat(app): 达梦数据库兼容">
      <option name="closed" value="true" />
      <created>1751526035632</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1751526035632</updated>
    </task>
    <task id="LOCAL-00016" summary="feat(datasource): sql转换日志优化">
      <option name="closed" value="true" />
      <created>1751529341724</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1751529341724</updated>
    </task>
    <task id="LOCAL-00017" summary="feat(datasource): sql转换日志结构优化&amp;解析抽取">
      <option name="closed" value="true" />
      <created>1751530239995</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1751530239995</updated>
    </task>
    <task id="LOCAL-00018" summary="feat(datasource): sql转换日志优化">
      <option name="closed" value="true" />
      <created>1751530509929</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1751530509929</updated>
    </task>
    <task id="LOCAL-00019" summary="feat(core): 完善VirtualCopyRequest">
      <option name="closed" value="true" />
      <created>1751535142691</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1751535142692</updated>
    </task>
    <task id="LOCAL-00020" summary="feat(pom): 去掉okhttp">
      <option name="closed" value="true" />
      <created>1751537064725</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1751537064725</updated>
    </task>
    <task id="LOCAL-00021" summary="feat(config): feign&amp;aop配置优化">
      <option name="closed" value="true" />
      <created>1751544118482</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1751544118482</updated>
    </task>
    <task id="LOCAL-00022" summary="del(module): plugin">
      <option name="closed" value="true" />
      <created>1751544714439</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751544714439</updated>
    </task>
    <task id="LOCAL-00023" summary="feat(pom): upgrade to 2.2.2-SNAPSHOT&#10;&#10;移除spring-cloud-starter-bootstrap">
      <option name="closed" value="true" />
      <created>1751618856054</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751618856054</updated>
    </task>
    <task id="LOCAL-00024" summary="feat(pom): upgrade to 2.2.2-SNAPSHOT&#10;&#10;1. 去掉swagger、incubation、plugin模块&#10;2. dependency:2.2.2-SNAPSHOT&#10;3. 所有模块采用自动装配&#10;4. 重构SpingAwareUtils、缓存操作类&#10;5. 去掉不必要的feign旧版本配置&#10;6. 规范objectstorage包名&#10;7. RedisDataBaseProperties支持指定用途的redis库&#10;8. 去掉SpringPropertiesConfigurerUtils，合并到SpringEnvUtils">
      <option name="closed" value="true" />
      <created>1751619528744</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1751619528744</updated>
    </task>
    <task id="LOCAL-00025" summary="feat(config): plumelog查询异常时默认返回空数据">
      <option name="closed" value="true" />
      <created>1751625467009</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1751625467009</updated>
    </task>
    <task id="LOCAL-00026" summary="feat(pom): seata:2.3.0">
      <option name="closed" value="true" />
      <created>1751874622454</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1751874622454</updated>
    </task>
    <task id="LOCAL-00027" summary="feat(pom): seata:2.3.0">
      <option name="closed" value="true" />
      <created>1751874692238</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1751874692238</updated>
    </task>
    <task id="LOCAL-00028" summary="feat(file): groupby select">
      <option name="closed" value="true" />
      <created>1751874723288</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1751874723288</updated>
    </task>
    <task id="LOCAL-00029" summary="feat(file): groupby select">
      <option name="closed" value="true" />
      <created>1751874748629</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1751874748629</updated>
    </task>
    <task id="LOCAL-00030" summary="feat(config): plumelog feign降级回调">
      <option name="closed" value="true" />
      <created>1751876167029</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1751876167029</updated>
    </task>
    <task id="LOCAL-00031" summary="feat(pom): 去掉plugin, pom">
      <option name="closed" value="true" />
      <created>1751878503237</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1751878503237</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(datasource): sql转换支持带注释的sql">
      <option name="closed" value="true" />
      <created>1751964123945</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1751964123945</updated>
    </task>
    <task id="LOCAL-00033" summary="feat(datasource): sql转换过滤器支持更多重载">
      <option name="closed" value="true" />
      <created>1751965969453</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1751965969453</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(model): sql双引号改单引号">
      <option name="closed" value="true" />
      <created>1752030605580</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1752030605580</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(twin): 多个insert改成多个values">
      <option name="closed" value="true" />
      <created>1752030698138</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1752030698138</updated>
    </task>
    <task id="LOCAL-00036" summary="feat(datasource): sql转换支持嵌套引号转换&amp;replace into支持多values">
      <option name="closed" value="true" />
      <created>1752030897721</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1752030897721</updated>
    </task>
    <task id="LOCAL-00037" summary="feat(twin): 达梦孪生体表单适配">
      <option name="closed" value="true" />
      <created>1752031978475</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1752031978475</updated>
    </task>
    <task id="LOCAL-00038" summary="feat(tenant): 达梦租户迁移schema切换适配">
      <option name="closed" value="true" />
      <created>1752032025871</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1752032025871</updated>
    </task>
    <task id="LOCAL-00039" summary="feat(twin): 达梦孪生体&amp;apijson适配">
      <option name="closed" value="true" />
      <created>1752045517396</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1752045517396</updated>
    </task>
    <task id="LOCAL-00040" summary="feat(core): 线程池最小核心线程1个">
      <option name="closed" value="true" />
      <created>1752050125906</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1752050125906</updated>
    </task>
    <task id="LOCAL-00041" summary="feat(twin): apijson自定义json序列化保持顺序&amp;map结果clob转string">
      <option name="closed" value="true" />
      <created>1752140797706</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1752140797706</updated>
    </task>
    <task id="LOCAL-00042" summary="feat(pom): apijson:8.0.2">
      <option name="closed" value="true" />
      <created>1752141269282</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1752141269282</updated>
    </task>
    <task id="LOCAL-00043" summary="feat(dict): map结果clob转string">
      <option name="closed" value="true" />
      <created>1752141467348</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1752141467348</updated>
    </task>
    <task id="LOCAL-00044" summary="feat(api): feign降级回调配置重构, 使用BasicFallbackFactory">
      <option name="closed" value="true" />
      <created>1752475477994</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1752475477994</updated>
    </task>
    <task id="LOCAL-00045" summary="feat(config): 新增BasicFallbackFactory">
      <option name="closed" value="true" />
      <created>1752475528207</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1752475528207</updated>
    </task>
    <task id="LOCAL-00046" summary="feat(datasource): 去掉seata">
      <option name="closed" value="true" />
      <created>1752571401828</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1752571401828</updated>
    </task>
    <task id="LOCAL-00047" summary="feat(pom): javax.json:1.1.4">
      <option name="closed" value="true" />
      <created>1752571460760</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1752571460760</updated>
    </task>
    <task id="LOCAL-00048" summary="feat(impl): 去掉seata相关事务操作并优化spring事务到方法级">
      <option name="closed" value="true" />
      <created>1752571651782</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1752571651782</updated>
    </task>
    <task id="LOCAL-00049" summary="feat(module): 去掉seata相关事务操作并优化spring事务到方法级">
      <option name="closed" value="true" />
      <created>1752571796709</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1752571796709</updated>
    </task>
    <task id="LOCAL-00050" summary="feat(twin): 增强jrm ddl,支持索引的create&amp;drop">
      <option name="closed" value="true" />
      <created>1753254678306</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1753254678306</updated>
    </task>
    <task id="LOCAL-00051" summary="feat: 添加用户信息映射接口及用户信息LRU缓存管理类支持apijson获取的孪生体用户信息">
      <option name="closed" value="true" />
      <created>1754983734392</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1754983734392</updated>
    </task>
    <task id="LOCAL-00052" summary="feat(pom): 将版本号更新为1.0.2-SNAPSHOT，并升级common-x版本到2.2.4-SNAPSHOT">
      <option name="closed" value="true" />
      <created>1755164831928</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1755164831929</updated>
    </task>
    <task id="LOCAL-00053" summary="feat(sql): 优化insert跨库获取">
      <option name="closed" value="true" />
      <created>1755248370098</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1755248370098</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="02fd085a-5e3c-444a-9e95-b97b44415f13" value="TOOL_WINDOW" />
        <entry key="b2b09290-b41e-44cf-9bd8-c4cded2719d0" value="TOOL_WINDOW" />
        <entry key="9fb169dd-7028-4bb9-a616-c19faf7aa105" value="TOOL_WINDOW" />
        <entry key="26672db5-2997-4a9e-acb3-5db94aafef18" value="TOOL_WINDOW" />
        <entry key="ec358522-c6d8-4e3a-96f5-e4cdc7236cbe" value="TOOL_WINDOW" />
        <entry key="7f267a84-a611-47b6-b34c-9394b809537a" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dev-syq" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dev" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/dev" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/prod" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feat-oceanbase" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feat-pedestal-x" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="assemble_feat" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feat-dameng" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="02fd085a-5e3c-444a-9e95-b97b44415f13">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/component/common-x" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="26672db5-2997-4a9e-acb3-5db94aafef18">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/pedestal-x" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="7f267a84-a611-47b6-b34c-9394b809537a">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/u-work-base" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="9fb169dd-7028-4bb9-a616-c19faf7aa105">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/component/dependency-x" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="b2b09290-b41e-44cf-9bd8-c4cded2719d0">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/edtap-server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="ec358522-c6d8-4e3a-96f5-e4cdc7236cbe">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/project-base-web" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat(twin): 达梦孪生体表单适配" />
    <MESSAGE value="feat(tenant): 达梦租户迁移schema切换适配" />
    <MESSAGE value="feat(twin): 达梦孪生体&amp;apijson适配" />
    <MESSAGE value="feat(core): 线程池最小核心线程1个" />
    <MESSAGE value="feat(twin): apijson自定义json序列化保持顺序&amp;map结果clob转string" />
    <MESSAGE value="feat(pom): apijson:8.0.2" />
    <MESSAGE value="feat(dict): map结果clob转string" />
    <MESSAGE value="feat(api): feign降级回调配置重构" />
    <MESSAGE value="feat(api): feign降级回调配置重构, 使用BasicFallbackFactory" />
    <MESSAGE value="feat(config): 新增BasicFallbackFactory" />
    <MESSAGE value="feat(datasource): 去掉seata相关依赖" />
    <MESSAGE value="feat(datasource): 去掉seata相关依赖" />
    <MESSAGE value="feat(pom): javax.json:1.1.4" />
    <MESSAGE value="feat(impl): 去掉seata相关事务操作并优化spring事务到方法级" />
    <MESSAGE value="feat(module): 去掉seata相关事务操作并优化spring事务到方法级" />
    <MESSAGE value="feat(enterprise): 启停团队时启停项目" />
    <MESSAGE value="feat(enterprise): 启停团队时启停项目" />
    <MESSAGE value="fix(twin): 移除TwinAllMapper.xml中saveAllBatch的ON DUPLICATE KEY UPDATE后的主键" />
    <MESSAGE value="fix(twin): 移除TwinAllMapper.xml中saveAllBatch的ON DUPLICATE KEY UPDATE后的主键" />
    <MESSAGE value="feat(twin): 增强jrm ddl,支持索引的create&amp;drop" />
    <MESSAGE value="fix(twin): 移除索引操作中的异常处理，在ddl类中忽略异常" />
    <MESSAGE value="fix(twin): 移除索引操作中的异常处理，在ddl类中忽略异常" />
    <MESSAGE value="feat: 添加用户信息映射接口及用户信息LRU缓存管理类支持apijson获取的孪生体用户信息" />
    <MESSAGE value="feat(pom): 将版本号更新为1.0.2-SNAPSHOT，并升级common-x版本到2.2.4-SNAPSHOT" />
    <MESSAGE value="feat(sql): 优化insert跨库获取" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(sql): 优化insert跨库获取" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/component/common-x/common-x-boot2/common-x-boot2-sql/src/test/java/com/uino/x/common/boot2/sql/transformation/SqlTransformationEngineTest.java</url>
          <line>99</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <condition expression="path.contains(&quot;list-user-id-mapping&quot;)" language="JAVA" />
          <url>file://$PROJECT_DIR$/pedestal-x/pedestal-x-app/system-x/src/main/java/com/uino/x/pedestal/app/systemx/config/callback/PermissionAopAdviceCallback.java</url>
          <line>179</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/license/license-impl/src/main/java/com/uino/x/pedestal/license/impl/ProductLicenseInfoServiceImpl.java</url>
          <line>138</line>
          <option name="timeStamp" value="120" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../apache-maven-3.9.9/repo/com/uino/productauth-sdk/1.5.0.RELEASE/productauth-sdk-1.5.0.RELEASE.jar!/com/uino/license/sdk/e.class</url>
          <line>28</line>
          <option name="timeStamp" value="121" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pedestal-x/pedestal-x-module/model/model-impl/src/main/java/com/uino/x/pedestal/model/impl/util/BundleLibUtil.java</url>
          <line>245</line>
          <option name="timeStamp" value="133" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="dataSource" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>