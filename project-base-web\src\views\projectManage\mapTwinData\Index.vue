<template>
  <div class="map-twin keep-px">
    <div class="tabs-list">
      <div class="campus-name">
        <img :src="systemLogo" class="header-logo" alt="header-logo" />
        <div class="header-title">{{ sceneName }}</div>
      </div>
      <div class="map-list">
        <a-tabs v-model="activeKey" @change="changeTab">
          <a-tab-pane v-for="(item, i) in mapTabs" :key="i" :tab="`${item.name}(${item.coords})`" />
        </a-tabs>
      </div>
      <div class="btn-list">
        <a-button class="addmap" type="primary" @click="mapServe"> 新增地图服务 </a-button>
      </div>
    </div>
    <div class="map-container">
      <MapLayer ref="mapLayerRef" :current-tab="currentTab" />
      <TableContent>
        <TwinDataManage ref="twinDataManageRef" @set-ass-map-open-edit="setAssMapOpenEdit" @refresh-map="refreshMap" />
      </TableContent>
      <!-- <EditForm v-if="useStore.showEditPanel" :current-tab="currentTab" @update="updateTable"></EditForm> -->
      <div class="mouse-pos">
        <div class="pos-wrappper">
          <span><label>经度：</label>{{ (+currentPos.lng).toFixed(6) }}</span>
          <span><label>纬度：</label>{{ (+currentPos.lat).toFixed(6) }}</span>
          <span title="保存默认视角" @click="saveDefaultPosition"><eye-outlined /></span>
        </div>
      </div>
      <div id="earthOutContainer" class="earth-container"></div>
    </div>
    <AddMapServe ref="addMapServeRef" />
    <Loading v-show="loading" :loading-percent="loadingPercent" />
    <div v-if="useStore.showEditPanel" id="map-place-twin-modal" class="map-place-twin-modal">
      <!-- 新增编辑弹窗 -->
      <EditForm :current-tab="currentTab" @update="updateTable" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch } from 'vue';
import { useRoute } from 'vue-router';
import coordtransform from 'coordtransform';
import TwinDataManage from '@/views/projectManage/twinObject/twinDataManage/Index.vue';
import { initApp } from '@/utils/scene/index';
import { MapManager } from '@/utils/scene/index';
import { getBaseMapList, changeAllMapDefaultCamInfo } from '@/api/business/mapManag';
import { twinClassDetail, getTwinData } from '@/api/business/twinObject';
import { loadJsFiles } from '@/utils/util';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { MapPlacement, CreatePointTwin, CreateLineTwin, CreateRegionTwin } from '@/utils/mapPlacement/index';
import { useMapStore } from '@/store/map';

import AddMapServe from './AddMapServe.vue';
import MapLayer from './MapLayer.vue';
import TableContent from './TableContent.vue';
import Loading from './Loading.vue';
import EditForm from './EditForm.vue';
import { useSceneStore } from '@/store/scene';
import headerLogo from '@/assets/img/header/header-logo.svg';
import axios from 'axios';
const tenant = axios.defaults.headers.common['Tenant'];
// 系统logo
const systemLogo = ref(sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`) || headerLogo);
// 系统标题
const systemName = ref(sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) || '');
// 修改link-icon
let favicon = document.querySelector('link[rel="icon"]');
if (favicon) {
  // @ts-ignore
  favicon.href = systemLogo.value;
}
// 项目名称
const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
if (projectName.value) {
  document.title = `${systemName.value} - ${projectName.value}`;
} else {
  document.title = `${systemName.value}`;
}
const route = useRoute();
const useStore = useMapStore();
const sceneStore = useSceneStore();
// 获取名称和uuid
const sceneName = ref();
const { query } = useRoute();
const { name } = query;
// 保存孪生对象名称
sceneName.value = name;
// 加载loading
let timer: any = null;
const loading = ref(true);
const loadingPercent = ref(0);

const mapLayerRef = ref();
const mapTabs = ref<any>([]); // 底图列表
const activeKey = ref(0);
const currentTab = ref<any>({}); // 当前选中的底图
const currentPos = reactive({
  lat: 0,
  lng: 0,
});
const twinData: any = ref({});
const twinDataManageRef = ref();

// 获取孪生对象详情
const getTwinClassData = async () => {
  const res = await twinClassDetail({ id: route.query.id });
  if (res.code === 200) {
    res.data.bubbleInfoVo.imgUrl = `${window.baseConfig.previewResourceUrl}${res.data.bubbleInfoVo.imgUrl}`;
    twinData.value = res.data;
    useStore.updateTwinData(res.data);
    // 显示数据
    twinDataManageRef.value.init(twinData.value);
  }
};

// 切换底图
const changeTab = (val: any) => {
  activeKey.value = val;
  // 判断当前和切换的底图坐标系是否一致,重新加载地图点位
  if (currentTab.value.coords !== mapTabs.value[activeKey.value]?.coords) {
    // 初始化摆点和绘制
    window.mapPlacementIns.reset(true);
    window.mapDrawInstance.reset();
    useStore.markFlag = false;
    // 销毁点位
    const point = window.app.query('.GeoPoint');
    const line = window.app.query('.GeoLine');
    const region = window.app.query('.GeoPolygon');
    if (sceneStore.thingjsVersion === 1) {
      point.destroyAll();
      line.destroyAll();
      region.destroyAll();
    } else if (sceneStore.thingjsVersion === 2) {
      console.log('销毁点位 all');
      point.forEach((item: any) => {
        item.destroy();
      });
      line.forEach((item: any) => {
        item.destroy();
      });
      region.forEach((item: any) => {
        item.destroy();
      });
    }
    refreshMap();
  }
  currentTab.value = mapTabs.value[activeKey.value];

  if (sceneStore.thingjsVersion === 1) {
    window.mapManagerIns.changeTile({
      tileLayerUrl: mapTabs.value[activeKey.value]?.tilesUrl, // 瓦片地址
      maximumLevel: 18, // 最大渲染层级 默认18
    });
  } else if (sceneStore.thingjsVersion === 2) {
    window.mapManagerIns.changeTile({
      url: mapTabs.value[activeKey.value]?.tilesUrl, // 瓦片地址
      name: mapTabs.value[activeKey.value]?.name,
      maximumLevel: 18, // 最大渲染层级 默认18
    });
  }
};

// 获取底图列表
const getMapList = () => {
  getBaseMapList({}).then(async (res) => {
    if (res.success) {
      if (!res.data || !res.data.length) {
        useGlobalMessage('error', '请添加地图瓦片服务！');
        return;
      }
      mapTabs.value = res.data.filter((item: any) => item.type === 0);
      activeKey.value = 0;
      currentTab.value = mapTabs.value[0];
      await getTwinClassData();
      // 加载默认地图服务
      initMap();
    }
  });
};

// 初始化地球
const initMap = () => {
  loadDepend(() => {
    createMap();
  });
};

// 加载依赖
const loadDepend = (cb: Function) => {
  loadJsFiles(() => {
    cb && cb();
  }, ['thingjs', 'uearth', 'thing.campus']);
};

// 创建地球
const createMap = async () => {
  loadProgress();
  //  初始化app实例
  const app = await initApp('earthOutContainer');
  window.app = app;
  // 创建地球
  window.mapManagerIns = MapManager({
    app, // THINGJS实例
    tileLayerUrl: mapTabs.value[activeKey.value]?.tilesUrl,
    mapLoaded: (object: any) => {
      object.attribution = 'none';
      initCamera();
      // 实时更新经纬度
      window.app.on(THING.EventType.MouseMove, (ev: any) => {
        const position = ev.pickedPosition ? ev.pickedPosition : sceneStore.thingjsVersion === 1 ? window.app.camera.screenToWorld(ev.x, ev.y) : window.app.camera.screenToWorld([ev.x, ev.y]);
        // @ts-ignore
        const pos = sceneStore.thingjsVersion === 1 ? CMAP.Util.convertWorldToLonlat(position) : THING.EARTH.Utils.convertWorldToLonlat(position);
        const [lng, lat] = pos;
        currentPos.lat = lat;
        currentPos.lng = lng;
      });
      // 隐藏loading
      timer && clearInterval(timer);
      loadingPercent.value = 100;
      setTimeout(() => {
        loading.value = false;
      }, 50);
      // 注册孪生体摆点相关事件
      mapLoaded();
      // 加载所有孪生体
      loadAllTwin();
    },
  });
};

// 初始化加载进度监听
const loadProgress = () => {
  timer = setInterval(() => {
    if (loadingPercent.value < 99) {
      loadingPercent.value += 1;
    }
  }, 5);
};

// 初始化默认视角
const initCamera = () => {
  // 飞到默认视角去
  if (currentTab.value.defaultCamInfo) {
    const caminfo = JSON.parse(currentTab.value.defaultCamInfo);
    window.app.camera.flyTo({
      position: caminfo.position,
      target: caminfo.target,
      time: 0,
    });
  } else {
    // 没有默认视角飞到北京天安门
    let pos = [116.397451, 39.909187];
    if (currentTab.value.coords === 'WGS84') {
      pos = coordtransform.gcj02towgs84(pos[0], pos[1]);
    }
    window.app.camera.earthFlyTo({
      lonlat: pos,
      time: 3000,
      height: 1000,
    });
  }
};

// 保存默认视角
const saveDefaultPosition = () => {
  const params = {
    position: window.app.camera.position,
    target: window.app.camera.target,
  };
  changeAllMapDefaultCamInfo(params).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '默认视角保存成功');
    }
  });
};

// 新增地图服务
const addMapServeRef = ref();
const mapServe = () => {
  addMapServeRef.value.init();
};

// 场景加载完毕处理方法
const mapLoaded = () => {
  // 初始化摆点控制实例
  window.mapPlacementIns = MapPlacement(window.app);
  if (twinData.value.dataType === 'POINT') {
    window.mapDrawInstance = CreatePointTwin(window.app, twinData.value);
  } else if (twinData.value.dataType === 'LINE') {
    window.mapDrawInstance = CreateLineTwin(window.app);
  } else if (twinData.value.dataType === 'SURFACE') {
    window.mapDrawInstance = CreateRegionTwin(window.app);
  }
};

const updateTable = () => {
  twinDataManageRef.value.getData();
  refreshMap();
  // 保存回调，如果标记还是开启状态，则需要重新初始化
  if (useStore.markFlag) {
    mapLayerRef.value.handleMark(true);
  }
};

const loadAllTwin = () => {
  const table = twinData.value.code;
  getTwinData({
    '[]': {
      [table]: { '@order': 'create_time-,uuid-' },
    },
    'total@': '/[]/total',
    'info@': '/[]/info',
  }).then((res) => {
    // 加载点位
    const data = res['[]'] || [];
    data.forEach((item: any) => {
      if (item[table].wgs84_position || item[table].gcj02_position) {
        window.mapDrawInstance.creatThing(item[table], currentTab.value.coords);
      }
    });
  });
};

const setAssMapOpenEdit = (record: any) => {
  mapLayerRef.value.handleMark(true, record);
};

const refreshMap = () => {
  loadAllTwin();
};

// 监听地图上删除按钮
watch(
  () => useStore.deleteThing,
  () => {
    updateTable();
  }
);
onMounted(() => {
  getMapList();
});
</script>
<style scoped lang="scss">
.map-twin.keep-px {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .map-place-twin-modal {
    position: absolute;
    top: 200px;
    right: 400px;
    z-index: 999;
    width: 320px;
    // max-height: 520px;
    overflow-y: hidden;
    border: 1px solid var(--primary-border-color);
    border-radius: 6px;
    box-shadow:
      inset 0 0 10px 0 rgb(0 0 0 / 10%),
      0 0 10px 0 rgb(0 0 0 / 10%);
  }

  .tabs-list {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-width: 1200px;
    height: 55px;
    background: var(--second-bg-color);
    border-bottom: 1px solid var(--header-border-color);
    outline: none;

    .campus-name {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 280px;
      min-width: 280px;
      height: 55px;
      padding: 0 0 0 24px;
      overflow: hidden;

      .header-logo {
        width: auto;
        height: 26px;
        margin-right: 8px;
      }

      .header-title {
        flex: 1;
        height: 55px;
        overflow: hidden;
        font-family: 'PingFang Bold';
        font-size: 16px;
        font-weight: 500;
        line-height: 55px;
        color: var(--primary-text-color);
      }
    }

    .map-list {
      flex: 1;
      margin-left: -280px;
    }

    .btn-list {
      margin-right: 32px;
    }
  }

  .map-container {
    position: relative;
    height: calc(100% - 55px);

    .earth-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .mouse-pos {
      position: absolute;
      bottom: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 35px;

      .pos-wrappper {
        display: flex;
        align-items: center;
        padding: 5px 10px;
        background: var(--primary-bg-color);
        border-radius: 5px;

        span {
          margin-right: 10px;

          &:last-child {
            margin-top: 1px;
            margin-right: 0;
            cursor: pointer;
          }
        }
      }
    }
  }
}

:deep .ant-tabs-nav {
  margin: 0 !important;

  .ant-tabs-nav-wrap {
    justify-content: center;
    height: 55px;
    min-height: 55px;
    max-height: 55px;
  }
}
</style>
