<!--
 * @Description: 三维场景-地球点位
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-05-10 10:31:37
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 13:52:29
-->
<template>
  <div class="map-preview keep-px">
    <div class="tabs-list">
      <div class="campus-name">
        <img :src="systemLogo" class="header-logo" alt="header-logo" />
        <div class="header-title">{{ sceneName }}</div>
      </div>
      <div class="map-list">
        <a-tabs v-model="activeKey" @change="changeTab">
          <a-tab-pane v-for="(item, i) in mapTabs" :key="i" :disabled="!mapLoaded" :tab="`${item.name}(${item.coords})`" />
        </a-tabs>
      </div>
    </div>
    <div class="map-container">
      <div id="map-div"></div>
      <div class="map-toggle">
        <a-select ref="select" v-model:value="mapType" style="width: 90px" @change="handleChangeMapType">
          <a-select-option value="高德">高德</a-select-option>
          <a-select-option value="天地图">天地图</a-select-option>
        </a-select>
      </div>
      <div class="map-search">
        <div class="search" v-if="mapType === '高德'">
          <input id="suggestId" ref="SearchText" type="text" @focus="checkGdKey" placeholder="请输入需要查询的地点（需联网）" autocomplete="off" @keyup="setInputStyle" />
          <div id="search-clear" ref="SearchClear" class="search-clear" title="清空" @click="clearSearch"></div>
        </div>
        <div class="search" v-if="mapType === '天地图'">
          <input ref="SearchText" type="text" v-model="place" @focus="checkTdKey" placeholder="请输入需要查询的地点（需联网）" autocomplete="off" @keyup="handleSearch" />
          <div class="suggest-list" v-if="suggests.length">
            <div class="suggest-item" v-for="(item, index) in suggests" @click="location(item)" :key="index">
              <div class="name">{{ item.name }}</div>
              {{ item.address }}
            </div>
          </div>
          <div id="search-clear" ref="SearchTClear" class="search-clear" title="清空" @click="clearTSearch"></div>
        </div>
      </div>
      <div class="mouse-pos">
        <div class="pos-wrappper">
          <span><label>经度：</label>{{ (+currentPos.lng).toFixed(6) }}</span>
          <span><label>纬度：</label>{{ (+currentPos.lat).toFixed(6) }}</span>
        </div>
      </div>
      <div id="earthOutContainer" class="earth-container"></div>
      <div id="map-preview-content-panel" class="preview-content-panel">
        <div class="right-content">
          <div
            id="map-right-content-wrap"
            class="right-content-wrap"
            :style="{
              width: rightContentShow ? rightWidth + 'px' : '0px',
              opacity: rightContentShow ? '1' : '0',
            }"
          >
            <MapSceneTwins :coords="currentTab.coords" @toggle-control="toggleControl" />
          </div>
          <div class="right-content-expand keep-px" @click="changeRightContentShow">
            <div class="right-content-hand" :class="{ hide: !rightContentShow }"></div>
          </div>
          <div id="map-drag-bar" class="drag-bar"></div>
        </div>
      </div>
      <!-- 园区调整器 -->
      <div
        v-show="showSceneControl"
        class="map-scene-control"
        :style="{
          right: rightContentShow ? rightWidth + 33 + 'px' : '0px',
          transitionDuration: dragFlag ? '0s' : '0.3s',
        }"
      >
        <div class="position-control">
          <div class="top" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('lat', 'add')" @click="handleNum('lat', 'add', false)"></div>
          <div class="right" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('lon', 'add')" @click="handleNum('lon', 'add', false)"></div>
          <div class="down" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('lat', 'reduce')" @click="handleNum('lat', 'reduce', false)"></div>
          <div class="left" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('lon', 'reduce')" @click="handleNum('lon', 'reduce', false)"></div>
        </div>
        <div class="angle-control">
          <div class="sub" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('angle', 'reduce')" @click="handleNum('angle', 'reduce', false)"></div>
          <div class="add" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('angle', 'add')" @click="handleNum('angle', 'add', false)"></div>
          旋转
        </div>
        <div class="height-control">
          <div class="sub" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('height', 'reduce')" @click="handleNum('height', 'reduce', false)"></div>
          <div class="add" @mouseup="cancelOpe" @mouseleave="cancelOpe" @mousedown="send('height', 'add')" @click="handleNum('height', 'add', false)"></div>
          高度
        </div>
        <div class="function-control">
          <div class="location" title="定位" @click="flyToCampus"></div>
          <div class="save" title="保存" @click="save"></div>
        </div>
      </div>
      <Loading v-show="loading" :loading-percent="loadingPercent" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import coordtransform from 'coordtransform';
import MapSceneTwins from './MapSceneTwins.vue';
import { initApp } from '@/utils/scene/index';
import { MapManager } from '@/utils/scene/index';
import { CampusManager } from '@/utils/scene/index';
import { loadJsFiles, loadScript } from '@/utils/util';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { getBaseMapList, changeAllMapDefaultCamInfo } from '@/api/business/mapManag';
import { getSysConfigInfo } from '@/api/business/twinObject';
import { getSceneRecord, saveMapInfo } from '@/api/business/scene';
import { useSceneStore } from '@/store/scene';
import Loading from './Loading.vue';

import headerLogo from '@/assets/img/header/header-logo.svg';
import axios from 'axios';
const tenant = axios.defaults.headers.common['Tenant'];
// 系统logo
const systemLogo = ref(sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`) || headerLogo);
// 系统标题
const systemName = ref(sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) || '');
// 修改link-icon
let favicon = document.querySelector('link[rel="icon"]');
if (favicon) {
  // @ts-ignore
  favicon.href = systemLogo.value;
}
// 项目名称
const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
if (projectName.value) {
  document.title = `${systemName.value} - ${projectName.value}`;
} else {
  document.title = `${systemName.value}`;
}
const sceneStore = useSceneStore();
// 搜素地图类型
const mapType = ref('天地图');
const handleChangeMapType = (item: any) => {
  console.log('地图搜索切换', item);
  clearSearch();
  suggests.value = [];
  place.value = '';
  if (item === '高德') {
    loadAmapScript();
  } else if (item === '天地图') {
    loadTmapScript();
  }
};
// 当前经纬度
const currentPos = reactive({
  lat: 0,
  lng: 0,
});
// 园区定位
const flyToCampus = () => {
  window.app.camera.earthFlyTo({
    lonlat: scenePosition.value.lonlat,
    time: 1000,
    height: 1000,
  });
};
// 正在保存flag
const saveFlag = ref(false);
// 保存
const save = () => {
  if (saveFlag.value) {
    return;
  }
  saveFlag.value = true;
  const param = {
    uuid: sceneUuid.value,
    mapInfo: JSON.stringify({
      lng: scenePosition.value.lonlat[0],
      lat: scenePosition.value.lonlat[1],
      height: scenePosition.value.height,
      angle: scenePosition.value.angle,
    }),
  };
  saveMapInfo(param)
    .then((res: any) => {
      saveFlag.value = false;
      if (res.code === 200) {
        useGlobalMessage('success', '保存成功！');
      } else {
        useGlobalMessage('error', '保存失败！');
      }
    })
    .catch(() => {
      saveFlag.value = false;
    });
};
// 改变场景位置
const changeCampusPosition = () => {
  const lgtd = scenePosition.value.lonlat[0];
  const latd = scenePosition.value.lonlat[1];
  const height = scenePosition.value.height;
  const angle = scenePosition.value.angle;
  const x = typeof lgtd === 'string' ? parseFloat(lgtd) : lgtd;
  const y = typeof latd === 'string' ? parseFloat(latd) : latd;
  if (window.campusMapManagerIns) {
    if (sceneStore.thingjsVersion === 1) {
      window.campusMapManagerIns.curCampus.position = CMAP.Util.convertLonlatToWorld([x, y], height);
      window.campusMapManagerIns.curCampus.worldAngles = CMAP.Util.getAnglesFromLonlat([x, y], angle);
    } else {
      window.campusMapManagerIns.curCampus.position = THING.EARTH.Utils.convertLonlatToWorld([x, y], height);
      window.campusMapManagerIns.curCampus.angles = THING.EARTH.Utils.getAnglesFromLonlat([x, y], angle);
    }
  }
};
// 显示场景控制器
const showSceneControl = ref(true);
// 处理园区调整器显隐
const toggleControl = (show: boolean) => {
  showSceneControl.value = show;
};
// 长按定时器
const pressTimer = ref();
/**
 * @description: 将长按的按钮信息储存起来
 */
const send = (type: string, ope: string) => {
  pressTimer.value = setInterval(() => {
    handleNum(type, ope, true);
  }, 100);
};
/**
 * @description: 处理数字加减逻辑
 * @param {String} type 类型
 * @param {String} ope + || -
 * @param {Boolean} drap true: 长按/false: 点击
 */
const handleNum = (type: string, ope: string, drap: boolean) => {
  if (type === 'height' || type === 'angle') {
    scenePosition.value[type] = ope === 'add' ? scenePosition.value[type] + (drap ? 0.5 * 5 : 0.5) : scenePosition.value[type] - (drap ? 0.5 * 5 : 0.5);
    if (type === 'angle' && scenePosition.value[type] >= 360) {
      scenePosition.value[type] = 0;
    } else if (type === 'angle' && scenePosition.value[type] <= -360) {
      scenePosition.value[type] = 0;
    }
  } else if (type === 'lon') {
    scenePosition.value.lonlat[0] =
      ope === 'add'
        ? Number((Number(scenePosition.value.lonlat[0]) + (drap ? 0.00001 * 5 : 0.00001)).toFixed(5))
        : Number((Number(scenePosition.value.lonlat[0]) - (drap ? 0.00001 * 5 : 0.00001)).toFixed(5));
  } else if (type === 'lat') {
    scenePosition.value.lonlat[1] =
      ope === 'add'
        ? Number((Number(scenePosition.value.lonlat[1]) + (drap ? 0.00001 * 5 : 0.00001)).toFixed(5))
        : Number((Number(scenePosition.value.lonlat[1]) - (drap ? 0.00001 * 5 : 0.00001)).toFixed(5));
  }
  // 改变位置
  changeCampusPosition();
};
/**
 * @description: 取消长按定时器
 */
const cancelOpe = () => {
  if (pressTimer.value) {
    clearInterval(pressTimer.value);
  }
  pressTimer.value = null;
};
// 右侧content展示控制
const rightContentShow = ref(true);
// 切换右侧内容展示
const changeRightContentShow = () => {
  rightContentShow.value = !rightContentShow.value;
};
// 右侧面板宽度
const rightWidth = ref(330);
// 拖拽flag
const dragFlag = ref(false);
// 初始化右侧拖拽条事件
const initRightDrag = () => {
  const dragBar = document.getElementById('map-drag-bar');
  const rightContent = document.getElementById('map-right-content-wrap');
  const previewPanel = document.getElementById('map-preview-content-panel');
  if (!dragBar) {
    return;
  }
  dragBar.onmousedown = (e) => {
    e.stopPropagation();
    // 处理拖拽过程中选中文字问题
    document.onselectstart = function () {
      return false;
    };
    const startX = e.clientX;
    const startWidth = rightWidth.value;
    let width = 0;
    rightContent!.style.transitionDuration = '0s';
    previewPanel!.style.pointerEvents = 'all';
    document.onmousemove = (e1) => {
      dragFlag.value = true;
      const endX = e1.clientX;
      width = startX - endX + startWidth;
      if (width < 330) {
        width = 330;
      }
      if (width > 690) {
        // 超过最大高度不能往下再拖
        width = 690;
      }
      rightWidth.value = width;
    };
    document.onmouseup = () => {
      e.stopPropagation();
      dragFlag.value = false;
      rightContent!.style.transitionDuration = '0.3s';
      previewPanel!.style.pointerEvents = 'none';
      if (width !== 0) {
        document.onmousemove = null;
        document.onmouseup = null;
        document.onselectstart = null;
        rightWidth.value = width;
      }
    };
  };
};
// 加载loading
let timer: any = null;
const loading = ref(true);
const loadingPercent = ref(0);

const mapTabs = ref<any>([]); // 底图列表
const activeKey = ref(0);
const currentTab = ref<any>({}); // 当前选中的底图
// 切换底图
const changeTab = (val: any) => {
  activeKey.value = val;
  currentTab.value = mapTabs.value[activeKey.value];
  if (sceneStore.thingjsVersion === 1) {
    window.mapManagerIns.changeTile({
      tileLayerUrl: mapTabs.value[activeKey.value]?.tilesUrl, // 瓦片地址
      maximumLevel: 18, // 最大渲染层级 默认18
    });
  } else if (sceneStore.thingjsVersion === 2) {
    window.mapManagerIns.changeTile({
      url: mapTabs.value[activeKey.value]?.tilesUrl, // 瓦片地址
      name: mapTabs.value[activeKey.value]?.name,
      maximumLevel: 18, // 最大渲染层级 默认18
    });
  }
};
// 获取底图列表
const getMapList = () => {
  getBaseMapList({}).then(async (res) => {
    if (res.success) {
      if (!res.data || !res.data.length) {
        useGlobalMessage('error', '请添加地图瓦片服务');
        return;
      }
      mapTabs.value = res.data.filter((item: any) => item.type === 0);
      activeKey.value = 0;
      currentTab.value = mapTabs.value[0];
      // 加载默认地图服务
      initMap();
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
// 获取名称和uuid
const sceneName = ref();
const sceneUuid = ref();
const { query } = useRoute();
const { uuid, name } = query;
// 保存场景名称
sceneName.value = name;
sceneUuid.value = uuid;
// 场景信息
const sceneData = ref();
// 场景位置
const scenePosition = computed(() => sceneStore.scenePosition);
// 获取主场景信息
const getMainSceneMsg = () => {
  if (!sceneUuid.value) {
    return;
  }
  getSceneRecord({ uuid: sceneUuid.value }).then((res: any) => {
    if (res.code === 200) {
      sceneData.value = res.data;
      const { mapInfo } = res.data;
      if (mapInfo) {
        const { lng, lat, height, angle } = JSON.parse(mapInfo);
        sceneStore.scenePosition = {
          lonlat: [lng || 116.416357, lat || 39.928353],
          height: height || 1,
          angle: angle || 0,
        };
      } else {
        sceneStore.scenePosition = {
          lonlat: [116.416357, 39.928353],
          height: 1,
          angle: 0,
        };
      }
      // 加载场景
      loadCampus();
    }
  });
};
// 加载场景
const loadCampus = () => {
  window.campusMapManagerIns = CampusManager({
    app: window.app, // THINGJS实例
    sceneCode: sceneData.value.sceneCode,
    flyToSetting: false, // 园区，建筑，楼层默认飞到设置视角 - 默认true
    initChildScene: false, // 加载主场景时是否加载子场景 默认false
    lon: scenePosition.value.lonlat[0],
    lat: scenePosition.value.lonlat[1],
    height: scenePosition.value.height,
    angle: scenePosition.value.angle,
    sceneLoaded: () => {
      // 隐藏loading
      timer && clearInterval(timer);
      loadingPercent.value = 100;
      setTimeout(() => {
        loading.value = false;
        window.app.level.change(window.campusMapManagerIns.curCampus);
        window.app.camera.earthFlyTo({
          lonlat: scenePosition.value.lonlat,
          time: 3000,
          height: 1000,
        });
      }, 200);
    },
  });
};
// 地球是否加载完毕
const mapLoaded = ref(false);
// 初始化地球
const initMap = () => {
  loadDepend(() => {
    createMap();
  });
};
// 加载依赖
const loadDepend = (cb: Function) => {
  loadJsFiles(() => {
    cb && cb();
  }, ['thingjs', 'uearth', 'thing.campus']);
};
// 创建地球
const createMap = async () => {
  loadProgress();
  //  初始化app实例
  const app = await initApp('earthOutContainer');
  window.app = app;
  // 创建地球
  window.mapManagerIns = MapManager({
    app, // THINGJS实例
    tileLayerUrl: mapTabs.value[activeKey.value]?.tilesUrl,
    leftInteractive: false,
    mapLoaded: () => {
      setTimeout(() => {
        mapLoaded.value = true;
        window.mapManagerIns.map.attribution = 'none';
        // 实时更新经纬度
        window.app.on(THING.EventType.MouseMove, (ev: any) => {
          const position = ev.pickedPosition ? ev.pickedPosition : sceneStore.thingjsVersion === 1 ? window.app.camera.screenToWorld(ev.x, ev.y) : window.app.camera.screenToWorld([ev.x, ev.y]);
          // @ts-ignore
          const pos = sceneStore.thingjsVersion === 1 ? CMAP.Util.convertWorldToLonlat(position) : THING.EARTH.Utils.convertWorldToLonlat(position);
          const [lng, lat] = pos;
          currentPos.lat = lat;
          currentPos.lng = lng;
        });
        // 获取园区场景信息
        getMainSceneMsg();
      }, 200);
    },
  });
};
// 初始化加载进度监听
const loadProgress = () => {
  timer = setInterval(() => {
    if (loadingPercent.value < 99) {
      loadingPercent.value += 1;
    }
  }, 5);
};
// 保存默认视角
const saveDefaultPosition = () => {
  const params = {
    position: window.app.camera.position,
    target: window.app.camera.target,
  };
  changeAllMapDefaultCamInfo(params).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '默认视角保存成功');
    }
  });
};
// 位置搜索
const SearchText = ref();
const SearchClear = ref();
// 处理清空按钮显示隐藏
const setInputStyle = () => {
  if (SearchText.value.value) {
    SearchClear.value.style.display = 'block';
  } else {
    SearchClear.value.style.display = 'none';
  }
};
// 清空搜索
const clearSearch = () => {
  SearchText.value.value = '';
  if (SearchClear.value) {
    SearchClear.value.style.display = 'none';
  }
};
// 清空搜索
const clearTSearch = () => {
  place.value = '';
  suggests.value = [];
};
// 加载高德地图依赖
const hasGdkey = ref();
const checkGdKey = () => {
  if (!hasGdkey.value) {
    useGlobalMessage('error', '请在开发配置中配置高德地图1.4.15版本JSAPI密钥');
  }
};
const loadAmapScript = () => {
  if (!document.getElementsByClassName('AMapScript').length) {
    getSysConfigInfo(['AMAP_JSAPI_KEY']).then((res) => {
      if (res.success && res.data?.[0] && res.data?.[0] !== '无') {
        hasGdkey.value = res.data[0];
        loadScript(
          `https://webapi.amap.com/maps?v=1.4.15&key=${res.data[0]}&plugin=AMap.MapType,AMap.Autocomplete,AMap.PlaceSearch,AMap.Geocoder`,
          () => {
            nextTick(() => {
              initAMapSearch();
            });
          },
          'AMapScript'
        );
      }
    });
  }
};
// 加载天地地图依赖
const hasTdkey = ref();
const checkTdKey = () => {
  if (!hasTdkey.value) {
    useGlobalMessage('error', '请在开发配置中配置天地地图密钥');
  }
};
const loadTmapScript = () => {
  if (!document.getElementsByClassName('TMapScript').length) {
    getSysConfigInfo(['TIAN_DI_TU_MI_YAO']).then((res) => {
      if (res.success && res.data?.[0] && res.data?.[0] !== '无') {
        hasTdkey.value = res.data[0];
        loadScript(
          `http://api.tianditu.gov.cn/api?v=4.0&tk=${res.data[0]}`,
          () => {
            nextTick(() => {
              initTMapSearch();
            });
          },
          'TMapScript'
        );
      }
    });
  }
};
// 初始化地图自动提示-高德
const initAMapSearch = () => {
  const auto = new window.AMap.Autocomplete({
    input: 'suggestId',
  });
  window.AMap.event.addListener(auto, 'select', (e: any) => {
    SearchClear.value.style.display = 'block';
    if (e.poi.location) {
      const { lng, lat } = e.poi.location;
      let pos = [lng, lat];
      if (currentTab.value.coords === 'WGS84') {
        pos = coordtransform.gcj02towgs84(lng, lat);
      }
      window.app.camera.earthFlyTo({
        lonlat: pos,
        time: 3000,
        height: 1000,
      });
    } else {
      useGlobalMessage('warning', '未找到位置');
    }
  });
};
// 天地图提示
const suggests = ref([]);
let localsearch = ref(null);
// 初始化地图自动提示-天地图
const initTMapSearch = () => {
  // @ts-ignore
  const map = new T.Map('map-div', {
    projection: 'EPSG:4326',
  });
  console.log('map', map);
  // @ts-ignore
  map.centerAndZoom(new T.LngLat(116.40769, 39.89945), 12);
  // 搜素处理
  //创建搜索对象
  const config = {
    pageCapacity: 10, //每页显示的数量
    onSearchComplete: mapSearch, //接收数据的回调函数
  };
  // @ts-ignore
  localsearch.value = new T.LocalSearch(map, config);
};
// 地图搜索
const mapSearch = (obj: any) => {
  suggests.value = obj.suggests || [];
};
// 键盘弹起搜索
const place = ref('');
const SearchTClear = ref();
const handleSearch = (e: any) => {
  if (place.value) {
    SearchTClear.value.style.display = 'block';
    localsearch.value.search(place.value, 4);
  } else {
    SearchTClear.value.style.display = 'none';
    suggests.value = [];
  }
};
// 定位
const location = (item: any) => {
  const lnglat = item.lonlat.split(',');
  let pos = lnglat;
  if (currentTab.value.coords === 'GCJ02') {
    pos = coordtransform.wgs84togcj02(lnglat[0], lnglat[1]);
  }
  window.app.camera.earthFlyTo({
    lonlat: pos,
    time: 3000,
    height: 1000,
  });
};
onMounted(() => {
  getMapList();
  initRightDrag();
  loadTmapScript();
});
</script>
<style scoped lang="scss">
.map-preview.keep-px {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .tabs-list {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-width: 1200px;
    height: 55px;
    background: var(--second-bg-color);
    border-bottom: 1px solid var(--header-border-color);
    outline: none;

    .campus-name {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      width: 280px;
      min-width: 280px;
      height: 55px;
      padding: 0 0 0 24px;
      overflow: hidden;

      .header-logo {
        width: auto;
        height: 26px;
        margin-right: 8px;
      }

      .header-title {
        flex: 1;
        height: 55px;
        overflow: hidden;
        font-family: 'PingFang Bold';
        font-size: 16px;
        font-weight: 500;
        line-height: 55px;
        color: var(--primary-text-color);
      }
    }

    .map-list {
      flex: 1;
      margin-left: -280px;
    }
  }

  .map-container {
    position: relative;
    width: 100%;
    height: calc(100% - 55px);
    overflow: hidden;

    #map-div {
      position: absolute;
      top: -100px;
      width: 100px;
      height: 100px;
    }

    .map-toggle {
      position: absolute;
      top: 24px;
      left: 24px;
      z-index: 999;

      ::v-deep(.ant-select-selector, .ant-select-selection-item) {
        height: 38px;
        font-size: 16px;
        line-height: 38px;
        color: #333;
      }

      ::v-deep(.ant-select-single .ant-select-selector .ant-select-selection-item) {
        font-size: 16px;
        line-height: 36px;
        color: #333;
      }
    }

    .map-search {
      position: absolute;
      top: 24px;
      left: 120px;
      z-index: 100;
      pointer-events: all;

      .search {
        position: relative;

        input {
          position: relative;
          box-sizing: border-box;
          width: 300px;
          height: 38px;
          padding: 9px;
          font-size: 16px;
          color: #333;
          border: 1px solid #f1f1f1;
          border-radius: 4px;
          outline: none;
          box-shadow: 0 0 15px #c7c6c6;
        }

        .search-clear {
          position: absolute;
          top: 50%;
          right: 8px;
          display: none;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: #d4d2d2;
          border-radius: 50%;
          transform: translateY(-50%);

          &::after,
          &::before {
            position: absolute;
            top: 50%;
            left: 50%;
            display: block;
            width: 14px;
            height: 2px;
            margin-top: -1px;
            margin-left: -7px;
            content: '';
            background: #fff;
            border-radius: 1px;
          }

          &::after {
            transform: scale(0.7) rotate(45deg);
          }

          &::before {
            transform: scale(0.7) rotate(-45deg);
          }
        }

        .suggest-list {
          position: absolute;
          top: 44px;
          left: 0;
          z-index: 999;
          box-sizing: border-box;
          width: 300px;
          max-height: 200px;
          padding: 5px 0;
          overflow: hidden auto;
          background-color: #fff;

          .suggest-item {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            width: 300px;
            padding: 2px 10px;
            color: #666;
            cursor: pointer;

            .name {
              margin-right: 10px;
              color: #000;
            }
          }
        }
      }
    }

    .earth-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .mouse-pos {
      position: absolute;
      bottom: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 35px;

      .pos-wrappper {
        display: flex;
        align-items: center;
        padding: 5px 10px;
        background: var(--primary-bg-color);
        border-radius: 5px;

        span {
          margin-right: 10px;

          &:last-child {
            margin-top: 1px;
            margin-right: 0;
            cursor: pointer;
          }
        }
      }
    }

    .preview-content-panel {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 100;
      display: flex;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .right-content {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        pointer-events: all;
        user-select: none;

        .right-content-wrap {
          width: 330px;
          height: 100%;
          background: var(--primary-bg-color);
          border-left: 1px solid var(--header-border-color);
          transition: all 0.3s;
        }

        .right-content-expand.keep-px {
          position: absolute;
          top: 50%;
          right: 100%;
          z-index: 999;
          display: flex;
          place-items: center center;
          width: 16px;
          height: 128px;
          cursor: pointer;
          background: var(--theme-color);
          border-radius: 8px 0 0 8px;
          transform: translateY(-50%);

          .right-content-hand {
            width: 0;
            height: 0;
            margin-left: 5px;
            border-top: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 5px solid #fefefe;

            &.hide {
              margin-left: 0;
              border-right: 5px solid #fefefe;
              border-left: 5px solid transparent;
            }
          }
        }

        .drag-bar {
          position: absolute;
          top: 0;
          left: 0;
          width: 10px;
          height: 100%;
          cursor: col-resize;
        }
      }
    }

    .map-scene-control {
      position: absolute;
      bottom: 33px;
      transition: all 0.3s;

      .position-control {
        position: relative;
        width: 120px;
        height: 120px;
        margin-bottom: 11px;
        background: url('@/assets/img/scene/position-bg.png') no-repeat;
        background-size: 100% 100%;

        .top {
          position: absolute;
          top: 6px;
          left: 50px;
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/top.png') no-repeat;
          mask-size: 100% 100%;
        }

        .right {
          position: absolute;
          top: 50px;
          left: 96px;
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/right.png') no-repeat;
          mask-size: 100% 100%;
        }

        .down {
          position: absolute;
          top: 99px;
          left: 50px;
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/down.png') no-repeat;
          mask-size: 100% 100%;
        }

        .left {
          position: absolute;
          top: 50px;
          left: 5px;
          width: 18px;
          height: 18px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/left.png') no-repeat;
          mask-size: 100% 100%;
        }
      }

      .angle-control {
        position: relative;
        width: 120px;
        height: 34px;
        margin-bottom: 11px;
        line-height: 34px;
        color: #333;
        text-align: center;
        background: url('@/assets/img/scene/second-bg.png') no-repeat;
        background-size: 100% 100%;

        .sub {
          position: absolute;
          top: 9px;
          left: 11px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/sub.png') no-repeat;
          mask-size: 100% 100%;
        }

        .add {
          position: absolute;
          top: 9px;
          left: 93px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/add.png') no-repeat;
          mask-size: 100% 100%;
        }
      }

      .height-control {
        position: relative;
        width: 120px;
        height: 34px;
        margin-bottom: 11px;
        line-height: 34px;
        color: #333;
        text-align: center;
        background: url('@/assets/img/scene/second-bg.png') no-repeat;
        background-size: 100% 100%;

        .sub {
          position: absolute;
          top: 9px;
          left: 11px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/sub.png') no-repeat;
          mask-size: 100% 100%;
        }

        .add {
          position: absolute;
          top: 9px;
          left: 93px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/add.png') no-repeat;
          mask-size: 100% 100%;
        }
      }

      .function-control {
        position: relative;
        width: 120px;
        height: 34px;
        margin-bottom: 11px;
        color: #333;
        background: url('@/assets/img/scene/second-bg.png') no-repeat;
        background-size: 100% 100%;

        .location {
          position: absolute;
          top: 9px;
          left: 11px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/location.png') no-repeat;
          mask-size: 100% 100%;
        }

        .save {
          position: absolute;
          top: 9px;
          left: 93px;
          width: 16px;
          height: 16px;
          cursor: pointer;
          background: var(--theme-color);
          background-size: 100% 100%;
          mask: url('@/assets/img/scene/save.png') no-repeat;
          mask-size: 100% 100%;
        }
      }
    }
  }
}

:deep .ant-tabs-nav {
  margin: 0 !important;

  .ant-tabs-nav-wrap {
    justify-content: center;
    height: 55px;
    min-height: 55px;
    max-height: 55px;
  }
}
</style>
