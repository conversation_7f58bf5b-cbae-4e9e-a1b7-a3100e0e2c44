package com.uino.x.pedestal.twin.impl;


import apijson.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.uino.x.common.core.factory.PageFactory;
import com.uino.x.common.core.util.TenantUtils;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.enums.BoolEnum;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.label.enums.YesOrNotEnum;
import com.uino.x.common.label.enums.exp.ExpEnumOption;
import com.uino.x.common.pojo.entity.BaseEntity;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.tool.base.*;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.alarm.dao.mapper.DeviceAlertMapper;
import com.uino.x.pedestal.bubble.api.SysBubbleInfoApi;
import com.uino.x.pedestal.bubble.dao.mapper.SysBubbleInfoMapper;
import com.uino.x.pedestal.bubble.pojo.entity.SysBubbleInfo;
import com.uino.x.pedestal.bubble.pojo.vo.SysBubbleInfoVo;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.dict.api.SysDictDataApi;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.api.SysAppApi;
import com.uino.x.pedestal.identity.api.SysMenuApi;
import com.uino.x.pedestal.identity.common.enums.MenuOpenTypeEnum;
import com.uino.x.pedestal.identity.common.enums.MenuSpecialEnum;
import com.uino.x.pedestal.identity.common.enums.MenuTypeEnum;
import com.uino.x.pedestal.identity.common.enums.MenuWeightEnum;
import com.uino.x.pedestal.identity.common.enums.exp.SysAppExpEnum;
import com.uino.x.pedestal.identity.pojo.entity.SysApp;
import com.uino.x.pedestal.identity.pojo.entity.SysMenu;
import com.uino.x.pedestal.identity.pojo.param.SysMenuParam;
import com.uino.x.pedestal.model.api.ThingsModelsApi;
import com.uino.x.pedestal.model.pojo.entity.ThingsModel;
import com.uino.x.pedestal.performance.dao.mapper.DevicePerformanceHistoryMapper;
import com.uino.x.pedestal.performance.dao.mapper.DevicePerformanceMapper;
import com.uino.x.pedestal.twin.api.service.TwinClassService;
import com.uino.x.pedestal.twin.api.service.TwinCoalesceService;
import com.uino.x.pedestal.twin.common.constant.PermissionConstant;
import com.uino.x.pedestal.twin.common.constant.TwinDictCodeConstant;
import com.uino.x.pedestal.twin.common.constant.TwinFixedColumnConstant;
import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.common.enums.TwinClassDataTypeEnum;
import com.uino.x.pedestal.twin.common.enums.TwinClassLevelEnum;
import com.uino.x.pedestal.twin.common.enums.TwinTreeNodeTypeEnum;
import com.uino.x.pedestal.twin.common.exception.TwinClassException;
import com.uino.x.pedestal.twin.common.exception.TwinServiceException;
import com.uino.x.pedestal.twin.common.utils.ColumnUtils;
import com.uino.x.pedestal.twin.common.utils.FormUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.dao.mapper.*;
import com.uino.x.pedestal.twin.impl.ddl.AlterJrmDdl;
import com.uino.x.pedestal.twin.impl.ddl.CreateIndexJrmDdl;
import com.uino.x.pedestal.twin.impl.ddl.CreateJrmDdl;
import com.uino.x.pedestal.twin.impl.ddl.DropIndexJrmDdl;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.pojo.domain.*;
import com.uino.x.pedestal.twin.pojo.entity.*;
import com.uino.x.pedestal.twin.pojo.param.TwinClassInfoParam;
import com.uino.x.pedestal.twin.pojo.param.TwinClassListParam;
import com.uino.x.pedestal.twin.pojo.param.TwinClassPageDataTotalParam;
import com.uino.x.pedestal.twin.pojo.param.TwinClassParam;
import com.uino.x.pedestal.twin.pojo.vo.TwinClassFieldVo;
import com.uino.x.pedestal.twin.pojo.vo.TwinClassVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 孪生体分类service实现
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/8/23 17:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TwinClassServiceImpl extends ServiceImpl<TwinClassMapper, TwinClass> implements TwinClassService {
    /**
     * 孪生对象关联属性
     */
    public static final String TWIN_TYPE_RELEATIVE = "releative";
    /**
     * 孪生对象数据类型字典编码
     */
    private static final String TWIN_CLASS_DATA_TYPE = "TWIN_CLASS_DATA_TYPE";
    /**
     * 孪生对象所在层级字典编码
     */
    private static final String TWIN_CLASS_LEVEL = "TWIN_CLASS_LEVEL";
    /**
     * 孪生对象排除编码
     */
    private static final String TWIN_CLASS_EXCLUDE_CODE = "ABSTRACT";
    private static final long TWIN_MENU_APP_ID = 1475772003431485441L;
    private static final long TWIN_MENU_PID = 0L;
    private static final String TWIN_MENU_ROUTER = "/TWIN-";
    private static final String TWIN_MENU_COMPONENT = "twinData";
    private static final int TWIN_MENU_SORT = 1500;
    private static final long TWIN_MENU_CATEGORY_ID = 1650056377098895362L;

    private final SysBubbleInfoMapper sysBubbleInfoMapper;
    private final SysBubbleInfoApi sysBubbleInfoApi;
    private final TwinCoalesceMapper twinCoalesceMapper;
    private final ThingsModelsApi thingsModelsApi;
    private final SysAppApi sysAppApi;
    private final SysMenuApi sysMenuApi;
    private final TwinClassGroupMapper twinClassGroupMapper;
    private final TwinXxvMapper twinXxvMapper;
    private final DeviceAlertMapper deviceAlertMapper;
    private final DevicePerformanceMapper devicePerformanceMapper;
    private final DevicePerformanceHistoryMapper devicePerformanceHistoryMapper;
    private final TwinCoalesceMapMapper twinCoalesceMapMapper;
    private final TwinCoalesceService twinCoalesceService;
    private final TwinClassModelMappingMapper twinClassModelMappingMapper;
    private final TwinBodyDataMapper twinBodyDataMapper;
    private final TwinClassMapper twinClassMapper;
    private final TwinPermissionMapper twinPermissionMapper;

    private final SysDictDataApi sysDictDataApi;

    // 添加权限缓存，用于同一请求中的权限数据复用
    private final ThreadLocal<Map<String, List<TwinPermission>>> permissionCache = ThreadLocal.withInitial(HashMap::new);

    // 添加字典数据缓存，避免重复查询
    private final ThreadLocal<Map<String, Map<String, String>>> dictCache = ThreadLocal.withInitial(HashMap::new);

    // 添加用户角色缓存，避免重复获取
    private final ThreadLocal<List<String>> userRoleCache = ThreadLocal.withInitial(ArrayList::new);

    // 预定义的空JSON字符串，避免重复创建
    private static final String EMPTY_COLUMN_JSON = "{\"column\":[]}";
    private static final String EMPTY_LIST_JSON = "{\"list\":[]}";
    // 预定义的空集合，避免重复创建
    private static final Set<String> EMPTY_SET = Collections.emptySet();

    // 添加静态的 ObjectMapper 实例，避免重复创建
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 解析表结构到FormItems
     *
     * @param structure 表结构信息
     * @return FormItems FormItemList
     * @date 2022/7/21 16:38
     * <AUTHOR>
     */
    public static List<FormItem> parseFormItemsByStructure(String structure, JSONArray exclude) {

        final String modelKey = "model";
        try {
            JSONArray columns = JrmJsonUtils.getColumns(structure);
            Set<String> excludeModel = exclude.toJavaList(JSONObject.class).stream().map(e -> e.get(modelKey).toString()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(columns)) {
                throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "请添加字段映射"));
            }
            return columns.toJavaList(JSONObject.class).stream()
                    .map(e -> FormItem.of(JSON.parseObject(e.toString(), Column.class)))
                    .filter(e -> !excludeModel.contains(e.getModel()))
                    .filter(e -> !StringUtils.isAnyEmpty(e.getModel(), e.getKey(), e.getLabel(), e.getType()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.toString());
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "表结构异常解析失败"));
        }
    }

    @Override
    public void modifyForm(TwinClassParam param) {
        // 获取表单信息
        String form = param.getForm();
        AssertUtils.isNotBlank(form, new TwinClassException(HttpStatus.BAD_REQUEST, "表单数据不能为空"));
        AssertUtils.isTrue(StringUtils.isJson(form), new TwinClassException(HttpStatus.BAD_REQUEST, "表单格式不正确"));
        // 查询出孪生体信息,取出表结构信息
        final TwinClass twinClass = this.baseMapper.selectById(param.getId());
        AssertUtils.isTrue(Objects.nonNull(twinClass), new TwinClassException(HttpStatus.BAD_REQUEST, "孪生体数据不存在"));
        final String structure = twinClass.getStructure();
        // 解析参数中的新form
        final List<FormItem> formItem = FormUtils.parseListToForm(form);
        // 创建表名
        final String tableName = createTableName(twinClass.getCode());

        AssertUtils.isNotBlank(tableName, new TwinClassException(HttpStatus.BAD_REQUEST, "编码不能为空"));

        // 校验孪生融合使用字段
        List<TwinCoalesceMap> twinCoalesceMaps = twinCoalesceMapMapper.selectList(new LambdaQueryWrapper<TwinCoalesceMap>()
                .eq(TwinCoalesceMap::getTwinCode, twinClass.getCode())
                .isNotNull(TwinCoalesceMap::getTwinModel)
                .eq(TwinCoalesceMap::getStatus, StatusEnum.ENABLE.getCode()));
        List<String> twinModels = twinCoalesceMaps.stream().map(TwinCoalesceMap::getTwinModel).collect(Collectors.toList());
        Set<Long> twinCoalesceIds = twinCoalesceMaps.stream().map(TwinCoalesceMap::getTwinCoalesceId).collect(Collectors.toSet());
        // 如果不为空进行校验
        if (!(CollectionUtils.isEmpty(twinModels) && CollectionUtils.isEmpty(twinCoalesceIds))) {

            String twinCoalesceNames = twinCoalesceService.list(new LambdaQueryWrapper<TwinCoalesce>()
                            .in(TwinCoalesce::getId, twinCoalesceIds)
                            .eq(TwinCoalesce::getStatus, StatusEnum.ENABLE.getCode()))
                    .stream().map(TwinCoalesce::getName).collect(Collectors.joining("，"));

            List<FormItem> oldFormItems = FormUtils.parseListToForm(twinClass.getForm());
            Map<String, String> oldCollect = oldFormItems.stream().filter(e -> twinModels.contains(e.getModel()))
                    .collect(Collectors.toMap(FormItem::getModel, FormItem::getType, (k1, k2) -> k1));
            Map<String, String> newCollect = formItem.stream().filter(e -> twinModels.contains(e.getModel()))
                    .collect(Collectors.toMap(FormItem::getModel, FormItem::getType, (k1, k2) -> k1));
            Map<String, String> oldCollectLabel = oldFormItems.stream().filter(e -> twinModels.contains(e.getModel()))
                    .collect(Collectors.toMap(FormItem::getModel, FormItem::getLabel, (k1, k2) -> k1));

            for (Map.Entry<String, String> next : oldCollect.entrySet()) {
                String newType = newCollect.get(next.getKey());
                AssertUtils.isNotNull(newType, new TwinClassException(HttpStatus.BAD_REQUEST, "【" + oldCollectLabel.get(next.getKey()) + "】字段已被【" + twinCoalesceNames + "】视图所关联，禁止删除或编辑数据字段"));
                AssertUtils.isTrue(newType.equals(next.getValue()), new TwinClassException(HttpStatus.BAD_REQUEST, "【" + oldCollectLabel.get(next.getKey()) + "】字段已被【" + twinCoalesceNames + "】视图所关联，禁止删除或编辑数据字段"));
            }
        }

        // 校验模型映射字段
        final LambdaQueryWrapper<TwinClassModelMapping> wrapper = Wrappers.<TwinClassModelMapping>lambdaQuery()
                .eq(TwinClassModelMapping::getTwinClassId, twinClass.getId())
                .ne(BaseEntity::getStatus, StatusEnum.DELETED.getCode())
                .orderByAsc(TwinClassModelMapping::getOrderNum);
        final List<TwinClassModelMapping> modelMappings = twinClassModelMappingMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(modelMappings)) {
            final Set<String> fieldNames = modelMappings.stream().map(TwinClassModelMapping::getFieldName).collect(Collectors.toSet());

            List<FormItem> oldFormItems = FormUtils.parseListToForm(twinClass.getForm());
            final Map<String, FormItem> oldModelMap = oldFormItems.stream().filter(e -> fieldNames.contains(e.getModel()))
                    .collect(Collectors.toMap(FormItem::getModel, Function.identity(), (k1, k2) -> k1));

            final Map<String, FormItem> newModelMap = formItem.stream().filter(e -> fieldNames.contains(e.getModel()))
                    .collect(Collectors.toMap(FormItem::getModel, Function.identity(), (k1, k2) -> k1));

            oldModelMap.forEach((k, v) -> {
                final FormItem newModel = newModelMap.get(k);
                AssertUtils.isNotNull(newModel, new TwinClassException(HttpStatus.BAD_REQUEST, "【" + k + "】字段已被模型映射所关联，禁止删除或编辑数据字段"));
                AssertUtils.isTrue(Objects.equals(v.getType(), newModel.getType()), new TwinClassException(HttpStatus.BAD_REQUEST, "【" + k + "】字段已被模型映射所关联，禁止删除或编辑数据字段"));
            });
        }

        // 执行alter ddl
        final AlterJrmDdl alterJrmDdl = new AlterJrmDdl(AutoTableTypeEnum.TWIN, tableName, formItem, structure);
        alterJrmDdl.enableInvokePostProcess().parse();
        List<AlterColumn> deletedColumnList = alterJrmDdl.getDeletedColumnList();
        List<String> oldKeys = deletedColumnList.stream().map(AlterColumn::getOld).collect(Collectors.toList());
        modifyRelative(oldKeys);
        alterJrmDdl.invoke();
        // 处理字段索引
        processorColumnIndex(alterJrmDdl);

        // 根据情况排除添加对应的固定列
        String[] excludeColumnNames = new String[]{};
        if (TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId())) {
            // 如果为默认孪生体，排除所有的基础列
            excludeColumnNames = TwinFixedColumnConstant.ALL_COLUMNS;
            final String code = twinClass.getCode();

            if (Arrays.asList(TwinFixedColumnConstant.BUILDING_CODE, TwinFixedColumnConstant.FLOOR_CODE, TwinFixedColumnConstant.ROOM_CODE, TwinFixedColumnConstant.PARK_CODE).contains(code)) {
                // 如果是默认孪生体园区、建筑、楼层、房间，添加scene_code, campus_builder_id列
                alterJrmDdl.getNewColumnList().addAll(0, Arrays.asList(ColumnUtils.SCENE_CODE_COLUMN, ColumnUtils.USER_ID));
            } else if (Arrays.asList(TwinFixedColumnConstant.MAP_LEVEL, TwinFixedColumnConstant.MAP_VECTOR_DATA).contains(code)) {
                // 如果是默认孪生体地图层级、地图矢量数据，添加map_level_id列
                alterJrmDdl.getNewColumnList().addAll(0, Lists.newArrayList(ColumnUtils.MAP_LEVEL_ID_COLUMN));
            } else if (Objects.equals(TwinFixedColumnConstant.MENU, code)) {
                // 如果是默认孪生体菜单，添加menu_id列
                alterJrmDdl.getNewColumnList().addAll(0, Lists.newArrayList(ColumnUtils.MENU_ID_COLUMN));
            }
        } else if (Objects.equals(twinClass.getDataType(), TwinClassDataTypeEnum.FORM.getCode())) {
            // 如果为菜单类型的孪生体，排除所有的基础列
            excludeColumnNames = TwinFixedColumnConstant.ALL_COLUMNS;
        } else if (Objects.equals(twinClass.getLevel(), TwinClassLevelEnum.MAP.getCode())) {
            // 如果为地图类型的孪生体，排除地图不需要的列
            excludeColumnNames = TwinFixedColumnConstant.MAP_EXCLUDE_COLUMNS;
        }
        // 创建表结构
        final String tableStructure = JrmJsonUtils.createTableStructure(AutoTableTypeEnum.TWIN, tableName, alterJrmDdl.getNewColumnList(), excludeColumnNames);

        // 更新孪生体信息
        twinClass.setForm(form);
        twinClass.setStructure(tableStructure);

        if (!IdentityContext.isMe()) {
            LambdaQueryWrapper<TwinPermission> twinPermissionLambdaQueryWrapper = Wrappers.<TwinPermission>lambdaQuery();
            twinPermissionLambdaQueryWrapper.eq(TwinPermission::getTwinCode, twinClass.getCode());
            List<TwinPermission> twinPermissions = twinPermissionMapper.selectList(twinPermissionLambdaQueryWrapper);
            twinPermissions.forEach(e -> {
                e.setAddColumn(processFields(structure, e.getAddColumn(), tableStructure));
                e.setReadColumn(processFields(structure, e.getReadColumn(), tableStructure));
                e.setEditColumn(processFields(structure, e.getEditColumn(), tableStructure));
            });
        }
        this.baseMapper.updateById(twinClass);
    }

    private void processorColumnIndex(AlterJrmDdl alterJrmDdl) {
        final AutoTableTypeEnum autoTableType = alterJrmDdl.getAutoTableType();
        final String tableName = alterJrmDdl.getTableName();
        final List<FormItem> formItem = alterJrmDdl.getFormItem();
        final List<Column> newColumnList = alterJrmDdl.getNewColumnList();
        final Map<String, Column> oldCodeColumnMap = alterJrmDdl.getOldColumnList().stream()
                .collect(Collectors.toMap(Column::getCode, v -> v));
        final List<FormItem> createIndexFormItems = new ArrayList<>();
        final List<FormItem> dropIndexFormItems = new ArrayList<>();
        for (Column column : newColumnList) {
            final String code = column.getCode();
            final Column oldColumn = Optional.ofNullable(oldCodeColumnMap.get(code)).orElse(new Column());
            final String indexType = Optional.ofNullable(column.getIndexType()).orElse("");
            final String oldIndexType = Optional.ofNullable(oldColumn.getIndexType()).orElse("");
            // 新旧不一致说明需要操作索引
            if (!Objects.equals(indexType, oldIndexType)) {
                // 新的是空的意味需要删除索引
                if (StringUtils.isBlank(indexType)) {
                    dropIndexFormItems.add(FormItem.ofIndex(oldColumn.getName(), oldColumn.getCode(), oldIndexType));
                    continue;
                }
                // 旧是空的意味着需要新增索引
                if (StringUtils.isBlank(oldIndexType)) {
                    createIndexFormItems.add(FormItem.ofIndex(column.getName(), code, indexType));
                } else {
                    // 否则需要先删后增
                    dropIndexFormItems.add(FormItem.ofIndex(oldColumn.getName(), oldColumn.getCode(), oldIndexType));
                    createIndexFormItems.add(FormItem.ofIndex(column.getName(), code, indexType));
                }
            }
        }
        new DropIndexJrmDdl(autoTableType, tableName, dropIndexFormItems).parse().invoke();
        new CreateIndexJrmDdl(autoTableType, tableName, createIndexFormItems).parse().invoke();
    }

    private static String processFields(String jsonString1, String fields, String jsonString3) {
        List<String> fieldList = new ArrayList<>();
        Map<String, String> nameMapping = new HashMap<>();

        try {
            // 解析 String1 和 String3 的 JSON 数据
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode1 = objectMapper.readTree(jsonString1);
            JsonNode rootNode3 = objectMapper.readTree(jsonString3);

            // 获取 String1 中的所有字段名称
            JsonNode columnsNode1 = rootNode1.path("column");
            for (JsonNode column : columnsNode1) {
                String oldName = column.path("name").asText();
                nameMapping.put(oldName, oldName); // 初始化映射
            }

            // 获取 String3 中的字段名称，并更新映射
            JsonNode columnsNode3 = rootNode3.path("column");
            for (JsonNode column : columnsNode3) {
                String newName = column.path("name").asText();
                String oldName = nameMapping.keySet().stream()
                        .filter(key -> key.equals(newName) || key.equals(nameMapping.get(key)))
                        .findFirst()
                        .orElse(null);
                if (oldName != null) {
                    nameMapping.put(oldName, newName); // 更新映射
                }
            }

            // 拆分 String2 中的字段并检查是否存在
            String[] fieldArray = fields.split(",");
            for (String field : fieldArray) {
                String trimmedField = field.trim();
                String mappedField = nameMapping.get(trimmedField);
                if (mappedField != null) {
                    fieldList.add(mappedField); // 如果存在，则添加新的字段名
                }
            }
        } catch (Exception e) {
            e.printStackTrace(); // 处理异常
        }

        // 返回用逗号隔开的字符串
        return String.join(",", fieldList);
    }

    /**
     * 判断删除字段是否绑定了关联孪生体属性，如果存在删除绑定关系
     *
     * @param oldKeys 删除字段
     * <AUTHOR>
     * @date 2022/05/20 16:41:31
     */
    public void modifyRelative(List<String> oldKeys) {
        if (CollectionUtils.isEmpty(oldKeys)) {
            return;
        }
        LambdaQueryWrapper<TwinClass> twinClassLambdaQueryWrapper = new LambdaQueryWrapper<>();
        twinClassLambdaQueryWrapper.eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());
        twinClassLambdaQueryWrapper.like(TwinClass::getForm, TWIN_TYPE_RELEATIVE);
        List<TwinClass> twinClassList = this.list(twinClassLambdaQueryWrapper);
        for (TwinClass twinClass : twinClassList) {
            //判断form结果是否被修改
            AtomicBoolean formChanged = new AtomicBoolean(false);
            String form = twinClass.getForm();
            List<FormItem> formItems = FormUtils.parseListToForm(form);
            for (FormItem formItem : formItems) {
                FormTwins twins = formItem.getOptions().getTwins();
                if (Objects.nonNull(twins)) {
                    String oldTwinJson = JSON.toJSONString(twins);
                    List<String> props = twins.getProps();
                    if (props != null) {
                        props.removeAll(oldKeys);
                        String newTwinJson = JSON.toJSONString(twins);
                        form = form.replace(oldTwinJson, newTwinJson);
                        formChanged.set(true);
                    }
                }
            }
            if (formChanged.get()) {
                twinClass.setForm(form);
                this.baseMapper.updateById(twinClass);
            }
        }

    }

    @Override
    public TwinClassVo add(TwinClassParam param) {
        return createTwinClass(param, TwinFixedColumnConstant.UNIQUE_CODE_FORM);
    }

    /**
     * 创建孪生对象表名
     *
     * @param twinClassCode 孪生对象唯一编码
     * @return 表名
     * <AUTHOR>
     * @date 2021/8/25 11:14
     */
    private String createTableName(String twinClassCode) {

        return AutoTableTypeEnum.TWIN.getTablePrefix() + twinClassCode.toLowerCase();
    }

    @Override
    public TwinClassVo edit(TwinClassParam param) {
        // 校验参数
        checkParam(param, true);

        // 如果为默认孪生体，不能编辑
        if (TwinFixedColumnConstant.isDefaultTwin(param.getGroupId())) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "默认孪生对象不能编辑"));
        }

        TwinClass twinClass = Optional.ofNullable(getById(param.getId()))
                .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));
        int oldPure = twinClass.getPure();
        BeanUtils.copy(param, twinClass, true);

        if (Objects.nonNull(param.getThingsModelUuid())) {
            ThingsModel thingsModel = thingsModelsApi.getOneByModelId(param.getThingsModelUuid())
                    .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象所选模型不存在")));
            twinClass.setThingsModelUuid(thingsModel.getModelId());
        }

        // 不更新表单JSON
        twinClass.setForm(null);
        updateById(twinClass);

        // 如果为单独管理，编辑孪生体菜单
        editTwinMenu(oldPure, twinClass);

        return entityToVO(twinClass);
    }


    @Override
    public PageResult<TwinClassVo> page(TwinClassParam param) {
        long startTime = System.currentTimeMillis();
        long stepStartTime;

        try {
            // 步骤1: 创建查询条件
            stepStartTime = System.currentTimeMillis();
            LambdaQueryWrapper<TwinClass> queryWrapper = createQueryWrapper(param);
            // 只查询状态为 ENABLE 的 TwinClass
            queryWrapper.eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());
            log.warn("步骤1-创建查询条件耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤2: 获取满足条件的 TwinClass 的代码集合 - 进一步优化
            stepStartTime = System.currentTimeMillis();
            // 只查询真正需要的字段，减少数据传输
            queryWrapper.select(TwinClass::getCode, TwinClass::getId, TwinClass::getBubbleInfoId, TwinClass::getThingsModelUuid, TwinClass::getThingsModelId);
            // 减少查询数量限制
            queryWrapper.last("LIMIT 500");
            // 添加查询超时控制
            queryWrapper.last("/*+ MAX_EXECUTION_TIME(5000) */");

            List<TwinClass> twinClassList = list(queryWrapper);
            Set<String> twinClassCodes = twinClassList.stream()
                    .map(TwinClass::getCode)
                    .collect(Collectors.toSet());

            // 添加调试日志，检查thingsModelUuid
            log.debug("步骤2-查询到的TwinClass列表:");
            twinClassList.forEach(tc -> log.debug("TwinClass: id={}, code={}, thingsModelUuid={}, thingsModelId={}",
                    tc.getId(), tc.getCode(), tc.getThingsModelUuid(), tc.getThingsModelId()));

            // 检查thingsModelUuid的分布情况
            Map<String, Long> uuidCountMap = twinClassList.stream()
                    .map(TwinClass::getThingsModelUuid)
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(uuid -> uuid, Collectors.counting()));
            log.debug("步骤2-thingsModelUuid分布: {}", uuidCountMap);

            // 检查空值情况
            long nullUuidCount = twinClassList.stream()
                    .map(TwinClass::getThingsModelUuid)
                    .filter(Objects::isNull)
                    .count();
            log.debug("步骤2-thingsModelUuid为空的记录数: {}", nullUuidCount);

            log.warn("步骤2-获取TwinClass代码集合耗时: {}ms, 查询到{}个TwinClass",
                    System.currentTimeMillis() - stepStartTime, twinClassCodes.size());

            // 如果没有找到任何 TwinClass，返回空结果
            if (CollectionUtils.isEmpty(twinClassCodes)) {
                log.warn("未找到任何TwinClass，总耗时: {}ms", System.currentTimeMillis() - startTime);
                return new PageResult<>();
            }

            // 步骤3: 准备分页查询参数
            stepStartTime = System.currentTimeMillis();
            TwinClassPageDataTotalParam pageDataTotalParam = new TwinClassPageDataTotalParam();
            // 保持原始code值用于SQL查询
            pageDataTotalParam.setTwinClassCodes(twinClassCodes);
            // 设置表名列表用于information_schema查询
            Set<String> tableNames = twinClassCodes.stream()
                    .map(code -> "twin_" + code.toLowerCase())
                    .collect(Collectors.toSet());
            pageDataTotalParam.setTableNames(tableNames);
            // 设置排序字段和排序规则
            pageDataTotalParam.setSortField(Optional.ofNullable(param.getSortField()).map(StringUtils::lowerToUnderscore).orElse(null));
            pageDataTotalParam.setSortRule(Optional.ofNullable(param.getSortRule()).orElse("ASC"));
            log.warn("步骤3-准备分页查询参数耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤4: 执行分页查询 - 根据filterTotal参数决定是否统计总数
            stepStartTime = System.currentTimeMillis();
            IPage<TwinClassVo> page;
            boolean shouldCountTotal = param.getFilterTotal() == null || !param.getFilterTotal();

            if (shouldCountTotal) {
                // 需要统计总数
                final String tenant = TenantUtils.getTenantByRequest();
                String tableSchema = TenantConstant.MASTER.equals(tenant) ? DataSourceConstant.TWIN_X: tenant.toLowerCase() + StringConstant.UNDERSCORE + DataSourceConstant.TWIN_X;
                try {
                    // 优先使用优化的查询方法
                    page = twinClassMapper.pageDataTotalOptimizedNoCount(PageFactory.defaultPage(), pageDataTotalParam);

                    // 优先使用优化的查询方法
                    List<TwinDataTotal> twinDataTotals = twinClassMapper.sumDataTotalInformation(tableSchema);
                    if (CollectionUtils.isNotEmpty(page.getRecords()) &&  CollectionUtils.isNotEmpty(twinDataTotals)) {
                        Map<String, Long> twinGroup = twinDataTotals.stream().collect(Collectors.toMap(TwinDataTotal::getTableName, TwinDataTotal::getRowCount, (value1, value2) -> value2));
                        for (TwinClassVo twinClass:page.getRecords()){
                            long total = twinGroup.getOrDefault(String.format("twin_%s",twinClass.getCode().toLowerCase()),0L);
                            twinClass.setTotal(total);
                        }
                    }

                } catch (Exception e) {
                    log.warn("优化查询失败，使用备用查询方法: {}", e.getMessage());
                    // 降级到原始查询方法
                    page = twinClassMapper.pageDataTotal(PageFactory.defaultPage(), pageDataTotalParam);
                }
                log.warn("步骤4-执行分页查询(含总数统计)耗时: {}ms", System.currentTimeMillis() - stepStartTime);
            } else {
                // 不统计总数，使用性能更好的查询方法
                try {
                    // 优先使用优化的查询方法（不统计总数）
                    page = twinClassMapper.pageDataTotalOptimizedNoCount(PageFactory.defaultPage(), pageDataTotalParam);
                } catch (Exception e) {
                    log.warn("优化查询失败，使用备用查询方法(不统计总数): {}", e.getMessage());
                    // 降级到原始查询方法（不统计总数）
                    page = twinClassMapper.pageDataTotalNoCount(PageFactory.defaultPage(), pageDataTotalParam);
                }
                log.warn("步骤4-执行分页查询(不统计总数)耗时: {}ms", System.currentTimeMillis() - stepStartTime);
            }

            // 添加调试日志，检查分页查询结果
            log.debug("步骤4-分页查询返回的TwinClassVo列表:");
            page.getRecords().forEach(tc -> log.debug("TwinClassVo: id={}, code={}, thingsModelUuid={}, thingsModelId={}",
                    tc.getId(), tc.getCode(), tc.getThingsModelUuid(), tc.getThingsModelId()));

            // 检查thingsModelUuid的分布情况
            Map<String, Long> pageUuidCountMap = page.getRecords().stream()
                    .map(TwinClassVo::getThingsModelUuid)
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(uuid -> uuid, Collectors.counting()));
            log.debug("步骤4-thingsModelUuid分布: {}", pageUuidCountMap);

            // 检查空值情况
            long pageNullUuidCount = page.getRecords().stream()
                    .map(TwinClassVo::getThingsModelUuid)
                    .filter(Objects::isNull)
                    .count();
            log.debug("步骤4-thingsModelUuid为空的记录数: {}", pageNullUuidCount);

            // 步骤5: 获取用户角色信息 - 优化：使用缓存
            stepStartTime = System.currentTimeMillis();
            // 获取当前用户的角色 ID - 使用缓存
            List<String> loginUserRoleIds = userRoleCache.get();
            if (loginUserRoleIds.isEmpty()) {
                loginUserRoleIds = IdentityContext.getLoginUserRoleIds();
                userRoleCache.set(loginUserRoleIds);
            }
            final List<String> finalLoginUserRoleIds = loginUserRoleIds;
            final boolean me = IdentityContext.isMe();
            log.warn("步骤5-获取用户角色信息耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤6: 处理权限并过滤表单和结构
            stepStartTime = System.currentTimeMillis();
            // 处理权限并过滤表单和结构 - 使用并行处理提高性能
            List<TwinClassVo> processedRecords = new ArrayList<>();
            if (Objects.isNull(param.getFilterForm()) || param.getFilterForm().equals(false)) {
                processedRecords = page.getRecords().stream()
                        .map(e -> processTwinClassOptimized(e, finalLoginUserRoleIds, me))
                        .collect(Collectors.toList());
            } else {
                processedRecords = page.getRecords();
            }
            page.setRecords(processedRecords);
            log.warn("步骤6-处理权限并过滤表单和结构耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤7: 获取气泡信息和模型信息的映射 - 进一步优化远程调用
            stepStartTime = System.currentTimeMillis();
            // 获取气泡信息和模型信息的映射 - 使用异步和缓存优化
            final List<TwinClassVo> records = page.getRecords();
            Map<Long, SysBubbleInfo> sysBubbleInfoMap;
            Map<String, ThingsModel> thingsModelMap;

            try {
                CompletableFuture<Map<Long, SysBubbleInfo>> bubbleInfoFuture = CompletableFuture.supplyAsync(() ->
                        getBubbleInfoMapOptimized(records)
                );

//                CompletableFuture<Map<String, ThingsModel>> thingsModelFuture = CompletableFuture.supplyAsync(() ->
//
//                );

                // 等待两个异步任务完成
                sysBubbleInfoMap = bubbleInfoFuture.get(10, TimeUnit.SECONDS);
                thingsModelMap = getThingsModelMapOptimized(records);

                log.debug("异步调用成功: sysBubbleInfoMap.size={}, thingsModelMap.size={}",
                        sysBubbleInfoMap.size(), thingsModelMap.size());
            } catch (Exception e) {
                log.warn("异步调用失败，降级到同步调用: {}", e.getMessage());
                // 降级到同步调用
                sysBubbleInfoMap = getBubbleInfoMapOptimized(records);
                thingsModelMap = getThingsModelMapOptimized(records);

                log.debug("同步调用结果: sysBubbleInfoMap.size={}, thingsModelMap.size={}",
                        sysBubbleInfoMap.size(), thingsModelMap.size());
            }

            log.warn("步骤7-获取气泡信息和模型信息映射耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤8: 获取字典数据 - 优化：使用缓存
            stepStartTime = System.currentTimeMillis();
            // 获取字典数据 - 使用缓存避免重复查询
            Map<String, String> twinClassLevelMap = getDictDataWithCache(TWIN_CLASS_LEVEL);
            Map<String, String> twinClassDataTypeMap = getDictDataWithCache(TWIN_CLASS_DATA_TYPE);
            log.warn("步骤8-获取字典数据耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 步骤9: 将记录转换为 VO 对象
            stepStartTime = System.currentTimeMillis();
            // 将记录转换为 VO 对象
            Map<Long, SysBubbleInfo> finalSysBubbleInfoMap = sysBubbleInfoMap;
            Map<String, ThingsModel> finalThingsModelMap = thingsModelMap;
            IPage<TwinClassVo> pageVo = page.convert(e -> {
                TwinClassVo vo = entityToVO(e, finalSysBubbleInfoMap, finalThingsModelMap, twinClassLevelMap, twinClassDataTypeMap);
                // 显式设置bubbleInfoId和thingsModelUuid，保证序列化
                vo.setBubbleInfoId(e.getBubbleInfoId());
                vo.setThingsModelUuid(e.getThingsModelUuid());
                vo.setThingsModelId(e.getThingsModelId());
                return vo;
            });
            log.warn("步骤9-记录转换为VO对象耗时: {}ms", System.currentTimeMillis() - stepStartTime);

            // 返回最终的分页结果
            log.warn("page方法总耗时: {}ms, 是否统计总数: {}", System.currentTimeMillis() - startTime, shouldCountTotal);
            return new PageResult<>(pageVo);
        } finally {
            // 清理线程本地缓存
            stepStartTime = System.currentTimeMillis();
            permissionCache.get().clear();
            dictCache.get().clear();
            userRoleCache.get().clear();
            log.warn("清理线程本地缓存耗时: {}ms", System.currentTimeMillis() - stepStartTime);
        }
    }

    /**
     * @param twinClass:
     * @param loginUserRoleIds: return TwinClassVo
     * <AUTHOR>
     * @date 2025/1/14
     * {@link TwinClassVo}
     * @description 处理孪生体数据
     */
    private TwinClassVo processTwinClass(TwinClassVo twinClass, List<String> loginUserRoleIds) {
        if (IdentityContext.isMe()) {
            twinClass.setAddForm(twinClass.getForm());
            twinClass.setEditForm(twinClass.getForm());
            twinClass.setReadForm(twinClass.getForm());

            twinClass.setAddStructure(twinClass.getStructure());
            twinClass.setEditStructure(twinClass.getStructure());
            twinClass.setReadStructure(twinClass.getStructure());
        } else {

            // 定义用于存储添加、编辑和读取权限的列集合
            Set<String> addColumns = new HashSet<>();
            Set<String> editColumns = new HashSet<>();
            Set<String> readColumns = new HashSet<>();

            // 获取用户的权限
            List<TwinPermission> permissions = getPermissionsByRoleIds(loginUserRoleIds, twinClass.getCode());
            permissions.forEach(permission -> {
                // 将权限中的列添加到对应的集合中
                String addColumn = permission.getAddColumn();
                String editColumn = permission.getEditColumn();
                String readColumn = permission.getReadColumn();

                if (addColumn != null && !addColumn.trim().isEmpty()) {
                    addColumns.addAll(Arrays.asList(addColumn.split(",")));
                }
                if (editColumn != null && !editColumn.trim().isEmpty()) {
                    editColumns.addAll(Arrays.asList(editColumn.split(",")));
                }
                if (readColumn != null && !readColumn.trim().isEmpty()) {
                    readColumns.addAll(Arrays.asList(readColumn.split(",")));
                }
            });

            // 过滤表单和结构
            try {
                // 记录调试信息
                log.debug("处理孪生对象权限过滤，twinClass code: {}, form: {}, structure: {}",
                        twinClass.getCode(),
                        twinClass.getForm() != null ? "已设置" : "null",
                        twinClass.getStructure() != null ? "已设置" : "null");

                // 过滤添加、编辑和读取表单
                twinClass.setAddForm(filterJsonBySet(twinClass.getForm(), addColumns, false));
                twinClass.setEditForm(filterJsonBySet(twinClass.getForm(), editColumns, false));
                twinClass.setReadForm(filterJsonBySet(twinClass.getForm(), readColumns, false));

                // 将默认列添加到添加权限的列集合中
                addColumns.addAll(PermissionConstant.DEFAULT_COLUMN);
                // 过滤添加、编辑和读取结构
                twinClass.setAddStructure(filterJsonBySet(twinClass.getStructure(), addColumns, true));
                twinClass.setEditStructure(filterJsonBySet(twinClass.getStructure(), editColumns, true));
                twinClass.setReadStructure(filterJsonBySet(twinClass.getStructure(), readColumns, true));
            } catch (JsonProcessingException ex) {
                // 处理 JSON 解析异常，记录详细错误信息
                log.error("处理孪生对象权限过滤时发生JSON解析错误，twinClass code: {}, form: {}, structure: {}",
                        twinClass.getCode(),
                        twinClass.getForm(),
                        twinClass.getStructure(), ex);
                throw new RuntimeException("JSON 处理错误: " + ex.getMessage(), ex);
            }
        }

        return twinClass; // 返回处理后的 TwinClassVo 对象
    }

    /**
     * @param roleIds:  角色Ids
     * @param twinCode: 孪生体Code
     *                  return List<TwinPermission>
     * <AUTHOR>
     * @date 2025/1/14
     * {@link List<TwinPermission>}
     * @description 根据权限id获取数据权限列
     */
    private List<TwinPermission> getPermissionsByRoleIds(List<String> roleIds, String twinCode) {
        // 创建查询条件以获取权限
        LambdaQueryWrapper<TwinPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TwinPermission::getRoleId, roleIds); // 根据角色 ID 查询
        queryWrapper.eq(TwinPermission::getTwinCode, twinCode);
        return twinPermissionMapper.selectList(queryWrapper); // 返回权限列表
    }

    /**
     * @param records: return Map<Long,SysBubbleInfo>
     * <AUTHOR>
     * @date 2025/1/14
     * {@link Map<Long,SysBubbleInfo>}
     * @description 填充气泡信息
     */
    private Map<Long, SysBubbleInfo> getBubbleInfoMap(List<TwinClassVo> records) {
        // 获取所有气泡信息的 ID
        final Set<Long> bubbleInfoIds = records.stream()
                .map(TwinClassVo::getBubbleInfoId)
                .collect(Collectors.toSet());

        // 如果没有气泡信息 ID，返回空映射
        return CollectionUtils.isEmpty(bubbleInfoIds) ? new HashMap<>() :
                sysBubbleInfoMapper.selectBatchIds(bubbleInfoIds).stream()
                        .collect(Collectors.toMap(SysBubbleInfo::getId, Function.identity(), (k1, k2) -> k1)); // 转换为 ID 到 SysBubbleInfo 的映射
    }

    /**
     * @param records: return Map<String,ThingsModel>
     * <AUTHOR>
     * @date 2025/1/14
     * {@link Map<String,ThingsModel>}
     * @description 填充模型信息
     */
    private Map<String, ThingsModel> getThingsModelMap(List<TwinClassVo> records) {
        // 获取所有模型的 UUID
        final Set<String> thingsModelUuids = records.stream()
                .map(TwinClassVo::getThingsModelUuid)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 如果没有模型 UUID，返回空映射
        return CollectionUtils.isEmpty(thingsModelUuids) ? new HashMap<>() :
                thingsModelsApi.getListByModelIds(thingsModelUuids).stream()
                        .collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1)); // 转换为模型 ID 到 ThingsModel 的映射
    }

    /**
     * @param jsonString:
     * @param filterSet:
     * @param isColumnFilter: return String
     * <AUTHOR>
     * @date 2025/1/14
     * {@link String}
     * @description 校验并填充数据权限过滤字段
     */
    private static String filterJsonBySet(String jsonString, Set<String> filterSet, boolean isColumnFilter) throws JsonProcessingException {
        // 添加 null 检查，如果 jsonString 为 null 或空，则返回空 JSON
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return isColumnFilter ? "{\"column\":[]}" : "{\"list\":[]}";
        }

        ObjectMapper objectMapper = new ObjectMapper(); // 创建 ObjectMapper 实例
        JsonNode rootNode = objectMapper.readTree(jsonString); // 解析 JSON 字符串
        ArrayNode filteredArray = objectMapper.createArrayNode(); // 创建一个新的 JSON 数组用于存储过滤后的结果

        // 根据标志选择要过滤的目标节点
        JsonNode targetNode = isColumnFilter ? rootNode.path("column") : rootNode.path("list");

        // 根据 filterSet 过滤目标节点
        targetNode.forEach(node -> {
            // 获取当前节点的名称
            String name = isColumnFilter ? node.path("name").asText() : node.path("model").asText();
            // 如果名称在过滤集合中，则将节点添加到过滤后的数组中
            if (filterSet.contains(name)) {
                filteredArray.add(node);
            }
        });

        // 更新原始 JSON，替换成过滤后的数组
        if (isColumnFilter) {
            ((ObjectNode) rootNode).set("column", filteredArray); // 更新列数据
        } else {
            ((ObjectNode) rootNode).set("list", filteredArray); // 更新列表数据
        }

        // 将更新后的 JSON 转换回字符串并返回
        return objectMapper.writeValueAsString(rootNode);
    }

    @Override
    public TwinClassVo detail(TwinClassParam param) {
        TwinClass twinClass = Optional.ofNullable(getById(param.getId()))
                .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        return entityToVO(twinClass);
    }

    @Override
    public void delete(TwinClassParam param) {
        TwinClass twinClass = Optional.ofNullable(getById(param.getId()))
                .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        // 如果为默认孪生体，不能删除
        if (TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId())) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "默认孪生对象不能删除"));
        }

        // 如果被其他孪生对象关联，不能删除
        final List<TwinClass> otherUsedList = getOtherUsed(twinClass.getCode());
        if (otherUsedList.size() > 0) {
            final String otherUsedNames = otherUsedList.stream()
                    .map(e -> "【" + e.getName() + "】")
                    .collect(Collectors.joining(","));
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "当前孪生对象已被孪生对象" + otherUsedNames + "关联，不能删除"));
        }

        // 如果关联了孪生融合，不能删除
        List<String> alreadyUseView = twinCoalesceMapMapper.countByTwinCodeAndStatus(twinClass.getCode(), StatusEnum.ENABLE.getCode());
        if (!CollectionUtils.isEmpty(alreadyUseView)) {
            String views = Joiner.on("，").join(alreadyUseView);
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, String.format("当前孪生对象已被【%s】孪生融合视图关联，不能删除", views)));
        }

        twinClass.setStatus(StatusEnum.DELETED.getCode());
        updateById(twinClass);

        // 如果为单独管理，删除孪生体菜单
        if (Objects.equals(BoolEnum.TRUE.getCode(), twinClass.getPure())) {
            deleteTwinMenu(twinClass);
        }

        LambdaQueryWrapper<TwinXxv> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TwinXxv::getTwinId, param.getId());
        if (twinXxvMapper.selectList(wrapper).size() > 0) {
            twinXxvMapper.delete(wrapper);
        }
        final String structure = twinClass.getStructure();
        // 将表名标识为删除 deleted_xxx
//        final String deleteTableName = StringUtils.merge(JrmJsonUtils.DELETED_FLAG_PREFIX, AutoTableTypeEnum.TWIN.getTablePrefix(), twinClass.getCode().toLowerCase(), StringConstant.UNDERSCORE, twinClass.getId());
//        final String alterRenameTable = JrmJsonUtils.createAlterRenameTable(structure, deleteTableName);
//        JrmBean.request(alterRenameTable, RequestType.ALTER, (jrm, sqlDefinition) -> jrm.setSqlExecutor(DefaultSqlExecutor.of(sqlDefinition)));
//
//        new DropIndexJrmDdl(AutoTableTypeEnum.TWIN, JrmJsonUtils.getTableName(structure), FormUtils.parseListToForm(twinClass.getForm()).stream().filter(fi -> StringUtils.isNotBlank(fi.getOptions().getIndexType())).toList())
//                .parse().invoke();
        // 直接删除表
        JrmBean.request(RequestType.DROP, structure);

        String twinCode = twinClass.getCode();
        // 孪生体删除后，相应的指标和告警都要删除
        if (StringUtils.isNotEmpty(twinCode)) {
            deviceAlertMapper.updateForLogic(twinCode, StatusEnum.DELETED.getCode());
            deviceAlertMapper.updateForLogicByLatest(twinCode, StatusEnum.DELETED.getCode());
            devicePerformanceMapper.updateForLogic(twinCode, StatusEnum.DELETED.getCode());
            devicePerformanceHistoryMapper.updateForLogic(twinCode, StatusEnum.DELETED.getCode());

            // youngBoss TODO: 删除孪生体时删除对应孪生体的数据权限
            LambdaQueryWrapper<TwinPermission> twinPermissionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            twinPermissionLambdaQueryWrapper.eq(TwinPermission::getTwinCode, twinCode);
            twinPermissionMapper.delete(twinPermissionLambdaQueryWrapper);
        }

        // 删除关联的模型映射
        final List<TwinClassModelMapping> modelMappings = twinClassModelMappingMapper.selectList(
                Wrappers.<TwinClassModelMapping>lambdaQuery()
                        .eq(TwinClassModelMapping::getTwinClassId, twinClass.getId())
                        .ne(BaseEntity::getStatus, StatusEnum.DELETED.getCode())
                        .orderByAsc(TwinClassModelMapping::getOrderNum)
        );
        if (!CollectionUtils.isEmpty(modelMappings)) {
            modelMappings.forEach(e -> {
                e.setStatus(StatusEnum.DELETED.getCode());
                twinClassModelMappingMapper.updateById(e);
            });
        }
    }

    @Override
    public List<TwinClass> getByGroupId(Long groupId) {
        LambdaQueryWrapper<TwinClass> queryWrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getGroupId, groupId)
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        return list(queryWrapper);
    }

    @Override
    public List<TwinClass> getListByParam(TwinClassListParam param) {
        LambdaQueryWrapper<TwinClass> twinClassWrapper = Wrappers.<TwinClass>lambdaQuery()
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode())
                .in(!CollectionUtils.isEmpty(param.getDataTypes()), TwinClass::getDataType, param.getDataTypes())
                .eq(StringUtils.isNotEmpty(param.getLevel()), TwinClass::getLevel, param.getLevel())
                .and(StringUtils.isNotEmpty(param.getKeyword()), q -> q.like(TwinClass::getName, param.getKeyword())
                        .or()
                        .like(TwinClass::getCode, param.getKeyword()))
                .in(!CollectionUtils.isEmpty(param.getIds()), TwinClass::getId, param.getIds())
                .in(!CollectionUtils.isEmpty(param.getTwinClassCodes()), TwinClass::getCode, param.getTwinClassCodes())
                .ne(!param.getHasDefault(), TwinClass::getGroupId, TwinFixedColumnConstant.DEFAULT_GROUP_ID)
                .notIn(!CollectionUtils.isEmpty(param.getExcludeTwinCode()), TwinClass::getCode, param.getExcludeTwinCode())
                .eq(Objects.nonNull(param.getGroupId()), TwinClass::getGroupId, param.getGroupId())
                .orderByAsc(BaseEntity::getCreateTime);
        return list(twinClassWrapper);
    }

    @Override
    public TwinClassTreeNode entityToTreeNode(TwinClass twinClass, SysBubbleInfoVo bubbleInfoVo, ThingsModel thingsModel) {
        TwinClassTreeNode treeNode = BeanUtils.copyToBean(twinClass, TwinClassTreeNode.class, true);
        treeNode.setPid(twinClass.getGroupId());
        treeNode.setTitle(twinClass.getName());
        treeNode.setType(TwinTreeNodeTypeEnum.CLASS.getCode());
        treeNode.setBubbleInfoVo(bubbleInfoVo);
        treeNode.setThingsModelType(Objects.nonNull(thingsModel) ? thingsModel.getType() : null);
        treeNode.setCustomColor(twinClass.getCustomColor());
        treeNode.setDefaultTwin(TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId()));

        return treeNode;
    }

    @Override
    public TwinClass findByCode(String code) {
        AssertUtils.isNotBlank(code, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象编码不能为空")));

        return getOne(Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getCode, code)
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode()));
    }

    @Override
    public List<Column> getTwinDataColumns(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象不能为空")));
        String structure = twinClass.getStructure();
        if (StringUtils.isBlank(structure)) {
            return new ArrayList<>();
        }
        List<? extends Column> columns = ColumnUtils.parseStructureToColumn(AutoTableTypeEnum.TWIN, structure, Column.class, false);

        return columns.stream().map(e -> BeanUtils.copyToBean(e, Column.class)).collect(Collectors.toList());
    }

    @Override
    public String getTwinDataTableName(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象不能为空")));

        String twinDataJson = twinClass.getStructure();

        if (StringUtil.isEmpty(twinDataJson)) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象未配置表单"));
        }

        final String tenant = TenantUtils.getTenantByRequest();
        final String table = JrmJsonUtils.getTableName(twinDataJson);
        return TenantConstant.MASTER.equals(tenant)
                ? DataSourceConstant.TWIN_X + StringConstant.DOT + table
                : tenant.toLowerCase() + StringConstant.UNDERSCORE + DataSourceConstant.TWIN_X + StringConstant.DOT + table;
    }

    @Override
    public Map<Long, TwinClass> getMapByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        return listByIds(ids).stream().collect(Collectors.toMap(TwinClass::getId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<TwinClass> getByBubbleInfoId(Long bubbleInfoId) {
        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getBubbleInfoId, bubbleInfoId)
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        return list(wrapper);
    }

    @Override
    public LinkedList<List<String>> buildTwinDataFixedHead(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象不能为空")));

        final List<Column> twinDataColumns = getTwinDataColumns(twinClass);
        final Map<String, String> twinDataColumnMap = twinDataColumns.stream().collect(Collectors.toMap(Column::getName, Column::getComment, (k1, k2) -> k1));
        final String dataType = twinClass.getDataType();
        final String twinLevel = twinClass.getLevel();
        final Long groupId = twinClass.getGroupId();
        LinkedList<List<String>> fixedHead = new LinkedList<>();
        //如果孪生体数据类型是表单类型，不添加固定表头
        if (TwinDictCodeConstant.FORM.equals(dataType)) {
            return fixedHead;
        }
        //如果是默认分组内的孪生体
        if (TwinDictCodeConstant.TWIN_CLASS_DEFAULT.equals(groupId)) {
            return fixedHead;
        }

        String dictCode = TwinDictCodeConstant.MAP.equals(twinLevel) ? TwinDictCodeConstant.TWIN_CLASS_MAP_COLUMN : TwinDictCodeConstant.TWIN_CLASS_PARK_COLUMN;
        Map<String, String> levelMap = sysDictDataApi.getDataMapByTypeCode(dictCode);
        if (Objects.equals(twinLevel, TwinClassLevelEnum.MAP.getCode())) {
            levelMap = sysDictDataApi.getDataMapByTypeCode(TwinDictCodeConstant.TWIN_CLASS_MAP_COLUMN);
        } else if (Objects.equals(twinLevel, TwinClassLevelEnum.PARK.getCode())) {
            levelMap = sysDictDataApi.getDataMapByTypeCode(TwinDictCodeConstant.TWIN_CLASS_PARK_COLUMN);
        }

        for (String key : levelMap.keySet()) {
            List<String> head = new ArrayList<>();
            head.add(key);
            head.add(twinDataColumnMap.get(key));
            fixedHead.add(head);
        }

        return fixedHead;
    }

    @Override
    public List<TwinClass> findListUsedDynamicKey(String dynamicKey) {
        AssertUtils.isNotBlank(dynamicKey, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "字典动态数据源key不能为空")));

        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode())
                .isNotNull(TwinClass::getForm)
                .like(TwinClass::getForm, dynamicKey);

        return list(wrapper);
    }

    @Override
    public void updateFormDynamicKeyOptions(List<TwinClass> twinClasses, String dynamicKey, JSONArray dynamicValue) {
        AssertUtils.isNotBlank(dynamicKey, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "dynamicKey不能为空")));
        AssertUtils.isNotNull(dynamicValue, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "dynamicValue不能为空")));
        if (CollectionUtils.isEmpty(twinClasses)) {
            return;
        }

        List<TwinClass> updatedTwinClasses = new ArrayList<>(twinClasses.size());
        for (TwinClass twinClass : twinClasses) {
            final String form = twinClass.getForm();
            // form为空，排除
            if (StringUtils.isBlank(form)) {
                continue;
            }

            // json对象为空，排除
            JSONObject formObject = null;
            try {
                formObject = JSONObject.parseObject(form);
            } catch (Exception e) {
                log.error("form解析失败", e);
            }
            if (CollectionUtils.isEmpty(formObject)) {
                continue;
            }

            // list数组为空，排除
            JSONArray listArray = null;
            try {
                listArray = formObject.getJSONArray("list");
            } catch (Exception e) {
                log.error("form/list解析失败", e);
            }
            if (CollectionUtils.isEmpty(listArray)) {
                continue;
            }

            // 遍历list
            boolean isUpdate = false;
            for (int i = 0; i < listArray.size(); i++) {

                // item为空，排除
                JSONObject item = null;
                try {
                    item = listArray.getJSONObject(i);
                } catch (Exception e) {
                    log.error("form/list/options解析失败", e);
                }
                if (CollectionUtils.isEmpty(item)) {
                    continue;
                }

                // options为空，排除
                JSONObject options = null;
                try {
                    options = item.getJSONObject("options");
                } catch (Exception e) {
                    log.error("form/list/options/options解析失败", e);
                }
                if (CollectionUtils.isEmpty(options)) {
                    continue;
                }

                if (Objects.equals(options.getString("dynamicKey"), dynamicKey)) {
                    options.put("options", dynamicValue.clone());
                    item.put("options", options);
                    listArray.set(i, item);
                    isUpdate = true;
                }
            }

            if (isUpdate) {
                formObject.put("list", listArray);
                twinClass.setForm(JSON.toJSONString(formObject, SerializerFeature.DisableCircularReferenceDetect));
                updatedTwinClasses.add(twinClass);
            }
        }

        if (!CollectionUtils.isEmpty(updatedTwinClasses)) {
            updateBatchById(updatedTwinClasses);
        }
    }

    @Override
    public TwinClassVo detailByCode(String code) {
        TwinClass twinClass = Optional.ofNullable(findByCode(code))
                .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        return entityToVO(twinClass);
    }

    @Override
    public JSONArray fieldDetailByCode(TwinClassInfoParam param) {
        TwinClass twinClass = Optional.ofNullable(findByCode(param.getCode()))
                .orElseThrow(() -> new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));
        String form = twinClass.getForm();
        JSONArray result = new JSONArray();
        if (org.apache.poi.util.StringUtil.isBlank(form)) {
            return result;
        }
        try {
            JSONObject formStruct = JSON.parseObject(form);
            JSONArray fields = formStruct.getJSONArray("list");
            if (CollectionUtils.isEmpty(param.getKeys())) {
                result = fields;
            } else {
                Set<String> keyList = param.getKeys();
                for (int i = 0; i < fields.size(); i++) {
                    JSONObject currentField = fields.getJSONObject(i);
                    String fieldName = currentField.getString("model");
                    if (StringUtils.isBlank(fieldName)) {
                        continue;
                    }
                    if (keyList.contains(fieldName)) {
                        result.add(currentField);
                    }
                }
            }

        } catch (Exception e) {
            log.error(String.format("获取{}孪生体的字段详情失败：%s", param.getCode()), e);
        }
        // 添加固定列
        if (param.isAddFixed()) {
            List<FormItem> formItems = parseFormItemsByStructure(twinClass.getStructure(), result);
            result.addAll(formItems);
        }
        return result;
    }

    @Override
    public TwinClassVo copy(TwinClassParam param) {
        String form = StringUtils.isNotBlank(param.getForm()) ? param.getForm() : TwinFixedColumnConstant.UNIQUE_CODE_FORM;
        return createTwinClass(param, form);
    }

    @Override
    public List<TwinClassFieldVo> getTwinPropertyFields(long twinClassId) {
        final TwinClass twinClass = Optional.ofNullable(getById(twinClassId))
                .orElseThrow(ThrowUtils.getThrowSupplier().badRequest("孪生对象不存在"));

        String structure = twinClass.getStructure();
        if (StringUtils.isBlank(structure)) {
            return Lists.newArrayList();
        }
        List<? extends Column> columns = ColumnUtils.parseStructureToColumn(AutoTableTypeEnum.TWIN, structure, Column.class, true);
        return columns.stream().map(TwinClassFieldVo::of).toList();
    }


    @Override
    public boolean checkTwinClassDataType(String twinCode, TwinClassDataTypeEnum dataType) {
        LambdaQueryWrapper<TwinClass> where = Wrappers.<TwinClass>lambdaQuery().eq(TwinClass::getCode, twinCode).eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());
        TwinClass twinClass = this.baseMapper.selectOne(where);
        if (Objects.isNull(twinClass)) {
            return false;
        }
        return Objects.equals(twinClass.getDataType(), dataType.getCode());
    }

    @Override
    public Set<String> getSpecificTwinClassData(TwinClassDataTypeEnum dataType) {
        LambdaQueryWrapper<TwinClass> where = Wrappers.<TwinClass>lambdaQuery().eq(TwinClass::getDataType, dataType.getCode()).eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());
        List<TwinClass> twinClassList = this.baseMapper.selectList(where);
        if (CollectionUtils.isEmpty(twinClassList)) {
            return Collections.emptySet();
        }
        return twinClassList.stream().map(TwinClass::getCode).collect(Collectors.toSet());
    }

    @Override
    public long countUseGroupId(Long groupId) {
        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getGroupId, groupId)
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        return count(wrapper);
    }

    @Override
    public boolean isLeafGroup(Long groupId) {
        final LambdaQueryWrapper<TwinClassGroup> wrapper = Wrappers.<TwinClassGroup>lambdaQuery()
                .eq(TwinClassGroup::getPid, groupId)
                .ne(TwinClassGroup::getStatus, StatusEnum.DELETED.getCode());

        return twinClassGroupMapper.selectCount(wrapper) == 0;
    }

    @Override
    public long sumDataTotal(TwinClassParam param) {
        // 查询条件
        LambdaQueryWrapper<TwinClass> queryWrapper = createQueryWrapper(param);
        queryWrapper.eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());
        final Set<String> twinClassCodes = list(queryWrapper).stream()
                .map(TwinClass::getCode)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(twinClassCodes)) {
            return 0;
        }

        return twinClassMapper.sumDataTotal(twinClassCodes);
    }

    /**
     * 通过查询参数构建Wrapper
     *
     * @param param 查询参数
     * @return 查询Wrapper
     * <AUTHOR>
     * @date 2021/8/30 14:06
     */
    private LambdaQueryWrapper<TwinClass> createQueryWrapper(TwinClassParam param) {
        return Wrappers.<TwinClass>lambdaQuery()
                .like(StringUtils.isNotEmpty(param.getName()), TwinClass::getName, param.getName())
                .like(StringUtils.isNotEmpty(param.getCode()), TwinClass::getCode, param.getCode())
                .eq(StringUtils.isNotEmpty(param.getLevel()), TwinClass::getLevel, param.getLevel())
                .eq(StringUtils.isNotEmpty(param.getDataType()), TwinClass::getDataType, param.getDataType())
                .eq(Objects.nonNull(param.getGroupId()) && param.getGroupId() != 0, TwinClass::getGroupId, param.getGroupId())
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());
    }

    /**
     * 孪生体实体对象转换为VO对象
     *
     * @param entity 孪生体实体对象
     * @return 孪生体VO对象
     * <AUTHOR>
     * @date 2021/8/30 14:06
     */
    private TwinClassVo entityToVO(TwinClass entity) {
        TwinClassVo vo = BeanUtils.copyToBean(entity, TwinClassVo.class, true);

        Optional.ofNullable(sysBubbleInfoMapper.selectById(entity.getBubbleInfoId()))
                .map(sysBubbleInfoApi::entityToVO)
                .ifPresent(e -> {
                    vo.setBubbleInfoName(e.getName());
                    vo.setBubbleInfoGroupId(e.getGroupId());
                    vo.setBubbleInfoVo(e);
                });

        Optional.ofNullable(entity.getThingsModelUuid()).flatMap(thingsModelsApi::getOneByModelId).ifPresent(e -> {
            vo.setThingsModelName(e.getTitle());
            vo.setThingsModelClassify(e.getClassify());
        });

        vo.setLevelText(sysDictDataApi.getDataMapByTypeCode(TWIN_CLASS_LEVEL).get(entity.getLevel()));
        vo.setDataTypeText(sysDictDataApi.getDataMapByTypeCode(TWIN_CLASS_DATA_TYPE).get(entity.getDataType()));
        vo.setTotal(twinBodyDataMapper.countByTwin(entity.getCode().toLowerCase()));
        processTwinClass(vo, IdentityContext.getLoginUserRoleIds());
        return vo;
    }

    private TwinClassVo entityToVO(TwinClass entity,
                                   Map<Long, SysBubbleInfo> sysBubbleInfoMap,
                                   Map<String, ThingsModel> thingsModelMap,
                                   Map<String, String> twinClassLevelMap,
                                   Map<String, String> twinClassDataTypeMap) {
        TwinClassVo vo = BeanUtils.copyToBean(entity, TwinClassVo.class, true);

        Optional.ofNullable(sysBubbleInfoMap.get(entity.getBubbleInfoId())).ifPresent(e -> {
            vo.setBubbleInfoName(e.getName());
            vo.setBubbleInfoGroupId(e.getGroupId());
        });

        Optional.ofNullable(entity.getThingsModelUuid()).map(thingsModelMap::get).ifPresent(e -> {
            vo.setThingsModelName(e.getTitle());
            vo.setThingsModelClassify(e.getClassify());
        });

        vo.setLevelText(twinClassLevelMap.get(entity.getLevel()));
        vo.setDataTypeText(twinClassDataTypeMap.get(entity.getDataType()));

        return vo;
    }

    /**
     * 校验保存参数
     *
     * @param param         保存参数
     * @param isExcludeSelf 校验是否排除自身
     * <AUTHOR>
     * @date 2021/8/30 14:06
     */
    private void checkParam(TwinClassParam param, boolean isExcludeSelf) {
        if (Objects.equals(param.getCode(), TWIN_CLASS_EXCLUDE_CODE)) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象编码不可用"));
        }

        // 数据类型不是表单
        if (!Objects.equals(param.getDataType(), TwinClassDataTypeEnum.FORM.getCode())) {
            // 所在层级不能为空
            if (StringUtils.isBlank(param.getLevel())) {
                throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "所在层级不能为空"));
            }

            // 气泡判断
            if (Objects.isNull(param.getBubbleInfoId())) {
                throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "气泡ID不能为空"));
            }
            final SysBubbleInfo sysBubbleInfo = sysBubbleInfoMapper.selectById(param.getBubbleInfoId());
            AssertUtils.isNotNull(sysBubbleInfo, new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象所选气泡不存在")));

            // 数据类型为点
            if (Objects.equals(param.getDataType(), TwinClassDataTypeEnum.POINT.getCode())) {

                // 模型判断
                if (Objects.isNull(param.getThingsModelUuid())) {
                    throw new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "模型ID不能为空"));
                }
                final ThingsModel thingsModel = thingsModelsApi.getOneByModelId(param.getThingsModelUuid()).orElse(null);
                AssertUtils.isNotNull(thingsModel, new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象所选模型不存在")));
            }
        }

        // 编码
        LambdaQueryWrapper<TwinClass> eqCodeWrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getCode, param.getCode())
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        // 名称
        LambdaQueryWrapper<TwinClass> eqNameWrapper = Wrappers.<TwinClass>lambdaQuery()
                .eq(TwinClass::getName, param.getName())
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        // 是否排除自身
        if (isExcludeSelf) {
            eqCodeWrapper.ne(TwinClass::getId, param.getId());
            eqNameWrapper.ne(TwinClass::getId, param.getId());
        }

        if (count(eqCodeWrapper) > 0) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象编码重复"));
        }
        if (count(eqNameWrapper) > 0) {
            throw new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象名称重复"));
        }

        // 校验 孪生融合视图 code冲突
        LambdaQueryWrapper<TwinCoalesce> TwinCoalesceLambdaQueryWrapper = new LambdaQueryWrapper<TwinCoalesce>()
                .eq(TwinCoalesce::getCode, param.getCode())
                .ne(TwinCoalesce::getStatus, StatusEnum.DELETED.getCode());
        AssertUtils.isTrue(!twinCoalesceMapper.exists(TwinCoalesceLambdaQueryWrapper), new TwinClassException(HttpStatus.BAD_REQUEST, "孪生对象编码与孪生融合视图编码重复"));

        // 分组
        final TwinClassGroup twinClassGroup = twinClassGroupMapper.selectById(param.getGroupId());
        AssertUtils.isNotNull(twinClassGroup, new TwinClassException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象所选分组不存在")));

        // 判断分组是否是叶子节点
        AssertUtils.isTrue(isLeafGroup(param.getGroupId()), ThrowUtils.getThrow().badRequest("孪生对象所选分组必须是叶子节点"));
    }

    /**
     * 添加孪生体菜单
     *
     * @param twinClass 孪生对象
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private void addTwinMenu(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "twinClass不能为空")));

        // 数据管理应用
        final String twinMenuAppCode = Optional.ofNullable(sysAppApi.getSysAppById(TWIN_MENU_APP_ID)).map(SysApp::getCode)
                .orElseThrow(() -> new TwinClassException(SysAppExpEnum.APP_NOT_EXIST));

        // 添加菜单
        SysMenuParam sysMenuParam = buildSysMenuParam(twinClass, twinMenuAppCode);
        sysMenuApi.add(sysMenuParam);

    }

    /**
     * 编辑孪生体菜单，考虑不同处理方式
     *
     * @param oldPure   之前是否管理
     * @param twinClass 孪生对象
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private void editTwinMenu(int oldPure, TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "twinClass不能为空")));

        // 构造孪生分类数据变更的处理映射
        Map<String, Consumer<TwinClass>> consumerMap = new HashMap<>();

        // pure 1 -> 1 变更
        consumerMap.put(BoolEnum.TRUE.getCode() + StringConstant.RIGHT_CHEV + BoolEnum.TRUE.getCode(), this::editTwinMenu);
        // pure 1 -> 0 删除
        consumerMap.put(BoolEnum.TRUE.getCode() + StringConstant.RIGHT_CHEV + BoolEnum.FALSE.getCode(), this::deleteTwinMenu);
        // pure 0 -> 1 新增
        consumerMap.put(BoolEnum.FALSE.getCode() + StringConstant.RIGHT_CHEV + BoolEnum.TRUE.getCode(), this::addTwinMenu);
        // pure 0 -> 0 不处理
        consumerMap.put(BoolEnum.FALSE.getCode() + StringConstant.RIGHT_CHEV + BoolEnum.FALSE.getCode(), e -> {
        });

        String consumerKey = oldPure + StringConstant.RIGHT_CHEV + twinClass.getPure();
        Optional.ofNullable(consumerMap.get(consumerKey)).ifPresent(e -> e.accept(twinClass));
    }

    /**
     * 编辑孪生体菜单
     *
     * @param twinClass 孪生对象
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private void editTwinMenu(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "twinClass不能为空")));

        final Optional<SysMenu> sysMenuOptional = findTwinMenu(twinClass.getCode());

        sysMenuOptional.map(e -> {
            SysMenuParam param = BeanUtils.copyToBean(e, SysMenuParam.class, true);
            param.setName(twinClass.getName());
            return param;
        }).ifPresent(sysMenuApi::edit);
    }

    /**
     * 删除孪生体菜单
     *
     * @param twinClass 孪生对象
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private void deleteTwinMenu(TwinClass twinClass) {
        AssertUtils.isNotNull(twinClass, new TwinClassException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "twinClass不能为空")));

        final Optional<SysMenu> sysMenuOptional = findTwinMenu(twinClass.getCode());

        // 存在就删除
        sysMenuOptional.ifPresent(sysMenu -> sysMenuApi.delete(BeanUtils.copyToBean(sysMenu, SysMenuParam.class, true)));
    }

    /**
     * 根据编码查询关联的孪生体菜单
     *
     * @param code 孪生对象
     * @return 孪生体菜单
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private Optional<SysMenu> findTwinMenu(String code) {
        // 数据管理应用
        final String twinMenuAppCode = Optional.ofNullable(sysAppApi.getSysAppById(TWIN_MENU_APP_ID)).map(SysApp::getCode)
                .orElseThrow(() -> new TwinClassException(SysAppExpEnum.APP_NOT_EXIST));

        return Optional.ofNullable(sysMenuApi.getOneByCodeAndApp(code, twinMenuAppCode));
    }

    /**
     * 构造孪生对象菜单参数
     *
     * @param twinClass 孪生对象
     * @return 孪生对象菜单参数
     * <AUTHOR>
     * @date 2021/12/30 18:14
     */
    private SysMenuParam buildSysMenuParam(TwinClass twinClass, String twinMenuAppCode) {
        // 构造孪生体菜单参数
        SysMenuParam sysMenuParam = new SysMenuParam();
        sysMenuParam.setPid(TWIN_MENU_PID);
        sysMenuParam.setName(twinClass.getName());
        sysMenuParam.setCode(twinClass.getCode());
        sysMenuParam.setType(MenuTypeEnum.MENU.getCode());
        sysMenuParam.setRouter(TWIN_MENU_ROUTER + twinClass.getCode());
        sysMenuParam.setComponent(TWIN_MENU_COMPONENT);
        sysMenuParam.setApplication(twinMenuAppCode);
        sysMenuParam.setVisible(YesOrNotEnum.Y.getCode());
        sysMenuParam.setWeight(MenuWeightEnum.SUPER_ADMIN_WEIGHT.getCode());
        sysMenuParam.setSort(TWIN_MENU_SORT);
        sysMenuParam.setSpecial(MenuSpecialEnum.TWIN.getCode());
        sysMenuParam.setIcon("code-sandbox");
        sysMenuParam.setSysCategoryId(TWIN_MENU_CATEGORY_ID);
        // 如果为地图层级的孪生体，生成外链的方式
        if (Objects.equals(twinClass.getLevel(), TwinClassLevelEnum.MAP.getCode())) {
            sysMenuParam.setOpenType(MenuOpenTypeEnum.OUTER.getCode());
            sysMenuParam.setLink("/preview/mapTwinData?id=" + twinClass.getId());
        } else {
            sysMenuParam.setOpenType(MenuOpenTypeEnum.COMPONENT.getCode());
        }

        return sysMenuParam;
    }

    /**
     * 获取排除的固定列
     *
     * @param twinClass 孪生对象
     * @return 排除的固定列
     */
    private String[] getExcludeColumnNames(TwinClass twinClass) {
        if (Objects.equals(twinClass.getDataType(), TwinClassDataTypeEnum.FORM.getCode())) {
            return TwinFixedColumnConstant.ALL_COLUMNS;
        } else if (Objects.equals(twinClass.getLevel(), TwinClassLevelEnum.MAP.getCode())) {
            return TwinFixedColumnConstant.MAP_EXCLUDE_COLUMNS;
        } else {
            return new String[]{};
        }
    }

    /**
     * 创建孪生对象
     *
     * @param param 孪生对象参数
     * @param form  表单信息
     * @return 孪生对象信息
     * <AUTHOR>
     * @date 2022/3/14 14:26
     */
    private TwinClassVo createTwinClass(TwinClassParam param, String form) {
        SpringIocUtils.mustGetBean(TwinBodyDataMapper.class).dropTable("test");
        // 校验参数
        checkParam(param, false);

        TwinClass twinClass = BeanUtils.copyToBean(param, TwinClass.class, true);

        if (Objects.nonNull(param.getThingsModelUuid())) {
            ThingsModel thingsModel = thingsModelsApi.getOneByModelId(param.getThingsModelUuid())
                    .orElseThrow(() -> new TwinServiceException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象所选模型不存在")));
            twinClass.setThingsModelUuid(thingsModel.getModelId());
        }
        twinClass.setStatus(StatusEnum.ENABLE.getCode());

        // 如果为地图层级的孪生对象，排除对应的固定列
        String[] excludeColumnNames = getExcludeColumnNames(twinClass);

        // 默认增加孪生体编码
        twinClass.setForm(form);
        final List<FormItem> formItem = FormUtils.parseListToForm(form);
        final String tableName = createTableName(twinClass.getCode());
        final List<Column> columns = ColumnUtils.parseFormToColumn(formItem);
        final String tableStructure = JrmJsonUtils.createTableStructure(AutoTableTypeEnum.TWIN, tableName, columns, excludeColumnNames);
        // 执行create ddl
        new CreateJrmDdl(AutoTableTypeEnum.TWIN, tableName, formItem, tableStructure).parse().invoke();
        twinClass.setStructure(tableStructure);

        save(twinClass);

        // 如果为单独管理，添加对应孪生分类菜单
        if (Objects.equals(BoolEnum.TRUE.getCode(), twinClass.getPure())) {
            addTwinMenu(twinClass);
        }

        return entityToVO(twinClass);
    }

    /**
     * 获取关联该孪生体的孪生对象
     *
     * @param twinClassCode 孪生对象编码
     * @return 孪生对象列表
     * <AUTHOR>
     * @date 2022/5/24 10:28
     */
    private List<TwinClass> getOtherUsed(String twinClassCode) {
        final LambdaQueryWrapper<TwinClass> otherUsedWrapper = Wrappers.<TwinClass>lambdaQuery()
                .like(TwinClass::getForm, "\"twinClassCode\":\"" + twinClassCode + "\"")
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());
        return list(otherUsedWrapper);
    }

    @Override
    public List<TwinClass> findByNames(Set<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Lists.newArrayList();
        }

        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .in(TwinClass::getName, names)
                .eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());

        return list(wrapper);
    }

    @Override
    public List<TwinClass> findByNameLike(String name) {
        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .and(q -> q.like(TwinClass::getName, name).or().like(TwinClass::getCode, name))
                .eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode());

        return list(wrapper);
    }

    /**
     * 优化后的获取气泡信息映射方法
     */
    private Map<Long, SysBubbleInfo> getBubbleInfoMapOptimized(List<TwinClassVo> records) {
        // 获取所有气泡信息的 ID
        final Set<Long> bubbleInfoIds = records.stream()
                .map(TwinClassVo::getBubbleInfoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 如果没有气泡信息 ID，返回空映射
        if (CollectionUtils.isEmpty(bubbleInfoIds)) {
            return new HashMap<>();
        }

        // 优化：使用批量查询，减少数据库连接次数，添加超时控制
        try {
            // 分批查询，避免一次性查询过多数据
            Map<Long, SysBubbleInfo> result = new HashMap<>();
            List<Long> idList = new ArrayList<>(bubbleInfoIds);
            int batchSize = 20; // 每批查询20个

            for (int i = 0; i < idList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, idList.size());
                List<Long> batchIds = idList.subList(i, endIndex);

                try {
                    List<SysBubbleInfo> batchResult = sysBubbleInfoMapper.selectBatchIds(batchIds);
                    batchResult.forEach(info -> result.put(info.getId(), info));
                } catch (Exception e) {
                    log.warn("批量查询气泡信息失败，批次: {}-{}, 错误: {}", i, endIndex, e.getMessage());
                    // 降级：使用单个查询
                    for (Long id : batchIds) {
                        try {
                            SysBubbleInfo info = sysBubbleInfoMapper.selectById(id);
                            if (info != null) {
                                result.put(id, info);
                            }
                        } catch (Exception ex) {
                            log.warn("查询气泡信息失败，ID: {}, 错误: {}", id, ex.getMessage());
                        }
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.warn("批量查询气泡信息失败，使用单个查询: {}", e.getMessage());
            // 降级：使用单个查询
            Map<Long, SysBubbleInfo> result = new HashMap<>();
            for (Long id : bubbleInfoIds) {
                try {
                    SysBubbleInfo info = sysBubbleInfoMapper.selectById(id);
                    if (info != null) {
                        result.put(id, info);
                    }
                } catch (Exception ex) {
                    log.warn("查询气泡信息失败，ID: {}, 错误: {}", id, ex.getMessage());
                }
            }
            return result;
        }
    }

    /**
     * 优化后的获取模型信息映射方法
     */
    private Map<String, ThingsModel> getThingsModelMapOptimized(List<TwinClassVo> records) {
        log.debug("开始执行getThingsModelMapOptimized方法，records.size={}", records.size());

        Map<String, ThingsModel> result = new HashMap<>();

        // 直接遍历records中的每个数据
        for (TwinClassVo record : records) {
            String thingsModelUuid = record.getThingsModelUuid();
            if (thingsModelUuid != null && !thingsModelUuid.trim().isEmpty()) {
                log.debug("查询模型，TwinClass.id={}, thingsModelUuid={}", record.getId(), thingsModelUuid);

                try {
                    // 通过things_model_uuid去查询模型
                    Optional<ThingsModel> model = thingsModelsApi.getOneByModelId(thingsModelUuid);
                    if (model.isPresent()) {
                        ThingsModel thingsModel = model.get();
                        // 使用thingsModelUuid作为key存储到映射中
                        result.put(thingsModelUuid, thingsModel);
                        log.debug("成功查询到模型: modelId={}, title={}, id={}, classify={}",
                                thingsModel.getModelId(), thingsModel.getTitle(), thingsModel.getId(), thingsModel.getClassify());
                    } else {
                        log.warn("未找到模型，thingsModelUuid={}", thingsModelUuid);
                    }
                } catch (Exception e) {
                    log.error("查询模型失败，thingsModelUuid={}, 错误: {}", thingsModelUuid, e.getMessage(), e);

                    // 如果是认证失败，记录详细信息但不抛出异常
                    if (e.getMessage() != null && e.getMessage().contains("401")) {
                        log.warn("认证失败，可能是微服务间Token传递问题，thingsModelUuid: {}", thingsModelUuid);
                    }
                }
            } else {
                log.debug("TwinClass的thingsModelUuid为空，跳过查询: TwinClass.id={}", record.getId());
            }
        }

        log.debug("ThingsModel映射构建完成，共{}个模型", result.size());
        log.debug("ThingsModel映射的keys: {}", result.keySet());
        return result;
    }

    /**
     * 优化后的孪生体数据处理方法
     * @param twinClass 孪生体数据
     * @param loginUserRoleIds 用户角色ID列表
     * @param me 是否为当前用户
     * @return 处理后的孪生体数据
     */
    private TwinClassVo processTwinClassOptimized(TwinClassVo twinClass, List<String> loginUserRoleIds, boolean me) {
        if (me) {
            // 当前用户直接设置原始数据，无需权限过滤
            twinClass.setAddForm(twinClass.getForm());
            twinClass.setEditForm(twinClass.getForm());
            twinClass.setReadForm(twinClass.getForm());
            twinClass.setAddStructure(twinClass.getStructure());
            twinClass.setEditStructure(twinClass.getStructure());
            twinClass.setReadStructure(twinClass.getStructure());
        } else {
            // 获取用户的权限 - 使用优化后的查询方法
            List<TwinPermission> permissions = getPermissionsByRoleIdsOptimized(loginUserRoleIds, twinClass.getCode());

            // 优化权限处理逻辑
            PermissionSets permissionSets = processPermissionsOptimized(permissions);

            // 过滤表单和结构
            try {
                // 记录调试信息
                log.debug("处理孪生对象权限过滤，twinClass code: {}, form: {}, structure: {}",
                        twinClass.getCode(),
                        twinClass.getForm() != null ? "已设置" : "null",
                        twinClass.getStructure() != null ? "已设置" : "null");

                // 过滤添加、编辑和读取表单
                twinClass.setAddForm(filterJsonBySetOptimized(twinClass.getForm(), permissionSets.addColumns, false));
                twinClass.setEditForm(filterJsonBySetOptimized(twinClass.getForm(), permissionSets.editColumns, false));
                twinClass.setReadForm(filterJsonBySetOptimized(twinClass.getForm(), permissionSets.readColumns, false));

                // 过滤添加、编辑和读取结构
                twinClass.setAddStructure(filterJsonBySetOptimized(twinClass.getStructure(), permissionSets.addColumnsWithDefault, true));
                twinClass.setEditStructure(filterJsonBySetOptimized(twinClass.getStructure(), permissionSets.editColumns, true));
                twinClass.setReadStructure(filterJsonBySetOptimized(twinClass.getStructure(), permissionSets.readColumns, true));
            } catch (JsonProcessingException ex) {
                // 处理 JSON 解析异常，记录详细错误信息
                log.error("处理孪生对象权限过滤时发生JSON解析错误，twinClass code: {}, form: {}, structure: {}",
                        twinClass.getCode(),
                        twinClass.getForm(),
                        twinClass.getStructure(), ex);
                throw new RuntimeException("JSON 处理错误: " + ex.getMessage(), ex);
            }
        }

        return twinClass;
    }

    /**
     * 优化后的权限查询方法，带缓存机制
     * @param roleIds 角色ID列表
     * @param twinCode 孪生体代码
     * @return 权限列表
     */
    private List<TwinPermission> getPermissionsByRoleIdsOptimized(List<String> roleIds, String twinCode) {
        if (CollectionUtils.isEmpty(roleIds) || StringUtils.isBlank(twinCode)) {
            return new ArrayList<>();
        }

        // 优化缓存key生成，使用StringBuilder避免字符串拼接
        String cacheKey = buildCacheKey(roleIds, twinCode);
        Map<String, List<TwinPermission>> cache = permissionCache.get();

        // 检查缓存
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }

        // 创建查询条件以获取权限
        LambdaQueryWrapper<TwinPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TwinPermission::getRoleId, roleIds);
        queryWrapper.eq(TwinPermission::getTwinCode, twinCode);

        // 只查询需要的字段，减少数据传输
        queryWrapper.select(TwinPermission::getAddColumn, TwinPermission::getEditColumn, TwinPermission::getReadColumn);

        List<TwinPermission> permissions = twinPermissionMapper.selectList(queryWrapper);

        // 缓存结果
        cache.put(cacheKey, permissions);

        return permissions;
    }

    /**
     * 优化后的缓存key生成方法
     * @param roleIds 角色ID列表
     * @param twinCode 孪生体代码
     * @return 缓存key
     */
    private String buildCacheKey(List<String> roleIds, String twinCode) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < roleIds.size(); i++) {
            if (i > 0) {
                sb.append(':');
            }
            sb.append(roleIds.get(i));
        }
        sb.append(':').append(twinCode);
        return sb.toString();
    }

    /**
     * 优化后的JSON过滤方法，使用缓存的ObjectMapper
     * @param jsonString JSON字符串
     * @param filterSet 过滤集合
     * @param isColumnFilter 是否为列过滤
     * @return 过滤后的JSON字符串
     * @throws JsonProcessingException JSON处理异常
     */
    private static String filterJsonBySetOptimized(String jsonString, Set<String> filterSet, boolean isColumnFilter) throws JsonProcessingException {
        // 快速返回空JSON
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return isColumnFilter ? EMPTY_COLUMN_JSON : EMPTY_LIST_JSON;
        }

        // 如果过滤集合为空，直接返回空JSON
        if (filterSet.isEmpty()) {
            return isColumnFilter ? EMPTY_COLUMN_JSON : EMPTY_LIST_JSON;
        }

        // 使用缓存的ObjectMapper实例
        JsonNode rootNode = OBJECT_MAPPER.readTree(jsonString);
        ArrayNode filteredArray = OBJECT_MAPPER.createArrayNode();

        // 根据标志选择要过滤的目标节点
        JsonNode targetNode = isColumnFilter ? rootNode.path("column") : rootNode.path("list");

        // 根据 filterSet 过滤目标节点
        targetNode.forEach(node -> {
            // 获取当前节点的名称
            String name = isColumnFilter ? node.path("name").asText() : node.path("model").asText();
            // 如果名称在过滤集合中，则将节点添加到过滤后的数组中
            if (filterSet.contains(name)) {
                filteredArray.add(node);
            }
        });

        // 更新原始 JSON，替换成过滤后的数组
        if (isColumnFilter) {
            ((ObjectNode) rootNode).set("column", filteredArray);
        } else {
            ((ObjectNode) rootNode).set("list", filteredArray);
        }

        // 将更新后的 JSON 转换回字符串并返回
        return OBJECT_MAPPER.writeValueAsString(rootNode);
    }

    /**
     * 优化后的权限处理方法
     * @param permissions 权限列表
     * @return 权限集合
     */
    private PermissionSets processPermissionsOptimized(List<TwinPermission> permissions) {
        if (CollectionUtils.isEmpty(permissions)) {
            return new PermissionSets(EMPTY_SET, EMPTY_SET, EMPTY_SET);
        }

        Set<String> addColumns = new HashSet<>();
        Set<String> editColumns = new HashSet<>();
        Set<String> readColumns = new HashSet<>();

        // 使用传统for循环和优化的字符串处理
        for (TwinPermission permission : permissions) {
            processPermissionColumns(permission.getAddColumn(), addColumns);
            processPermissionColumns(permission.getEditColumn(), editColumns);
            processPermissionColumns(permission.getReadColumn(), readColumns);
        }

        return new PermissionSets(addColumns, editColumns, readColumns);
    }

    /**
     * 优化后的权限列处理方法
     * @param columnString 权限列字符串
     * @param targetSet 目标集合
     */
    private void processPermissionColumns(String columnString, Set<String> targetSet) {
        if (columnString != null && !columnString.trim().isEmpty()) {
            // 使用更高效的字符串分割方式
            int length = columnString.length();
            int start = 0;
            for (int i = 0; i < length; i++) {
                if (columnString.charAt(i) == ',') {
                    if (i > start) {
                        targetSet.add(columnString.substring(start, i).trim());
                    }
                    start = i + 1;
                }
            }
            // 处理最后一个元素
            if (start < length) {
                targetSet.add(columnString.substring(start).trim());
            }
        }
    }

    /**
     * 权限集合封装类，用于优化权限处理
     */
    private static class PermissionSets {
        final Set<String> addColumns;
        final Set<String> editColumns;
        final Set<String> readColumns;
        final Set<String> addColumnsWithDefault;

        PermissionSets(Set<String> addColumns, Set<String> editColumns, Set<String> readColumns) {
            this.addColumns = addColumns;
            this.editColumns = editColumns;
            this.readColumns = readColumns;
            // 预计算包含默认列的添加权限集合
            this.addColumnsWithDefault = new HashSet<>(addColumns);
            this.addColumnsWithDefault.addAll(PermissionConstant.DEFAULT_COLUMN);
        }
    }


    private Map<String, String> getDictDataWithCache(String typeCode) {
        return dictCache.get().computeIfAbsent(typeCode, k -> {
            return sysDictDataApi.getDataMapByTypeCode(typeCode);
        });
    }

}
