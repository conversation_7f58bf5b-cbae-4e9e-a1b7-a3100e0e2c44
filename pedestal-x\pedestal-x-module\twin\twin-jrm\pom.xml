<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.uino.x</groupId>
        <artifactId>twin</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>twin-jrm</artifactId>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>${character.encoding}</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${character.encoding}</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>${project.parent.artifactId}-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>${project.parent.artifactId}-dao</artifactId>
        </dependency>
        <!--        apijson-->
        <dependency>
            <groupId>com.github.Tencent</groupId>
            <artifactId>APIJSON</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>identity-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>common-x-cache</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>alarm-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.uino.x</groupId>
            <artifactId>pedestal-x-common</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>