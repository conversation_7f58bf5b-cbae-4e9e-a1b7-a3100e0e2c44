package com.uino.x.pedestal.twin.jrm;


import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.exception.JrmException;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.json.serialize.JsonSerialize;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.SqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.SqlDefinitionFactory;
import com.uino.x.pedestal.twin.jrm.core.sql.executor.SqlExecutor;
import com.uino.x.pedestal.twin.jrm.json.definition.DefaultJsonDefinition;
import com.uino.x.pedestal.twin.jrm.sql.executor.DefaultSqlExecutor;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

/**
 * 基于spring环境的Jrm bean
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/24 9:38
 */
public class JrmBean {

    /**
     * 自定义json sql规则的请求实现
     *
     * @param requestType 请求类型
     * @param request     请求json
     * @return 请求响应结果
     * <AUTHOR>
     * @date 2021/4/24 9:38
     */
    public static String request(RequestType requestType, String request) {

        return Jrm.getInstance(requestType, requestType.isApiJson() ? null : createSqlExecutor(requestType, request)).request(request);
    }

    /**
     * 创建sql执行器
     *
     * @param requestType 请求类型
     * @param request     请求json
     * @return Sql执行器
     */
    private static SqlExecutor createSqlExecutor(RequestType requestType, String request) {
        final DataSource dataSource = getDataSource();
        final JsonSerialize jsonSerialize = getJsonSerialize();
        final JsonDefinition jsonDefinition = DefaultJsonDefinition.of(JrmJsonUtils.convertNestedStructure(request), jsonSerialize);
        final SqlDefinition sqlDefinition = SqlDefinitionFactory.get(dataSource, jsonDefinition, requestType);
        return DefaultSqlExecutor.of(sqlDefinition);
    }

    /**
     * sql查询
     *
     * @param sql sql字符串
     * @return 数据集json
     * <AUTHOR>
     * @date 2021/10/13 10:20
     */
    public static String query(String sql) {

        return Jrm.query(getDataSource(), sql);
    }

    /**
     * sql查询
     *
     * @param sql sql字符串
     * @return 数据集json
     * <AUTHOR>
     * @date 2021/10/13 10:20
     */
    public static List<Map<String, ?>> queryList(String sql) {

        return Jrm.queryList(getDataSource(), sql);
    }

    /**
     * 获取数据源
     *
     * @return 数据源
     * <AUTHOR>
     * @date 2023/2/16 15:07
     */
    public static DataSource getDataSource() {
        DataSource dataSource = SpringIocUtils.getBean(DataSource.class);
        AssertUtils.isNotNull(dataSource, new JrmException("DataSource bean not find!"));
        return dataSource;
    }

    /**
     * 获取json序列化器
     *
     * @return json序列化器
     * <AUTHOR>
     * @date 2023/2/16 15:07
     */
    public static JsonSerialize getJsonSerialize() {
        JsonSerialize jsonSerialize = SpringIocUtils.getBean(JsonSerialize.class);
        AssertUtils.isNotNull(jsonSerialize, new JrmException("JsonSerialize bean not find!"));
        return jsonSerialize;
    }
}
