<!--
 * @Description: 功能模块
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-03 17:29:30
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 15:33:23
-->
<template>
  <div class="func-module">
    <div class="tag-search">
      <div class="tag-content" v-if="tagsList?.length">
        <div v-for="group in tagsList" :key="group.id" class="tag-group">
          <div v-if="group?.tags?.length" class="tag-group-name" @click="checkGroupAll(group)">
            {{ group.groupName }}
          </div>
          <div v-if="group?.tags?.length" class="tag-item">
            <a-checkbox-group v-model:value="tagsChecked[group.id]" style="width: 100%">
              <div v-for="tag in group.tags" :key="tag.id" class="tag-item-name">
                <a-checkbox :value="tag.id">{{ tag.tagName }}</a-checkbox>
              </div>
            </a-checkbox-group>
          </div>
        </div>
      </div>
      <div class="no-tag-content" v-else>
        <a-spin class="loading-icon" v-if="tagLoading" :spinning="tagLoading" />
        <Nodata v-if="!tagLoading" title="请绑定标签" />
      </div>
    </div>
    <div class="page-wrap">
      <!-- 搜索栏 -->
      <div class="search-wrap">
        <div class="search-content">
          <div class="search-item">
            <span class="search-label">示例名称</span>
            <div>
              <a-input v-model:value.trim="searPar.keyWord" allow-clear placeholder="请输入示例名称" class="search-input" @keyup.enter="clickRefresh(searPar.keyWord)" />
            </div>
          </div>
          <div class="search-item">
            <span class="search-label">审批状态</span>
            <div>
              <a-select ref="select" v-model:value="reviewStatus" placeholder="请选择审批状态" class="search-select" allow-clear @change="selectStatus">
                <a-select-option :value="-1">全部</a-select-option>
                <a-select-option :value="3">待审批</a-select-option>
                <a-select-option :value="0">审批通过</a-select-option>
                <a-select-option :value="4">审批不通过</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="search-btns">
            <a-button type="primary" class="search-btn" @click="clickRefresh(searPar.keyWord)"> 查询 </a-button>
            <!-- <a-button class="search-btn" @click="reset()"> 重置 </a-button> -->
          </div>
        </div>
        <div class="table-handle">
          <a-button v-if="hasPerm('sys-sample:add')" type="primary" class="handle-btn" @click="handel('add', null)"> 新增功能示例 </a-button>
        </div>
      </div>
      <div class="content-list">
        <div class="list">
          <div v-for="item in sampleData" v-show="!loading && sampleData.length" :key="item.id" class="item">
            <div class="contain">
              <div class="img-box">
                <div class="img-item">
                  <img :src="getUrl(item)" alt="图片" class="img" @error="(event: any) => ((event.target.src = systemLogo), (event.target.style.width = 'auto'))" />
                </div>
                <div class="bottom-wrapper">
                  <div class="bottom-content">
                    <div class="time">上传时间：{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') || '' }}</div>
                    <div
                      v-if="item.status !== 0"
                      class="status"
                      :class="{
                        fail: item.status === 4,
                        check: item.status === 3,
                        delete: item.status === 2,
                        unpush: item.status === 1,
                      }"
                    >
                      {{ getStatus(item.status) }}
                    </div>
                  </div>
                </div>
                <div class="hover-box">
                  <div v-if="hasPerm('sys-sample:detail')" class="btn" @click="perviewClick(item)">预览</div>
                  <div v-if="hasPerm('sys-sample:edit')" class="btn" @click="handel('edit', item)">编辑</div>
                  <a-popconfirm
                    placement="topRight"
                    v-if="hasPerm('sys-sample:change-status') && hasPerm('sys-sample:batch-delete')"
                    :title="`确认${delOrOffShelf(item.status)}当前功能示例？`"
                    @confirm="delExampleFn(item)"
                  >
                    <div v-if="item.status === 0" class="btn">下架</div>
                    <div v-if="item.status !== 0" class="btn">删除</div>
                  </a-popconfirm>
                  <a-popconfirm
                    placement="topRight"
                    title="确认审批通过？"
                    v-if="hasPerm('sys-sample:change-status') && hasPerm('sys-sample:edit')"
                    okText="通过"
                    cancelText="不通过"
                    @confirm="confirmExamine(true, item)"
                    @cancel="confirmExamine(false, item)"
                  >
                    <div v-if="item.status === 3" class="btn">审批</div>
                    <div v-if="item.status === 4" class="btn">重新审批</div>
                  </a-popconfirm>
                  <div class="control-icon">
                    <a-tooltip placement="top">
                      <template #title>
                        <span>{{ item.failureCause }}</span>
                      </template>
                      <exception-outlined v-if="item.status === 4" title="未通过原因" style="margin-right: 5px" />
                    </a-tooltip>
                    <file-word-outlined title="示例说明" @click="openPreview(item.remark)" />
                  </div>
                </div>
              </div>
              <div class="item-bottom">
                <div class="title">
                  <div class="name" :title="item.name">{{ item.name }}</div>
                  <div class="user" :title="item.createName">
                    {{ item.createName }}
                  </div>
                </div>
                <div class="tag-wrapper">
                  <div :id="item.id" ref="tagList" class="tag-list">
                    <div v-for="(ele, index) in item.functionExampleTags" :key="index" :title="ele.tagName" class="tag-item" :style="{ backgroundColor: ele.color }">
                      {{ ele.tagName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a-spin v-if="loading" class="loading-icon" :spinning="loading" />
          <Nodata v-if="!loading && !sampleData.length" />
        </div>
        <div class="pagination-box">
          <a-pagination
            :total="paginationConfig.total"
            :page-size-options="['12', '20', '30', '40']"
            :current="paginationConfig.current"
            :page-size="paginationConfig.pageSize"
            size="small"
            :show-total="(total: number) => `共 ${total} 条`"
            show-size-changer
            @change="paginationChange"
          />
        </div>
      </div>
    </div>
    <AddExample ref="AddExampleRef" @ok="backOk" />
    <DocPreview ref="docPreviewRef" />
    <Reason ref="reasonRef" @ok="backOk" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import dayjs from 'dayjs';
import Nodata from '@/views/default/Nodata.vue';
import projectGalleryImg from '@/assets/img/resource/projectGallery.png';
import DocPreview from './DocPreview.vue';
import { getListByPage, delExample, sampleGroup, editExample, sampleShangeSa } from '@/api/operator/sourceManage/examples';
import Reason from './Reason.vue';
import AddExample from './AddExample.vue';
import router from '@/router';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
// 系统logo
const systemLogo = ref(sessionStorage.getItem('XI_TONG_LOGO') || projectGalleryImg);
const AddExampleRef = ref();

const tagsChecked = ref<any>({});
// ------------------------ 审批相关 -------------
const reviewStatus = ref(-1);
const selectStatus = (val: any) => {
  reviewStatus.value = val;
  // TODO: 根据审批状态筛选功能示例
  clickRefresh();
};
const editStatus = (param: any) => {
  editExample(param).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '功能示例审批完成');
      getData();
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
// 审批弹窗
const reasonRef = ref();
const confirmExamine = (val: any, item: any) => {
  if (!val) {
    // 审批不通过
    reasonRef.value.init(item);
  } else {
    // const formData = new FormData();
    // formData.append('status', 0);
    // formData.append('id', item.id);
    const param = {
      status: 0,
      id: item.id,
    };
    // 审批通过
    sampleShangeSa(param).then((res) => {
      if (res.code === 200) {
        useGlobalMessage('success', '功能示例审批完成');
        getData();
      } else {
        useGlobalMessage('error', res.message);
      }
    });
  }
};
const getStatus = (status: number) => {
  let backWord = '';
  if (status === 0) {
    backWord = '正常';
  } else if (status === 1) {
    backWord = '已下架';
  } else if (status === 2) {
    backWord = '已删除';
  } else if (status === 3) {
    backWord = '待审批';
  } else if (status === 4) {
    backWord = '未通过';
  }
  return backWord;
};
// ------------------- tag分组数据----------------------
// tag分组数据
const tagsList = ref([]);

watch(
  () => tagsChecked.value,
  () => {
    clickRefresh();
  },
  {
    deep: true,
  }
);
const tagLoading = ref(true);
// 获取类型分组
const getSampleGroup = () => {
  tagLoading.value = true;
  sampleGroup()
    .then((res) => {
      tagLoading.value = false;
      if (res.code !== 200) {
        tagsList.value = [];
        return;
      }
      if (res.data.length) {
        // 储存分组结构
        res.data.forEach((group: any) => {
          tagsChecked.value[group.id] = [];
        });
      } else {
        tagsChecked.value = {};
      }
      tagsList.value = res.data || [];
    })
    .catch(() => {
      tagsChecked.value = {};
      tagsList.value = [];
      tagLoading.value = false;
    });
};
getSampleGroup();
// 全选-反选当前分组
const checkGroupAll = (record: any) => {
  const currentTagIds = record.tags?.map((tag: any) => tag.id);
  if (JSON.stringify(tagsChecked.value[record.id]) === JSON.stringify(currentTagIds)) {
    tagsChecked.value[record.id] = [];
  } else {
    tagsChecked.value[record.id] = record.tags.map((tag: any) => tag.id);
  }
};
// 分页配置
const paginationConfig = ref({
  total: 0,
  current: 1,
  pageSize: 12,
  showTotal: (total: any) => `共有${total}条`,
  showSizeChanger: true,
  showQuickJumper: true,
  size: 'small',
});
// 分页变化
const paginationChange = (current: number, pageSize: number) => {
  paginationConfig.value = Object.assign(paginationConfig.value, {
    current,
    pageSize,
  });
  getData();
};
// 预览
const perviewClick = (record: any) => {
  // const url = `${window.config.previewUrl}${record.url}index.html`;
  // window.open(url);
  const { href } = router.resolve({
    path: '/portal/sampleOnlineEditor',
    query: {
      id: record.id,
    },
  });
  window.open(href, '_blank');
};
// 示例说明预览
const docPreviewRef = ref();
const openPreview = (item: string) => {
  docPreviewRef.value.init(item);
};
const delOrOffShelf = (status: any) => {
  let word = '删除';
  if (status === 0) {
    word = '下架';
  }
  return word;
};
// 删除
// status：0正常/审核通过  1 下架  2删除   3待审核  4审核未通过
// failureCause：失败原因
const delExampleFn = async (record: any) => {
  const { id, status } = record;
  // 审批通过下架
  if (status === 0) {
    // const formData = new FormData();
    // formData.append('status', '1');
    // formData.append('id', id);
    sampleShangeSa({
      id,
      status: '1',
    }).then((res) => {
      if (res.code === 200) {
        useGlobalMessage('success', '功能示例下架完成');
        getData();
      } else {
        useGlobalMessage('error', res.message);
      }
    });
  } else {
    // 审批不通过和未审批的删除
    const res: any = await delExample({
      id,
    });
    if (res.code === 200) {
      useGlobalMessage('success', '功能示例删除成功');
      getData();
    } else {
      useGlobalMessage('error', res.message);
    }
  }
};

const searPar = ref({
  keyWord: '', // 项目名称
});

const backOk = () => {
  getData();
};

const loading = ref(true);
const sampleData: any = ref([]);

// 获取列表
const getData = async () => {
  sampleData.value = [];
  loading.value = true;
  const tagId = Object.values(tagsChecked.value)?.flat(Infinity) || [];
  const param = {
    data: {
      name: searPar.value.keyWord,
      pageNo: paginationConfig.value.current,
      pageSize: paginationConfig.value.pageSize,
      tagId,
    },
  };
  const status = reviewStatus.value;
  if (status > -1) {
    // @ts-ignore
    param.data.status = status;
  }
  const res: any = await getListByPage(param);

  loading.value = false;
  if (res.code === 200) {
    const { rows, pageNo, totalRows } = res.data;
    paginationConfig.value.total = totalRows;
    paginationConfig.value.current = pageNo;
    sampleData.value = rows;
  }
};
// 重置
const reset = () => {
  searPar.value.keyWord = '';
  reviewStatus.value = -1;
  clickRefresh();
};
const clickRefresh = (name = '') => {
  paginationConfig.value.current = 1;
  paginationConfig.value.pageSize = 12;
  getData();
};

const handel = (type: string, record: any) => {
  AddExampleRef.value.init(type, record);
};
// 获取拼接地址
const getUrl = (item: any) => {
  return `${window.config.previewUrl}${item.previewUrl}?width=400`;
};
</script>

<style scoped lang="scss">
::v-deep .ant-modal-mask {
  background-color: rgb(0 0 0 / 10%);
}

.func-module {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0 10px 0 0;
  overflow: hidden;
  background: var(--primary-bg-color);
  border-radius: 4px;

  .tag-search {
    box-sizing: border-box;
    width: 187px;
    min-width: 100px;
    max-width: 187px;
    height: 100%;
    padding: 10px 0;
    border-right: 1px solid var(--header-border-color);

    .tag-content {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 10px;
      overflow-y: auto;
    }

    .no-tag-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 0 10px;
    }

    .tag-group {
      width: 100%;
      margin-bottom: 8px;

      .tag-group-name {
        height: 24px;
        min-height: 20px;
        margin-bottom: 4px;
        overflow: hidden;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: var(--header-text-active-color);
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }

      .tag-item-name {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 2px 2px 2px 9px;
        color: var(--header-text-color);
        border-radius: 4px;

        :deep(.ant-checkbox) {
          margin-right: 4px;
        }

        &.checked {
          background-color: var(--modal-header-bg-color);
        }

        &:hover {
          background-color: var(--modal-header-bg-color);
        }
      }

      .tag-item {
        margin-bottom: 4px;
      }
    }
  }

  .page-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    padding-left: 10px;
  }

  .search-wrap {
    align-items: inherit;
    border-bottom: 1px solid var(--primary-border-color);

    .table-handle {
      padding: 0;
      border: none;
    }
  }

  .search-content {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
  }

  .content-list {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
  }

  .list {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    place-content: baseline start;
    padding: 4px;
    overflow-y: auto;

    .loading-icon {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .item {
      padding: 4px;

      @media screen and (width >= 1600px) {
        width: calc(100% / 4);
      }

      @media screen and (width <= 1600px) {
        width: calc(100% / 3);
      }

      @media screen and (width <= 900) {
        width: calc(100% / 2);
      }

      @media screen and (width <= 750) {
        width: calc(100%);
      }

      .contain {
        box-sizing: border-box;
        height: 100%;
        background: var(--router-bg-color);
        border-radius: 4px;

        .img-box {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          height: 163px;
          overflow: hidden;
          text-align: center;
          background: var(--conent-bg-color-img);
          background-color: #000;
          border-bottom: 1px solid var(--header-border-color);

          .top-wrapper {
            position: absolute;
            top: 8px;
            left: 6px;
            padding: 2px 10px;
            color: #fff;
            background-color: rgb(0 0 0 / 20%);
            border-radius: 4px;

            .normal {
              color: green;
            }

            .delete {
              color: #f8f8f8;
            }

            .fail {
              color: red;
            }

            .check {
              color: dodgerblue;
            }
          }

          .bottom-wrapper {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 24px;
            color: #fff;

            .status {
              padding: 0 6px;
              color: #fff;

              &.normal {
                color: green;
              }

              &.delete {
                color: #f8f8f8;
              }

              &.fail {
                color: red;
              }

              &.check {
                color: dodgerblue;
              }

              &.unpush {
                color: aqua;
              }
            }

            .bottom-content {
              display: flex;
              flex-direction: row-reverse;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              height: 100%;
              padding: 0 3px;
              background-color: rgb(0 0 0 / 50%);
            }
          }

          &:hover {
            .hover-box {
              visibility: visible;
            }
          }

          .hover-box {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            visibility: hidden;
            background-color: rgb(0 0 0 / 50%);
            border-radius: 4px 4px 0 0;

            .control-icon {
              position: absolute;
              top: 10px;
              right: 15px;
              font-size: 16px;
              color: #fff;
              cursor: pointer;
            }

            .btn {
              height: 20px;
              padding: 0 12px;
              font-size: 12px;
              line-height: 20px;
              color: #fff;
              text-align: center;
              letter-spacing: 2px;
              cursor: pointer;
              background-color: var(--theme-color);
              border-radius: 4px;

              &:nth-of-type(1),
              &:nth-of-type(2),
              &:nth-of-type(3) {
                margin-right: 5px;
              }
            }
          }

          &.active {
            border-color: var(--theme-color);
          }

          img {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            max-height: 100%;
            object-fit: cover;
            transform: translate(-50%, -50%);
          }
        }

        .item-bottom {
          width: 100%;
          padding: 6px 6px 8px;

          .title {
            display: flex;

            .name {
              width: 70%;
              overflow: hidden;
              font-size: 14px;
              font-weight: 500;
              color: var(--primary-text-color);
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .user {
              width: 30%;
              overflow: hidden;
              font-size: 13px;
              font-weight: 500;
              color: var(--secnd-text-color);
              text-align: right;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .tag-wrapper {
            position: relative;

            .tag-list {
              display: flex;
              flex-wrap: wrap;

              .tag-item {
                flex-shrink: 0;
                max-width: 98%;
                padding: 0 6px;
                margin-top: 2px;
                margin-right: 6px;
                overflow: hidden;
                font-family: Helvetica;
                font-size: 14px;
                color: #fff;
                text-overflow: ellipsis;
                white-space: nowrap;
                border-radius: 4px;
              }
            }

            .tag-opera {
              position: absolute;
              top: 0;
              right: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 22px;
              height: 22px;
              cursor: pointer;
              background: var(--second-bg-color);
              border-radius: 4px;
            }

            .type-list {
              position: absolute;
              right: 0;
              bottom: 30px;
              display: flex;
              flex-wrap: wrap;
              width: 200px;
              max-height: 150px;
              padding: 8px;
              overflow-y: scroll;
              background: #fff;
              border-radius: 6px;

              .type-item {
                height: 22px;
                padding: 0 6px;
                margin-right: 6px;
                margin-bottom: 6px;
                font-family: Helvetica;
                font-size: 14px;
                line-height: 22px;
                color: #fff;
                border-radius: 4px;
              }
            }
          }
        }
      }
    }
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 40px;
    min-height: 40px;
    max-height: 50px;
  }
}
</style>
