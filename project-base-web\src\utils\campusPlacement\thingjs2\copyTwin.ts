import { useSceneStore } from '@/store/scene';
import axios from 'axios';
/*
 * @Description: 单个或者批量复制孪生体
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-21 15:01:13
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-26 17:55:36
 */
let copyMouseWheel: any = null; // 鼠标滚轮滚动事件-处理无法移除问题
export default class CopyTwin {
  app: THING.App;

  copyTarget: any = null; // 正在复制的孪生体

  maxCount = 100; // 批量复制最大数量

  interval = 0; // 批量复制间距

  targetOldAngles: any = null;

  batchObject: THING.BaseObject | null; // 批量复制对象

  pressShift = false; // 是否按下shift键

  pressAlt = false; // 是否按下alt键

  pressCtrl = false; // 是否按下ctrl键

  startPos: any; // 复制对象的初始位置三维向量

  endPos: any; // 鼠标最终位置 用于计算并创建点位

  copyAngle = 0; // 旋转角度

  copyTargetSize = 0; // 用于计算的宽度

  copyArray: any[] = []; // 批量复制创建的对象-实时变化

  boundingBoxSize: any; // 包围盒大小

  bacthPromise: any = [];

  batchCb: Function | undefined = undefined; // 批量复制回调

  dragDownFlag = false;

  dragMoveFlag = false;

  constructor(app: THING.App) {
    this.app = app;
    this.batchObject = null;
  }
  /**
   *复制当前孪生体-单个
   *
   * @memberof CreateTwin
   */
  copyOne(object: any, cb?: Function) {
    const that = this;
    this.copyTarget = object;
    const userData = object.userData;
    if (userData.dataType === 'POINT' && userData.thingsModelType === 'Particles') {
      this.createCopyParticle(object, cb);
    } else if (userData.dataType === 'LINE') {
      this.createCopyLine(object, cb);
    } else if (userData.dataType === 'SURFACE') {
      this.createCopyRegion(object, cb);
    } else {
      this.copyEntityOne(object, cb);
    }
  }
  /**
   *复制当前孪生体-单个/打组对象
   *
   * @memberof CreateTwin
   */
  async copyEntityOne(object: any, cb?: Function) {
    const data: any = object.userData;
    const position = [object.position[0] - object.orientedBox.size[0], object.position[1], object.position[2]];
    // // @ts-ignore
    // // const copyObj = new THING.Object3D();
    // await copyObj.copy(object);
    // copyObj.parent = object.parent;
    // copyObj.children = object.children;
    // copyObj.position = position;
    // copyObj.userData = {
    //   ...data,
    //   placeType: 'new', //  点位摆放操作类型标识
    // };
    const copyObj = object.clone(true, object.parent);
    copyObj.position = position;
    copyObj.userData = {
      ...data,
      placeType: 'new', //  点位摆放操作类型标识
    };
    if (copyObj.type === 'GroupThing') {
      copyObj.children.forEach((child: any) => {
        child.userData.placeType = 'new';
      });
    }
    copyObj.helper.orientedBox.visible = false;
    copyObj.pickable = false;
    this.copyTarget = null;
    cb && cb(copyObj);
  }
  /**
   *复制单个粒子
   *
   * @param {} object
   * @param {Function} [cb]
   * @memberof CreatePointTwin
   */
  createCopyParticle(object: any, cb?: Function) {
    const that = this;
    const tenantCode = axios.defaults.headers.common['Tenant'] || 'master';

    const data = object.userData;
    // @ts-ignore
    const position = [object.position[0] - object.orientedBox.size[0], object.position[1], object.position[2]];
    this.app.create({
      name: 'customPlaceTwin',
      type: 'ParticleSystem',
      url: `${`${window.baseConfig.previewResourceUrl}${tenantCode}/model/${data.thingsModelUuid}`}/0/gltf/`,
      angles: object.angles,
      scale: object.scale,
      userData: {
        ...data,
        placeType: 'new', //  点位摆放操作类型标识
      },
      position,
      parent: object.parent,
      complete(ev: any) {
        ev.object.pickable = false;
        that.copyTarget = null;
        if (cb) {
          cb(ev.object);
        }
      },
    });
  }

  /**
   *复制单个线
   *
   * @param {} object
   * @param {Function} [cb]
   * @memberof CopyTwin
   */
  createCopyLine(object: any, cb?: Function) {
    const that = this;
    // @ts-ignore
    const position = [object.position[0] - object.orientedBox.size[0], object.position[1], object.position[2]];
    this.app.create({
      name: 'customPlaceTwin',
      type: object.type,
      scale: object.scale,
      angles: object.angles,
      points: object.points,
      width: object.width,
      radius: object.width,
      style: {
        // @ts-ignore
        color: object.style.color,
        // @ts-ignore
        opacity: object.style.opacity,
      },
      userData: {
        ...object.userData,
        placeType: 'new', // 摆点点位类型标识
      },
      parent: object.parent,
      complete: (ev: any) => {
        ev.object.position = position;
        ev.object.pickable = false;
        that.copyTarget = null;
        if (cb) {
          cb(ev.object);
        }
      },
    });
  }

  /**
   *复制单个面
   *
   * @param {} object
   * @param {Function} [cb]
   * @memberof CopyTwin
   */
  createCopyRegion(object: any, cb?: Function) {
    const that = this;
    // @ts-ignore
    const position = [object.position[0] - object.orientedBox.size[0], object.position[1], object.position[2]];
    const copyRegion = this.app.create({
      name: 'customPlaceTwin',
      type: object.type,
      scale: object.scale,
      angles: object.angles,
      points: object.points,
      userData: {
        ...object.userData,
        placeType: 'new', // 摆点点位类型标识
      },
      style: {
        color: object.style.color,
        opacity: object.style.opacity,
      },
      parent: object.parent,
      complete: (ev: any) => {
        ev.object.position = position;
        ev.object.pickable = false;
        that.copyTarget = null;
        if (cb) {
          cb(ev.object);
        }
      },
    });
    // @ts-ignore
    new THING.PixelLine({
      name: 'tempRegionLine',
      points: object.points,
      style: {
        color: object.children[0].style.color || '#FFFFFF',
        opacity: object.children[0].style.opacity || 1,
      },
      closure: true,
      parent: copyRegion,
      complete(e: any) {
        e.object.pickable = false;
      },
    });
  }
  /**
   *开启批量复制
   *
   * @memberof CopyTwin
   */
  beginBatchCopy(obj: any, cb?: Function) {
    const sceneStore = useSceneStore();
    sceneStore.batchCopyFlag = true;
    // 保存信息
    this.copyTarget = obj;
    this.batchCb = cb;
    this.targetOldAngles = this.copyTarget.angles;
    this.boundingBoxSize = JSON.parse(JSON.stringify(this.copyTarget.boundingBox.size));
    // 位置转三维向量并存储
    this.startPos = [this.copyTarget.position[0], this.copyTarget.position[1], this.copyTarget.position[2]];
    // 销毁批量创建孪生体
    if (this.copyArray.length) {
      this.copyArray.forEach((item: any) => {
        item.destroy();
      });
      this.copyArray = [];
    }
    // 初始化对象本身用于计算宽度
    this.refreshCopyTargetSize();
    // 初始化批量复制监听
    this.initBatchListener();
  }

  /**
   *初始化批量复制监听
   *
   * @memberof CopyTwin
   */
  initBatchListener() {
    copyMouseWheel = this.batchMouseWheel.bind(this);
    this.app.on(THING.EventType.MouseMove, this.batchMouseMove.bind(this), 'batchCopy');
    this.app.on(THING.EventType.MouseUp, this.batchMouseUp.bind(this), 'batchCopy');
    // @ts-ignore
    this.app.container.addEventListener('wheel', copyMouseWheel);
    this.app.on(THING.EventType.KeyDown, this.batchKeyDown.bind(this), 'batchcopy');
    this.app.on(THING.EventType.KeyUp, this.batchKeyUp.bind(this), 'batchcopy');
    this.initDragListener();
  }

  // 场景拖拽平移监听
  initDragListener() {
    this.app.on(
      THING.EventType.MouseDown,
      () => {
        this.dragDownFlag = true;
        this.app.on(
          THING.EventType.MouseMove,
          () => {
            if (this.dragDownFlag) {
              this.dragMoveFlag = true;
            } else {
              this.dragDownFlag = false;
            }
          },
          'batchDragListener'
        );
      },
      'batchDragListener'
    );
  }

  // 移除场景拖拽平移监听
  removeDragListener() {
    this.dragDownFlag = false;
    this.dragMoveFlag = false;
    this.app.off(THING.EventType.MouseDown, null, 'batchDragListener');
    this.app.off(THING.EventType.MouseMove, null, 'batchDragListener');
  }

  /**
   *移除批量复制监听
   *
   * @memberof CopyTwin
   */
  removeBatchListener() {
    this.app.off(THING.EventType.MouseMove, null, 'batchCopy');
    this.app.off(THING.EventType.MouseUp, null, 'batchCopy');
    // @ts-ignore
    if (copyMouseWheel) {
      // @ts-ignore
      this.app.container.removeEventListener('wheel', copyMouseWheel);
      copyMouseWheel = null;
    }
    this.app.off(THING.EventType.KeyDown, null, 'batchcopy');
    this.app.off(THING.EventType.KeyUp, null, 'batchcopy');
    this.removeDragListener();
  }

  /**
   *批量复制移动监听
   *
   * @param {*} e
   * @memberof CopyTwin
   */
  batchMouseMove(e: any) {
    const constant = -THING.Math.dotVector([0, 1, 0], [0, this.copyTarget.position[1], 0]);
    // @ts-ignore
    const worldPos = this.app.camera.intersectPlane(e.x, e.y, [0, 1, 0], constant);
    if (worldPos) {
      this.endPos = [worldPos[0], worldPos[1], worldPos[2]];
      // 刷新提示框位置
      this.refreshCopyDom(e.x, e.y);
      // 刷新批量复制点位
      this.refreshCopyObject();
    }
  }

  /**
   *批量复制鼠标滚动监听
   *
   * @param {*} e
   * @memberof CopyTwin
   */
  batchMouseWheel(e: any) {
    if (this.pressCtrl || this.pressShift) {
      e.preventDefault();
      const wheel = e.wheelDelta || -e.detail;
      const delta = Math.max(-1, Math.min(1, wheel));
      let temp = this.copyTarget.orientedBox.size[0];
      temp = this.copyTarget.scale[0];
      this.interval += delta * 0.1 * temp;
      if (this.interval < 0) {
        this.interval = 0;
      }
      this.refreshCopyDom(e.layerX, e.layerY);
      this.refreshCopyObject();
    }
  }
  /**
   *批量复制按键按下
   *
   * @param {*} e
   * @memberof CopyTwin
   */
  batchKeyDown(e: any) {
    this.pressShift = e.shiftKey;
    this.pressAlt = e.altKey;
    this.pressCtrl = e.ctrlKey;
    if (this.pressShift || this.pressAlt || this.pressCtrl) {
      // 禁止三维操作
      // @ts-ignore
      this.app.camera.enable = false;
    }
  }

  /**
   *批量复制按键放开
   *
   * @param {*} e
   * @memberof CopyTwin
   */
  batchKeyUp(e: any) {
    this.pressShift = e.shiftKey;
    this.pressAlt = e.altKey;
    this.pressCtrl = e.ctrlKey;
    // 恢复三维操作
    // @ts-ignore
    this.app.camera.enable = true;
  }

  /**
   *计算并刷新批量复制点位
   *
   * @memberof CopyTwin
   */
  refreshCopyObject() {
    this.endPos[1] = this.startPos[1];
    // 实时计算copyTargetSize
    const calcSize = this.refreshTargetSize();
    this.copyTargetSize = calcSize ? Math.abs(this.boundingBoxSize[0]) : Math.abs(this.boundingBoxSize[2]);

    const dir = THING.Math.normalizeVector(THING.Math.subVector(this.endPos, this.startPos));
    // 刷新起点
    const reStart = THING.Math.addVector(this.startPos, THING.Math.scaleVector(dir, this.copyTargetSize / 2));
    let distance = THING.Math.getDistance(reStart, this.endPos);
    // 计算数量
    let count = Math.abs(Math.ceil(distance / (this.copyTargetSize + this.interval))); // + 1
    if (count > this.maxCount) {
      count = this.maxCount;
    }
    // 刷新终点位置
    const reEnd = THING.Math.addVector(reStart, THING.Math.scaleVector(dir, count * (this.copyTargetSize + this.interval)));
    // 从新计算距离
    distance = THING.Math.getDistance(reStart, reEnd);

    // 创建对象 多退少补
    if (this.copyArray.length < count) {
      const needs = count - this.copyArray.length;
      const targetScale = this.copyTarget.scale;
      for (let i = 0; i < needs; i++) {
        // @ts-ignore
        const obj = this.copyTarget.clone();
        obj.scale = [Math.abs(targetScale[0]), Math.abs(targetScale[1]), Math.abs(targetScale[2])];
        const userData = {
          ...this.copyTarget.userData,
          placeType: 'new',
        };
        obj.userData = userData;
        obj.helper.orientedBox.visible = false;
        obj.pickable = false;
        this.copyArray.push(obj);
      }
    } else if (this.copyArray.length > count) {
      const copyCount = this.copyArray.length;
      for (let i = count; i < copyCount; i++) {
        this.copyArray[i].destroy();
      }
      this.copyArray.splice(count, copyCount - count);
    }
    // 刷新变换-复制对象跟随鼠标旋转
    const halfSize = this.copyTargetSize / 2;
    for (let i = 0; i < this.copyArray.length; i++) {
      const obj = this.copyArray[i];
      const percent = ((i + 1) * (this.copyTargetSize + this.interval) - halfSize) / distance; // + 1
      // @ts-ignore
      const curPos = THING.Math.lerpVector(reStart, reEnd, percent);
      obj.position = curPos;
      obj.angles = this.copyTarget.angles;
    }
    return count;
  }

  /**
   *计算copyTargetSize
   *
   * @return {*}
   * @memberof CopyTwin
   */
  refreshTargetSize() {
    const startV3 = [this.startPos[0], this.startPos[1], this.startPos[2]];
    const endV3 = [this.endPos[0], this.endPos[1], this.endPos[2]];
    const subV3 = THING.Math.subVector(startV3, endV3);
    const subX = Math.abs(subV3[0]);
    const subZ = Math.abs(subV3[2]);
    return Math.abs(subX / this.boundingBoxSize[0]) > Math.abs(subZ / this.boundingBoxSize[2]);
  }

  // 刷新复制对象用于计算的大小
  refreshCopyTargetSize() {
    if (this.copyAngle % 180 !== 0) {
      this.copyTargetSize = this.copyTarget.orientedBox.size[2];
    } else {
      this.copyTargetSize = this.copyTarget.orientedBox.size[0];
    }
    this.copyTargetSize = Math.abs(this.copyTargetSize);
  }

  /**
   *获取垂直位置
   *
   * @param {*} curPos
   * @return {*}
   * @memberof CopyTwin
   */
  getStraightPostion(curPos: any) {
    const result = curPos;
    const x = [this.startPos[0], curPos[1], curPos[2]];
    const xDis = THING.Math.getDistance(x, this.startPos);
    const z = [curPos[0], curPos[1], this.startPos[2]];
    const zDis = THING.Math.getDistance(z, this.startPos);
    if (xDis > zDis) {
      result.x = this.startPos[0];
    } else {
      result.z = this.startPos[2];
    }
    return result;
  }

  /**
   *批量复制单个
   *
   * @param {THING.BaseObject} obj
   * @memberof CopyTwin
   */
  async batchCopyOne(object: any, parent: THING.BaseObject) {
    const data: any = object.userData;
    // @ts-ignore
    const copyObj = new THING.Object3D();
    await copyObj.copy(this.copyTarget);
    copyObj.position = object.position;
    copyObj.angels = object.angels;
    copyObj.parent = parent;
    copyObj.userData = {
      ...data,
      placeType: 'new', //  点位摆放操作类型标识
    };
    if (copyObj.type === 'GroupThing') {
      copyObj.children.forEach((child: any) => {
        child.userData.placeType = 'new';
      });
    }
    copyObj.helper.orientedBox.visible = false;
    copyObj.pickable = false;
  }

  /**
   *批量复制抬起监听
   *
   * @param {*} e
   * @memberof CopyTwin
   */
  batchMouseUp(e: any) {
    const that = this;
    // 场景拖拽-返回
    this.dragDownFlag = false;
    if (this.dragMoveFlag) {
      this.dragMoveFlag = false;
      return;
    }
    // 关闭提示
    const sceneStore = useSceneStore();
    sceneStore.batchCopyFlag = false;
    if (e.button === 0) {
      // 左键抬起
      if (that.copyArray.length) {
        const center = Math.floor(that.copyArray.length / 2);
        // 创建复制组
        this.app.create({
          name: 'customPlaceTwin',
          type: 'GroupThing',
          position: that.copyArray[center].position,
          parent: this.copyTarget.parent,
          userData: {
            ...this.copyTarget.userData,
            placeType: 'new',
          },
          complete: (e: any) => {
            that.copyArray.forEach((obj: any) => {
              that.bacthPromise.push(that.batchCopyOne(obj, e.object));
            });
            Promise.all(that.bacthPromise)
              .then(() => {
                that.bacthPromise = [];
                if (that.batchCb) {
                  that.batchCb(e.object);
                }
                that.reset();
              })
              .catch(() => {
                that.bacthPromise = [];
                if (that.batchCb) {
                  that.batchCb();
                }
                that.reset();
              });
          },
        });
      } else {
        that.reset();
      }
    } else if (e.button === 2) {
      if (that.batchCb) {
        that.batchCb();
      }
      // 重置
      this.reset();
    }
  }

  /**
   *还原批量复制
   *
   * @memberof CopyTwin
   */
  reset() {
    // 移除监听事件
    this.removeBatchListener();
    // 隐藏提示
    this.hideCopyDom();
    this.copyTarget = null; // 正在复制的孪生体
    this.maxCount = 100; // 批量复制最大数量
    this.interval = 0; // 批量复制间距
    this.targetOldAngles = null;
    if (this.copyArray.length) {
      this.copyArray.forEach((item: any) => {
        if (!item.isDestroyed) {
          item.destroy();
        }
      });
    }
    this.copyArray = []; // 批量复制创建的对象-实时变化
    this.pressShift = false; // 是否按下shift键
    this.pressAlt = false; // 是否按下alt键
    this.pressCtrl = false; // 是否按下ctrl键
    this.startPos = null; // 复制对象的初始位置三维向量
    this.endPos = null; // 鼠标最终位置 用于计算并创建点位
    this.copyAngle = 0; // 旋转角度
    this.copyTargetSize = 0; // 用于计算的宽度
    this.boundingBoxSize = 0; // 包围盒大小
    this.batchCb = undefined; // 批量复制回调
  }

  // 创建跟随鼠标dom-用于提示批量复制个数
  refreshCopyDom(x: number, y: number) {
    const batchPanel = document.getElementById('batch-copy-panel');
    const innerDom = `<span style="margin-right: 6px">个数:${this.copyArray.length}</span><span>间距:${this.interval.toFixed(1)}</span>`;
    if (batchPanel) {
      batchPanel.style.display = 'block';
      batchPanel.style.left = `${x + 20}px`;
      batchPanel.style.top = `${y}px`;
      batchPanel.innerHTML = innerDom;
    } else {
      const outDiv = document.createElement('div');
      outDiv.style.pointerEvents = 'none';
      outDiv.style.userSelect = 'none';
      outDiv.id = 'batch-copy-panel';
      outDiv.style.position = 'absolute';
      outDiv.style.left = `${x + 20}px`;
      outDiv.style.top = `${y}px`;
      outDiv.style.background = '#F7F8FA';
      outDiv.style.color = '#1D1F24';
      outDiv.style.padding = '4px';
      outDiv.style.borderRadius = '4px';
      outDiv.innerHTML = innerDom;
      // @ts-ignore
      this.app.container.append(outDiv);
    }
  }

  // 隐藏批量复制提示dom
  hideCopyDom() {
    const batchPanel = document.getElementById('batch-copy-panel');
    if (batchPanel) {
      batchPanel.style.display = 'none';
    }
  }
}
