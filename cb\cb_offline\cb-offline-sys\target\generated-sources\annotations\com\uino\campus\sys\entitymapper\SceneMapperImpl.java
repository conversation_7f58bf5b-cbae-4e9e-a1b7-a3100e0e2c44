package com.uino.campus.sys.entitymapper;

import com.uino.campus.sys.infrastructure.dto.scene.SceneDto;
import com.uino.campus.sys.infrastructure.entity.scene.Scene;
import java.util.Optional;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T09:40:36+0800",
    comments = "version: 1.5.3.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class SceneMapperImpl implements SceneMapper {

    @Override
    public Scene updateSceneFromSceneDto(SceneDto sceneDto, Scene scene) {
        if ( sceneDto == null ) {
            return scene;
        }

        scene.setName( sceneDto.getName() );
        if ( sceneDto.getType() != null ) {
            scene.setType( sceneDto.getType() );
        }

        scene.setIsTemp( Optional.ofNullable(sceneDto.getIsTemp()).orElse(0) );

        return scene;
    }
}
