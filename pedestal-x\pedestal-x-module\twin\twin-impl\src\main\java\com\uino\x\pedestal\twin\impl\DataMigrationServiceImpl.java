package com.uino.x.pedestal.twin.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.uino.x.common.cache.CacheRefresh;
import com.uino.x.common.core.util.ZipUtils;
import com.uino.x.common.datasource.util.DataSourceConfigurationUtils;
import com.uino.x.common.datasource.util.IgnoreInfo;
import com.uino.x.common.datasource.util.SqlUtils;
import com.uino.x.common.datasource.util.TenantDataSource;
import com.uino.x.common.label.annotation.PackageFlag;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.exception.GenericException;
import com.uino.x.common.objectstorage.response.UploadObjectResponse;
import com.uino.x.common.objectstorage.template.ObjectStorageTemplate;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.sql.maker.SchemaSql;
import com.uino.x.common.sql.maker.SchemaSqlFactory;
import com.uino.x.common.sql.maker.TableSql;
import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.base.CollectionUtils;
import com.uino.x.common.tool.base.DateUtils;
import com.uino.x.common.tool.base.ThrowUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.effectpackage.common.utils.PlacementDownloadUtils;
import com.uino.x.pedestal.file.common.domain.FileAsyInfo;
import com.uino.x.pedestal.file.common.properties.DownloadProperties;
import com.uino.x.pedestal.file.common.util.AsyncFileTask;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.api.SysUserApi;
import com.uino.x.pedestal.identity.dao.mapper.SysUserMapper;
import com.uino.x.pedestal.identity.pojo.entity.SysUser;
import com.uino.x.pedestal.message.api.RealTimeMessageApi;
import com.uino.x.pedestal.message.pojo.entity.SysRealTimeMessage;
import com.uino.x.pedestal.tenant.api.TenantInfoApi;
import com.uino.x.pedestal.tenant.common.enums.DataMigrationDifferenceOperation;
import com.uino.x.pedestal.tenant.common.enums.DynamicBusinessTable;
import com.uino.x.pedestal.tenant.common.exception.DataMigrationException;
import com.uino.x.pedestal.tenant.dao.mapper.DataMigrationMapper;
import com.uino.x.pedestal.tenant.migrate.AbstractDataMigrationService;
import com.uino.x.pedestal.tenant.migrate.TenantMigrationInfo;
import com.uino.x.pedestal.tenant.migrate.factory.TenantMigrationFactory;
import com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties;
import com.uino.x.pedestal.tenant.pojo.domain.DataMigrationMetaInfo;
import com.uino.x.pedestal.tenant.pojo.domain.DynamicForm;
import com.uino.x.pedestal.tenant.pojo.entity.TenantInfo;
import com.uino.x.pedestal.tenant.pojo.vo.DataMigrationDifferenceDetailVo;
import com.uino.x.pedestal.tenant.pojo.vo.DataMigrationDifferenceVo;
import com.uino.x.pedestal.twin.common.utils.FormUtils;
import com.uino.x.pedestal.twin.dao.mapper.TwinClassMapper;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.flyway.FlywayProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.uino.x.pedestal.common.utils.ProximabPathUtil.SEP;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/7 17/20/57
 * @description
 */
@Service("twinDataMigrationSvc")
@Slf4j
@EnableConfigurationProperties(DataMigrateProperties.class)
public class DataMigrationServiceImpl extends AbstractDataMigrationService {

    private static final String TWIN_X = DataSourceConstant.TWIN_X;
    public final DataMigrateProperties dataMigrateProperties;
    private final SysUserMapper sysUserMapper;
    private final SysUserApi sysUserApi;
    private final ObjectStorageTemplate ost;
    private final DataMigrationMapper dataMigrationMapper;
    private final RealTimeMessageApi realTimeMessageService;
    private final CacheRefresh cacheRefresh;
    private final TenantInfoApi tenantInfoService;
    private final FlywayProperties flywayProperties;
    private final String moduleName;

    public DataMigrationServiceImpl(DataMigrateProperties dataMigrateProperties, SysUserMapper sysUserMapper, SysUserApi sysUserApi, ObjectStorageTemplate ost, DataMigrationMapper dataMigrationMapper, RealTimeMessageApi realTimeMessageService, CacheRefresh cacheRefresh, TenantInfoApi tenantInfoService, FlywayProperties flywayProperties) {
        super.dataMigrateProperties = dataMigrateProperties;
        this.dataMigrateProperties = dataMigrateProperties;
        this.sysUserMapper = sysUserMapper;
        this.sysUserApi = sysUserApi;
        this.ost = ost;
        this.dataMigrationMapper = dataMigrationMapper;
        this.realTimeMessageService = realTimeMessageService;
        this.cacheRefresh = cacheRefresh;
        this.tenantInfoService = tenantInfoService;
        this.flywayProperties = flywayProperties;
        this.moduleName = TwinClassMapper.class.getPackage().getAnnotation(PackageFlag.class).module();
    }

    @Override
    public String doExportData(String tenantCode, String tag, Boolean businessData, Boolean flag) {
        log.info("---------------- start doExportData  twin ------------------------");
        String tenantName = getTenantName(tenantCode);
        Long loginUserId = null;
        if (flag) {
            loginUserId = IdentityContext.getSysLoginUserId();
        }
        final String businessDataMessage = businessData ? "" : "（不包含业务）";
        final String exportFilePath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        String zipFileName = moduleName + "_" + tenantCode + "_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
        String tempPath = fileAsyInfo.getTempPath();
        final TenantDataSource twinX = DataSourceConfigurationUtils.newTenantDataSource(tenantCode.toLowerCase(), TWIN_X);
        final DataMigrationMetaInfo dataMigrationMetaInfo = new DataMigrationMetaInfo();
        dataMigrationMetaInfo.setTag(tag);
        try (twinX) {
            final Map<String, List<IgnoreInfo>> ignoreInfoMap = dataMigrateProperties.getIgnoreInfoMap();
            // 构建basex忽略信息, 默认忽略tenant_info所有信息, 否则忽略定制的相关内容
            final List<IgnoreInfo> twinIgnoreInfoList = /*businessData ? Lists.newArrayList() :*/ Optional.ofNullable(ignoreInfoMap.get(moduleName)).orElse(new ArrayList<>());
            twinIgnoreInfoList.addAll(Arrays.asList(IgnoreInfo.of("undo_log"), IgnoreInfo.of("flyway_schema_history")));

            // 获取插件相关的sql文件
            final List<File> pluginAppendFiles = getPluginAppendFile(exportFilePath);

            // 1.版本号为当前最新版本直接导出
            final String currentTag = dataMigrateProperties.getTag();
            final IgnoreInfo[] twinIgnoreInfos = twinIgnoreInfoList.toArray(IgnoreInfo[]::new);
            final SchemaSql twinSchemaSql;
            if (currentTag.equals(tag)) {
                twinSchemaSql = SqlUtils.exportSchema(twinX, twinX.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, true, pluginAppendFiles, twinIgnoreInfos);
            } else {
                // 2.版本号非最新版本,从临时库导出
                twinSchemaSql = operateTempDatabase(tenantCode, tag, businessData, tenantName, loginUserId, exportFilePath, tempPath, twinX, businessDataMessage, currentTag, twinIgnoreInfoList, pluginAppendFiles);
            }
            log.info("----------------twin operate temp database success {}------------------------", currentTag);
            // 准备迁移元数据信息
            // 只有在不需要业务数据的情况下才构建 表单 元信息
            if (!businessData) {
                // 获取实际忽略的TableSql
                final List<TableSql> tableSqlList = ignoreTableSqlList(twinIgnoreInfoList, twinSchemaSql);
                // 查询对应的form并构建 表单 元信息
                final List<DynamicForm> dynamicFormList = getTableFormList(tenantCode.toLowerCase(), tableSqlList);
                dataMigrationMetaInfo.setDynamicFormList(dynamicFormList);
            }
            // 兼容没有可选业务数据的低版本 tag.txt文件
            if (tag.compareTo(dataMigrateProperties.getVersionBaseline().getBusinessData()) < 0) {
                FileUtils.writeStringToFile(new File(tempPath + SEP + "tag.txt"), tag, StandardCharsets.UTF_8, true);
            }
            FileUtils.writeStringToFile(new File(tempPath + SEP + MIGRATION_META_INFO_FILE_NAME), SqlUtils.encode(true, JSON.toJSONString(dataMigrationMetaInfo)), StandardCharsets.UTF_8, true);
            String buildFileName = fileAsyInfo.getZipPath() + PlacementDownloadUtils.SEP + zipFileName;
            ZipUtils.zipUsePass(tempPath, buildFileName, dataMigrateProperties.getPassword());
            String filmId = DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate());
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            final UploadObjectResponse uploadObjectResponse = ost.uploadObject(DownloadProperties.getTempPath(), filmId + "/" + zipFileName, buildFileName);
            log.info("租户打包孪生体数据资源完成");
            return uploadObjectResponse.getObject();
        } catch (Exception e) {
            e.printStackTrace();
            throw ThrowUtils.getThrow().internalServerError(String.format("打包孪生体【%s】系统数据%s%s版本失败", tenantName, businessDataMessage, tag), e);
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
                FileUtils.deleteDirectory(new File(exportFilePath));
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
    }

    @Override
    public JSONArray doImportData(String tenantCode, byte[] bytes, String fileName) {
        final String tenantName = getTenantName(tenantCode);
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        String filePath = tempPath.getAbsolutePath() + SEP;
        String fileUrl = filePath + fileName;
        File file = new File(fileUrl);
        try {
            FileUtils.writeByteArrayToFile(file, bytes);
        } catch (IOException io) {
            throw ThrowUtils.getThrow().internalServerError("读取文件失败", io);
        }

        String fileDir = filePath + fileName.substring(0, fileName.lastIndexOf("."));
        File unzipFile = new File(fileDir);
        try {
            FileUtils.forceMkdir(unzipFile);
        } catch (IOException e) {
            throw ThrowUtils.getThrow().internalServerError("文件创建失败", e);
        }
        String bakPath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        final String currentTag = dataMigrateProperties.getTag();

        final Map<String, List<IgnoreInfo>> ignoreInfoMap = dataMigrateProperties.getIgnoreInfoMap();
        final List<IgnoreInfo> twinIgnoreInfoList = ignoreInfoMap.get(moduleName);
        try {
            ZipUtils.unzip(fileUrl, fileDir, dataMigrateProperties.getPassword());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("数据文件解压失败", e);
        }
        File metaInfoFile = new File(unzipFile.getPath() + File.separator + MIGRATION_META_INFO_FILE_NAME);
        AssertUtils.isTrue(metaInfoFile.exists(), HttpStatus.BAD_REQUEST, "请上传有效的租户数据迁移文件");
        DataMigrationMetaInfo dataMigrationMetaInfo;
        final TenantDataSource twinXDataSource = DataSourceConfigurationUtils.newTenantDataSource(tenantCode.toLowerCase(), TWIN_X);
        final String twinXSchema = twinXDataSource.getSchema();
        try {
            final String metaInfoStr = FileUtils.readFileToString(metaInfoFile, StandardCharsets.UTF_8);
            final String metaInfoJson = SqlUtils.decode(true, metaInfoStr);
            dataMigrationMetaInfo = JSON.parseObject(metaInfoJson, DataMigrationMetaInfo.class);
            final List<DynamicForm> newDynamicFormList = dataMigrationMetaInfo.getDynamicFormList();
            final String importTag = dataMigrationMetaInfo.getTag();
            AssertUtils.isTrue(currentTag.equals(importTag), ThrowUtils.getThrow().badRequest(String.format("导入的数据版本与当前版本不一致, %s -> %s", importTag, currentTag)));
            if (CollectionUtils.isNotEmpty(newDynamicFormList)) {
                // 获取当前auto_api的SchemaSql
                final List<TableSql> tableSqlList;
                try (SchemaSql schemaSql = SchemaSqlFactory.get(twinXDataSource, twinXSchema)) {
                    // 获取实际忽略的TableSql
                    tableSqlList = ignoreTableSqlList(twinIgnoreInfoList, schemaSql);
                }
                final List<DynamicForm> originDynamicFormList = getTableFormList(tenantCode.toLowerCase(), tableSqlList);
                // 对比动态表单信息
                final List<DataMigrationDifferenceVo> dataMigrationDifferenceVoList = getDataMigrationDifference(originDynamicFormList, newDynamicFormList);
                // 校验不为空返回
                if (CollectionUtils.isNotEmpty(dataMigrationDifferenceVoList)) {

                    return JSONArray.parseArray(JSON.toJSONString(dataMigrationDifferenceVoList));
                }
            }
        } catch (GenericException e) {
            throw e;
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("数据合并元信息获取失败", e);
        }
        File bakFile = new File(bakPath);
        try {
            //备份租户数据
            final boolean bakFlag = bakData(twinXDataSource, bakPath);
            AssertUtils.isTrue(bakFlag, ThrowUtils.getThrow().serviceUnavailable(String.format("租户【%s】系统数据备份失败, 版本: %s", tenantName, currentTag)));
            List<File> sqlFileList = sortAppendFile(unzipFile);
            if (CollectionUtils.isEmpty(sqlFileList)) {
                throw new DataMigrationException(String.format("租户【%s】系统数据文件不存在, 版本: %s", tenantName, currentTag));
            }
            log.info("开始数据迁移");
            SqlUtils.importSchema(twinXDataSource, sqlFileList, true, sql ->{
                // 检查SQL是否包含deleted_前缀的表，这个回调中的sql是逐行，对于换行的情况下谨慎
//                if (sql.toLowerCase().contains("deleted_")) {
//                    // 直接返回空字符串，因为我们已经改为直接删除表
//                    return "";
//                }
                return transToSchemaSql(sql, twinXSchema, TWIN_X);
            });
            log.info("开始刷新缓存");
            cacheRefresh.refresh();
        } catch (Exception e) {
            try {
                log.warn("开始数据回滚");
                List<File> sqlFileList = sortAppendFile(bakFile);
                SqlUtils.importSchema(twinXDataSource, sqlFileList, true, sql -> transToSchemaSql(sql, twinXSchema, TWIN_X));
                cacheRefresh.refresh();
            } catch (Exception e1) {
                log.error("数据回滚失败", e1);
            }
            throw new DataMigrationException(String.format("租户【%s】系统数据迁移失败, 版本: %s", tenantName, currentTag), e);
        } finally {
            try {
                FileUtils.deleteQuietly(file);
                FileUtils.deleteDirectory(unzipFile);
                FileUtils.deleteQuietly(bakFile);
            } catch (IOException e) {
                log.error("Delete temp file fail", e);
            }
            DataSourceConfigurationUtils.closeTenantDataSources(twinXDataSource);
        }
        return new JSONArray();
    }

    @Override
    public String doExportFile(String tenantCode) {
        return null;
    }

    @Override
    public void doImportFile(String code, byte[] bytes, String fileName) {

    }

    /**
     * 根据租户编号初始化该租户的数据源
     *
     * <AUTHOR>
     * @date 2023/4/13 15:18
     */
    @Override
    public void createDatabase(String name, String code, Integer migrationType) {
        try {
            code = code.toLowerCase();
            dataMigrationMapper.createSchemaByTenantCode(code, DataSourceConstant.TWIN_X);
            // 根据租户编码新建数据源
            DataSourceProperty currentDataSourceProperty = DataSourceConfigurationUtils.getCurrentDataSourceProperty();
            currentDataSourceProperty.setSeata(false);
            DefaultDataSourceCreator dataSourceCreator = SpringIocUtils.mustGetBean(DefaultDataSourceCreator.class);
            try (TenantDataSource dataSource = DataSourceConfigurationUtils.newTenantDataSource(currentDataSourceProperty, dataSourceCreator, code, DataSourceConstant.TWIN_X)) {
                // 获取数据源,迁移信息
                final TenantMigrationInfo tenantMigrationInfo = new TenantMigrationInfo();
                tenantMigrationInfo.setTargetTenant(code);
                tenantMigrationInfo.setMigrationType(migrationType);
                tenantMigrationInfo.setName(name);
                tenantMigrationInfo.setSourceType(DataSourceConstant.TWIN_X);
                tenantMigrationInfo.setCover(true);
                tenantMigrationInfo.setAllowMigrationDataTable(dataMigrateProperties.getAllowMigrationDataTable());
                // 进行租户迁移
                TenantMigrationFactory.of(dataSource, tenantMigrationInfo).get().migrate();
            }
        } catch (Exception e) {
            ThrowUtils.getThrow().internalServerError(String.format("初始化租户【%s】孪生体数据源失败", code));
        }
    }

    /**
     * 获取动态表单列表
     *
     * @param tableSqlList TableSql列表
     * @return 动态表单列表
     * <AUTHOR>
     * @date 2022/10/18 16:32
     */
    private List<DynamicForm> getTableFormList(String tenantCode, List<TableSql> tableSqlList) {

        final String tenantDs = tenantCode + "_" + TWIN_X;
        return tableSqlList.stream().map(sqlMaker -> {
            final String table = sqlMaker.getName();
            final String tableName = StringUtils.strip(table, "`");
            final Optional<DynamicBusinessTable> dynamicBusinessTableOp = DynamicBusinessTable.nameOf(tableName);
            if (dynamicBusinessTableOp.isEmpty()) {
                log.warn("当前表不是动态表: {}", tableName);
                return null;
            }
            final DynamicBusinessTable dynamicBusinessTable = dynamicBusinessTableOp.get();
            final String businessTablePrefix = dynamicBusinessTable.getPrefix();
            final String businessTableName = dynamicBusinessTable.getName();
            final String code = tableName.replace(businessTablePrefix, "").toUpperCase();
            final String name;
            DynamicDataSourceContextHolder.push(tenantDs);
            try {
                name = dataMigrationMapper.selectDynamicName(businessTableName, code);
            } finally {
                DynamicDataSourceContextHolder.poll();
            }
            if (StringUtils.isBlank(name)) {
                log.warn("当前动态表没有业务记录: {}->{}", businessTableName, code);
                return null;
            }
            final String form;
            DynamicDataSourceContextHolder.push(tenantDs);
            try {
                form = dataMigrationMapper.selectDynamicForm(businessTableName, code);
            } finally {
                DynamicDataSourceContextHolder.poll();
            }
            final List<FormItem> formItems = FormUtils.parseListToForm(form);
            return new DynamicForm(name, code, formItems);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 操作临时数据库
     *
     * @param tenantName          租户名称
     * @param loginUserId         当前登录用户id
     * @param exportFilePath      导出的sql文件随机目录 data/export/uuid/
     * @param tempPath            导出的sql文件随机临时目录 data/export/uuid/temp
     * @param twinX               basex数据源
     * @param businessDataMessage 消息体
     * @param currentTag          当前版本
     * @param twinXIgnoreInfoList basex 忽略导出列表
     * @return 数据库schemaSql
     * <AUTHOR>
     * @date 2022/11/9 18:39
     */
    private SchemaSql operateTempDatabase(String tenantCode,
                                          String tag,
                                          Boolean businessData,
                                          String tenantName,
                                          Long loginUserId,
                                          String exportFilePath,
                                          String tempPath,
                                          TenantDataSource twinX,
                                          String businessDataMessage,
                                          String currentTag,
                                          List<IgnoreInfo> twinXIgnoreInfoList,
                                          List<File> appendFiles) throws Exception {
        tenantCode = tenantCode.toLowerCase();
        log.info("版本号【{}】当前租户{}正在操作临时数据库", tag, tenantName);
        final String twinXTmpSchema = TWIN_X + "_tmp";
        final String tenantTwinXTmpSchema = TenantConstant.MASTER.equals(tenantCode) ? twinXTmpSchema : tenantCode + "_" + twinXTmpSchema;
        try {
            // 创建临时库
            dataMigrationMapper.createDatabase(tenantTwinXTmpSchema);
        } catch (Exception e) {
            SysRealTimeMessage sysRealTimeMessage = SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("打包租户【%s】系统数据%s%s版本失败，请稍后再试！", tenantName, businessDataMessage, tag));
            log.info("创建孪生体临时数据库失败：{}", JSON.toJSONString(sysRealTimeMessage));
            throw ThrowUtils.getThrow().internalServerError("临时库创建失败", e);
        }
        // 导出basex auto_api schema, 不忽略不加密,添加额外undo文件
        appendFiles.addAll(getAppendFile(businessData, tag, currentTag, exportFilePath, currentModules(flywayProperties)::contains));
        SqlUtils.exportSchema(twinX, twinX.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, false, appendFiles);
        final TenantDataSource twinXTemp = DataSourceConfigurationUtils.newTenantDataSource(tenantCode, twinXTmpSchema);
        try (twinXTemp) {
            // 导入到临时库,不解密
            final File tmpDir = new File(tempPath);
            final List<File> sqlFileList = sortAppendFile(tmpDir);
            SqlUtils.importSchema(twinXTemp, sqlFileList, false, sql -> transToSchemaSql(sql, tenantTwinXTmpSchema, TWIN_X));
            // 移除原导出文件
            try {
                FileUtils.cleanDirectory(tmpDir);
            } catch (IOException e) {
                throw ThrowUtils.getThrow().internalServerError("移除原导出文件失败", e);
            }
            // 导出basex_tmp  schema 添加忽略并加密 不添加额外undo文件
            try {
                return SqlUtils.exportSchema(twinXTemp, twinXTemp.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, true, null, twinXIgnoreInfoList.toArray(IgnoreInfo[]::new));
            } finally {
                dataMigrationMapper.dropDatabase(tenantTwinXTmpSchema);
            }
        }
    }


    /**
     * 根据租户code获取租户名称
     *
     * @param code 租户code
     * @return 租户名称
     * <AUTHOR>
     * @date 2022/01/18 16:21:59
     */
    public String getTenantName(String code) {
        AssertUtils.isTrue(StringUtils.isNotEmpty(code), HttpStatus.BAD_REQUEST, "租户code不能为空");
        if (MASTER.equals(code)) {
            return "主";
        }
        TenantInfo tenantInfo = tenantInfoService.getTenantByCode(code);
        AssertUtils.isTrue(Objects.nonNull(tenantInfo), HttpStatus.BAD_REQUEST, "当前租户不存在");
        return tenantInfo.getName();
    }

    /**
     * 发送消息（如果当前用户不存在向超级管理员发送，否则不发送）
     *
     * @param userId     用户id
     * @param tenantName 租户名称
     * <AUTHOR>
     * @date 2022/07/13 18:02:37
     */
    private void sendMsg(Long userId, String tenantName) {
        try {
            SysUser userById = sysUserMapper.selectById(userId);
            if (Objects.isNull(userById)) {
                SysUser userByAccount = sysUserApi.getUserByAccount(IdentityContext.SUPERADMIN);
                if (Objects.nonNull(userByAccount)) {
                    realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userByAccount.getId(), String.format("迁移租户【%s】系统数据成功！", tenantName), null));
                }
            } else {
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("迁移租户【%s】系统数据成功！", tenantName), null));
            }
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }

    /**
     * 获取数据合并差异信息列表
     *
     * @param originDynamicFormList 源动态表单列表
     * @param newDynamicFormList    新动态表单列表
     * @return 数据合并差异信息列表
     * <AUTHOR>
     * @date 2022/10/18 16:32
     */
    private List<DataMigrationDifferenceVo> getDataMigrationDifference(List<DynamicForm> originDynamicFormList, List<DynamicForm> newDynamicFormList) {

        final List<DataMigrationDifferenceVo> dataMigrationDifferenceVoList = new ArrayList<>();
        // 源表在新表里没有, 删除
        for (Iterator<DynamicForm> iterator = originDynamicFormList.iterator(); iterator.hasNext(); ) {
            DynamicForm originDynamicForm = iterator.next();
            if (newDynamicFormList.stream().noneMatch(df -> originDynamicForm.getCode().equals(df.getCode()))) {
                final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.DELETE);
                dataMigrationDifferenceVo.setTargetName(originDynamicForm.getName());
                dataMigrationDifferenceVo.setCode(originDynamicForm.getCode());
                final List<DataMigrationDifferenceDetailVo> migrationDetailVoList = originDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                dataMigrationDifferenceVo.setOrigin(migrationDetailVoList);
                dataMigrationDifferenceVoList.add(dataMigrationDifferenceVo);
                iterator.remove();
            }
        }
        if (CollectionUtils.isNotEmpty(originDynamicFormList)) {
            // 新表在源表里没有, 新增
            for (Iterator<DynamicForm> iterator = newDynamicFormList.iterator(); iterator.hasNext(); ) {
                DynamicForm newDynamicForm = iterator.next();
                if (originDynamicFormList.stream().noneMatch(df -> newDynamicForm.getCode().equals(df.getCode()))) {
                    final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                    dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.ADD);
                    dataMigrationDifferenceVo.setTargetName(newDynamicForm.getName());
                    dataMigrationDifferenceVo.setCode(newDynamicForm.getCode());
                    final List<DataMigrationDifferenceDetailVo> migrationDetailVoList = newDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                    dataMigrationDifferenceVo.setTarget(migrationDetailVoList);
                    dataMigrationDifferenceVoList.add(dataMigrationDifferenceVo);
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(newDynamicFormList)) {
                // 剩下的就是编辑冲突
                final int size = Math.min(originDynamicFormList.size(), newDynamicFormList.size());
                for (int i = 0; i < size; i++) {

                    final DynamicForm originDynamicForm = originDynamicFormList.get(i);
                    final String originCode = originDynamicForm.getCode();
                    final List<FormItem> originFormItemList = originDynamicForm.getFormItemList();
                    final Optional<DataMigrationDifferenceVo> dataMigrationVoOp = newDynamicFormList.stream().filter(df -> {
                        // code一致
                        if (originCode.equals(df.getCode())) {

                            final List<FormItem> newFormItemList = df.getFormItemList();
                            // 表单控件数量不一致
                            if (originFormItemList.size() != newFormItemList.size()) {
                                return true;
                            }
                            for (int j = 0; j < originFormItemList.size(); j++) {

                                final FormItem originFormItem = originFormItemList.get(j);
                                final FormItem newFormItem = newFormItemList.get(j);
                                if (
                                    // 表单控件类型不一致
                                        !originFormItem.getType().equals(newFormItem.getType()) ||
                                                // 表单标签不一致
                                                !originFormItem.getLabel().equals(newFormItem.getLabel()) ||
                                                // 表单字段不一致
                                                !originFormItem.getModel().equals(newFormItem.getModel())) {
                                    return true;
                                }
                            }
                        }
                        return false;
                    }).findFirst().map(newDynamicForm -> {
                        final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                        // 源表详情
                        final List<DataMigrationDifferenceDetailVo> originMigrationDetailVoList = originDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                        // 新表详情
                        final List<DataMigrationDifferenceDetailVo> newMigrationDetailVoList = newDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                        dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.EDIT);
                        dataMigrationDifferenceVo.setOriginName(originDynamicForm.getName());
                        dataMigrationDifferenceVo.setTargetName(newDynamicForm.getName());
                        dataMigrationDifferenceVo.setCode(newDynamicForm.getCode());
                        dataMigrationDifferenceVo.setOrigin(originMigrationDetailVoList);
                        dataMigrationDifferenceVo.setTarget(newMigrationDetailVoList);
                        return dataMigrationDifferenceVo;
                    });
                    dataMigrationVoOp.ifPresent(dataMigrationDifferenceVoList::add);
                }
            }
        }

        return dataMigrationDifferenceVoList;
    }

}
