<!--
 * @Description: 三维资源
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-07-03 18:38:40
 * @LastEditors: hasaiki
 * @LastEditTime: 2025-07-16 15:44:32
-->
<template>
  <div class="chart-templete">
    <div class="content-wrap">
      <div class="tag-search">
        <div class="tag-content" v-if="tagsList?.length">
          <div v-for="group in tagsList" :key="group.id" class="tag-group">
            <div v-if="group.tags?.length" class="tag-group-name" @click="checkGroupAll(group)">
              {{ group.groupName }}
            </div>
            <div v-if="group.tags?.length" class="tag-item">
              <a-checkbox-group v-model:value="tagsChecked[group.id]" style="width: 100%">
                <div v-for="tag in group.tags" :key="tag.id" class="tag-item-name">
                  <a-checkbox :value="tag.id">{{ tag.tagName }}</a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
          </div>
        </div>
        <div class="no-tag-content" v-else>
          <a-spin class="loading-icon" v-if="tagLoading" :spinning="tagLoading" />
          <Nodata v-if="!tagLoading" title="请绑定标签" />
        </div>
      </div>
      <div class="content-list">
        <div class="search">
          <div class="search-wrap">
            <a-input v-model:value.trim="pageConfig.likeContent" class="search-wrapper" placeholder="搜索" @keyup.enter="handleChange(1, 12)">
              <template #suffix>
                <search-outlined style="cursor: pointer" @click="handleChange(1, 12)" />
              </template>
            </a-input>
            <a-select v-model:value="pageConfig.source" placeholder="请选择数据源" class="search-select" @change="handleChangeSource()">
              <a-select-option :value="1">公共资源</a-select-option>
              <a-select-option :value="2">项目资源</a-select-option>
              <a-select-option :value="3">我的资源</a-select-option>
            </a-select>
            <a-select v-if="pageConfig.source === 3" v-model:value="pageConfig.checkedType" placeholder="请选择状态" class="search-select" @change="handleChange(1, 12)">
              <a-select-option :value="-1">全部</a-select-option>
              <a-select-option :value="0">审批通过</a-select-option>
              <a-select-option :value="1">待审批</a-select-option>
              <a-select-option :value="2">未通过</a-select-option>
            </a-select>
            <a-button type="primary" class="search-btn" style="margin-right: 20px" @click="handleChange(1, 12)"> 查询 </a-button>
            <a-popover trigger="click" v-if="aiUrl" :destroyTooltipOnHide="true" placement="bottom">
              <template #content>
                <AiSearch @ai-search="aiSearch" :url="aiUrl" />
              </template>
              <SparkleButton />
            </a-popover>
          </div>
          <a-button v-if="hasPerm('annotate-component:upload-component')" type="primary" class="handle-btn" @click="clickUploadChart"> 导入图表模板 </a-button>
        </div>
        <div class="list" v-if="hasPerm('annotate-component:page-component')">
          <div v-for="item in chartData" v-show="!loading && chartData?.length" :key="item.id" class="item">
            <div class="contain">
              <div class="img-box">
                <img :src="getImg(item.filePath, item.snapShot)" alt="图片" class="img" @error="(event: any) => ((event.target.src = systemLogo), (event.target.style.width = 'auto'))" />
                <div class="bottom-wrapper" v-show="pageConfig.source === 3">
                  <div class="bottom-content">
                    <!-- <div v-if="pageConfig.source !== 2" class="time">预览次数：{{ item.detail?.viewCount || 0 }}</div> -->
                    <!-- <div v-if="pageConfig.source === 2" class="time">预览次数：{{ item.componentDetail?.viewCount || 0 }}</div> -->
                    <div
                      class="status"
                      :class="{
                        fail: item.status === 2,
                        normal: item.status === 0,
                        unpush: item.status === null,
                      }"
                    >
                      {{ getStatus(item.status) }}
                    </div>
                  </div>
                </div>
                <!-- <div class="bottom-wrapper">
                                    <span class="author">作者：{{ item.projectManger }}</span>
                                    <span class="watch-times">预览次数：{{ item.previewTimes }}</span>
                                </div> -->
                <div class="hover-box">
                  <div class="btn" @click="perviewClick(item)">预览</div>
                  <!-- <div v-if="pageConfig.source !== 3 " class="btn perview" @click="copyClick(item)">复制</div> -->
                  <!-- <div v-if="pageConfig.source !== 3 && item.publishId " class="btn perview" @click="useTemp(item)">使用模板</div> -->
                  <!-- <div v-if="pageConfig.source === 1 " class="btn perview" @click="openDetail(item)">详情</div> -->
                  <div v-if="pageConfig.source === 3" class="btn perview" @click="handleEdit(item)">编辑</div>
                  <!-- <div v-if="pageConfig.source === 3 && (!item.approve || item.approve === 3)" class="btn perview" @click="handelEditChart(item)">编辑图表</div> -->
                  <a-popconfirm
                    placement="topRight"
                    title="确认删除？"
                    v-if="pageConfig.source === 3 && item.status === 2 && hasPerm('annotate-component:delete-component')"
                    @confirm="deleteClick(item)"
                  >
                    <div class="btn perview">删除</div>
                  </a-popconfirm>
                  <div v-if="pageConfig.source === 3 && item.status === 2 && hasPerm('annotate-component:modify-component')" class="btn perview" @click="pushApprove(item)">提交审批</div>
                  <div class="control-icon">
                    <a-tooltip placement="top">
                      <template #title>
                        <span>{{ item.approveRemark }}</span>
                      </template>
                      <exception-outlined v-if="pageConfig.source === 3 && item.status === 2" title="未通过原因" style="margin-right: 12px" />
                    </a-tooltip>
                    <!-- <div v-if="item.detail?.imgs?.length" style="display: inline-block; width: 16px; height: 16px; margin-right: 5px">
                                            <picture-outlined title="图片预览" style="margin-right: 5px" @click="imgPreview(item)" />
                                            <div :id="'img-pre-wrap' + item.id" :style="{ display: 'none' }">
                                                <img v-for="(img, index) in item.detail?.imgs" :key="index" :src="getImgUrl(img)" alt="" />
                                            </div>
                                        </div> -->
                    <!-- <div v-if="item.snapShot" style="display: inline-block; width: 16px; height: 16px; margin-right: 12px">
                      <picture-outlined title="图片预览" style="margin-right: 5px" @click="imgPreview(item)" />
                      <div :id="'img-pre-wrap' + item.id" v-if="item.showImgPreview" :style="{ display: 'none' }">
                        <img :src="getImg(item.filePath, item.snapShot)" alt="" />
                      </div>
                    </div> -->
                    <!-- <video-camera-outlined v-if="item.detail?.media?.length" title="视频预览" @click="openPreview('video', item.detail?.media)" /> -->
                  </div>
                </div>
              </div>
              <div class="item-bottom">
                <div class="title">
                  <div class="name" :title="item.componentName">{{ item.componentName }}</div>
                  <div class="user" :title="item.ownerName">
                    {{ item.ownerName }}
                  </div>
                </div>
                <div class="tag-wrapper">
                  <div :id="item.id" ref="tagList" class="tag-list">
                    <div v-for="(ele, index) in item.tags" :key="index" class="tag-item" :style="{ backgroundColor: ele.color }">
                      {{ ele.tagName }}
                    </div>
                  </div>
                  <div v-show="item.moreTags" class="tag-opera" @click.stop="item.visibleTags = !item.visibleTags">
                    <ellipsis-outlined />
                  </div>
                  <div v-show="item.visibleTags" class="type-list">
                    <div v-for="(ele, index) in item.exTags" :key="index" class="type-item" :style="{ backgroundColor: ele.color }">
                      {{ ele.tagName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a-spin v-if="loading" class="loading-icon" :spinning="loading" />
          <Nodata v-if="!loading && !chartData?.length" />
        </div>
        <div class="list" v-else>
          <Nodata title="暂无权限" />
        </div>
        <div class="pagination-box">
          <a-pagination
            :total="pageConfig.total"
            :page-size-options="['12', '20', '30', '40']"
            :current="pageConfig.current"
            :page-size="pageConfig.pageSize"
            size="small"
            :show-total="(total: number) => `共 ${total} 条`"
            show-size-changer
            @change="handleChange"
          />
        </div>
      </div>
    </div>
    <UseTemp ref="useTempxRef" />
    <Detail ref="threeDetailRef" />
    <EditChart ref="editChartRef" @ok="getChartList" />
    <UploadFile ref="uploadFileRef" />
    <ImgVideoPreview ref="imgVideoPreviewRef" />
    <ViewerArrow ref="viewerArrowRef" :current="current" :total="total" @left="clickLeft" @right="clickRight" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import 'viewerjs/dist/viewer.css';
import Viewer from 'viewerjs';
import { updateCookies } from '@/hooks/useCookies';
import SparkleButton from '@/components/SparkleButton.vue';
import AiSearch from '@/components/AiSearch.vue';
import ViewerArrow from '@/components/ViewerArrow.vue';
import projectGalleryImg from '@/assets/img/resource/projectGallery.png';
import Nodata from '@/views/default/Nodata.vue';
import ImgVideoPreview from '@/components/ImgVideoPreview.vue';
import EditChart from './EditChart.vue';
import UseTemp from './UseTemp.vue';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { chartList, setDeleteChart, chartGroup, pushChartApprove } from '@/api/portal/resource/allChart';
import Detail from './Detail.vue';
import { getSysPage } from '@/api/operator/system/developConfig';
import UploadFile from './UploadFile.vue';
import { useUserStore } from '@/store/user';
// 系统logo
const systemLogo = ref(sessionStorage.getItem('XI_TONG_LOGO') || projectGalleryImg);
const userStore = useUserStore();

// ai识图搜索
const aiUrl = ref('');
// 获取ai识图配置
const getSysConfig = () => {
  getSysPage({
    code: 'AI_SHI_TU',
  })
    .then((res: any) => {
      const val = res.data?.rows[0]?.value;
      if (val !== '无') {
        aiUrl.value = val;
      } else {
        aiUrl.value = '';
      }
    })
    .catch(() => {
      aiUrl.value = '';
    });
};
getSysConfig();

const screenIp = ref('');
// 大屏访问按钮权限
const getSysConfigScreen = () => {
  getSysPage({
    code: 'SCREEN_UI_IP',
  })
    .then((res: any) => {
      const val = res.data?.rows[0]?.value || '';
      screenIp.value = val;
    })
    .catch(() => {
      screenIp.value = '';
    });
};
getSysConfigScreen();

const aiSearch = (data: any[]) => {
  pageConfig.current = 1;
  pageConfig.pageSize = 12;
  getChartList(data);
};
// 图片预览
let imgViewer: any = null;
const viewerArrowRef = ref();
const current = ref(0);
const total = ref(0);
const clickLeft = () => {
  if (imgViewer) {
    imgViewer.prev();
  }
};
const clickRight = () => {
  if (imgViewer) {
    imgViewer.next();
  }
};
const imgPreview = (item: any) => {
  item.showImgPreview = true;
  nextTick(() => {
    imgViewer = new Viewer(document.getElementById(`img-pre-wrap${item.id}`), {
      toolbar: false,
      navbar: true,
      title: false,
      transition: false,
      hidden: () => {
        imgViewer?.destroy();
        imgViewer = null;
        total.value = 0;
      },
      viewed() {
        current.value = imgViewer.index;
        total.value = imgViewer.length;
        // vue3组件转dom节点
        imgViewer.viewer.appendChild(viewerArrowRef.value.$el);
      },
    });
    imgViewer.show();
  });
};
onBeforeUnmount(() => {
  // 销毁viewer
  if (imgViewer) {
    imgViewer.destroy();
  }
});
// 点击图片,视频预览
const imgVideoPreviewRef = ref();
const openPreview = (type: string, data: any) => {
  imgVideoPreviewRef.value.init(type, data);
};
// 批量上传
const uploadFileRef = ref();
// 点击上传大屏模板
const clickUploadChart = () => {
  uploadFileRef.value.init();
};

// 拼接图片地址
const getImg = (path: string, url: string) => {
  if (screenIp.value) {
    return `${window.config.baseUrl}${url}?width=400`;
  }
  return `${window.config.baseUrl}${path}${url}?width=400`;
};
// 加载中
const loading = ref(true);
// 存储分组选中
const tagsChecked = ref<any>({});
// tag分组数据
const tagsList = ref([]);
// 全选-反选当前分组
const checkGroupAll = (record: any) => {
  const currentTagIds = record.tags?.map((tag: any) => tag.id);
  if (JSON.stringify(tagsChecked.value[record.id]) === JSON.stringify(currentTagIds)) {
    tagsChecked.value[record.id] = [];
  } else {
    tagsChecked.value[record.id] = record.tags.map((tag: any) => tag.id);
  }
};
// 标签选择改变-查询
watch(
  () => tagsChecked.value,
  () => {
    handleChange(1, 12);
  },
  {
    deep: true,
  }
);
const tagLoading = ref(true);
// 获取类型分组
const getChartGroup = () => {
  tagLoading.value = true;
  chartGroup()
    .then((res) => {
      tagLoading.value = false;
      if (res.code !== 200) {
        tagsList.value = [];
        return;
      }
      if (res.data.length) {
        // 储存分组结构
        res.data.forEach((group: any) => {
          tagsChecked.value[group.id] = [];
        });
      } else {
        tagsChecked.value = {};
      }
      tagsList.value = res.data || [];
    })
    .catch(() => {
      tagsChecked.value = {};
      tagsList.value = [];
      tagLoading.value = false;
    });
};

getChartGroup();
const useTempxRef = ref();
const threeDetailRef = ref();

// 图表列表请求相关数据
const chartData = ref([]);
const pageConfig = reactive({
  total: 0,
  current: 1,
  pageSize: 12,
  likeContent: '',
  tagIds: [],
  source: 1,
  checkedType: -1,
});
// 资源来源改变
const handleChangeSource = () => {
  pageConfig.checkedType = -1;
  handleChange(1, 12);
};
const handleChange = (page: number, pageSize: number) => {
  pageConfig.current = page;
  pageConfig.pageSize = pageSize;
  getChartList();
};
// 获取图表列表
const getChartList = async (aiName?: string[]) => {
  loading.value = true;
  // 获取筛选的标签id
  const tagIds = Object.values(tagsChecked.value)?.flat(Infinity) || [];
  const param: any = {
    pageNo: pageConfig.current,
    pageSize: pageConfig.pageSize,
    name: pageConfig.likeContent,
    tagIds: tagIds,
    status: pageConfig.source === 1 ? 0 : pageConfig.checkedType === -1 ? null : pageConfig.checkedType,
    type: pageConfig.source === 1 || pageConfig.source === 3 ? '0' : '1',
  };
  if (pageConfig.source === 3) {
    param.createUser = userStore.userInfo.id;
  }
  // 接口请求
  const res: any = await chartList(param);
  loading.value = false;
  if (res.code === 200) {
    const { rows, totalRows } = res.data;
    pageConfig.total = totalRows;
    chartData.value = rows || [];
  } else {
    useGlobalMessage('error', res.message);
  }
  // // ai识图
  // if (aiName) {
  //   // @ts-ignore
  //   param.query.snapshot = aiName;
  // }
  // if (pageConfig.source === 1) {
  // 	// 接口请求
  // 	const res: any = await chartList(param);
  // 	loading.value = false;
  // 	if (res.code === 200) {
  // 		const { rows, totalRows } = res.data;
  // 		pageConfig.total = totalRows;
  // 		chartData.value = rows || [];
  // 	} else {
  // 		useGlobalMessage('error', res.message);
  // 	}
  // } else if (pageConfig.source === 2) {
  // 	// @ts-ignore
  // 	param.query.qryType = 'component';
  // 	// @ts-ignore
  // 	param.query.visibleStatus = true;
  // 	const res = await getListProject(param);
  // 	loading.value = false;
  // 	if (res.code === 200) {
  // 		const { rows, totalRows } = res.data.componentPage;
  // 		chartData.value = rows || [];
  // 		pageConfig.total = totalRows;
  // 	}
  // } else if (pageConfig.source === 3) {
  // 	// 我的资源
  // 	if (pageConfig.checkedType !== -1 && pageConfig.source === 3) {
  // 		// @ts-ignore
  // 		param.query.approve = pageConfig.checkedType;
  // 	}
  // 	// 接口请求
  // 	const res: any = await myChartList(param);
  // 	loading.value = false;
  // 	if (res.code === 200) {
  // 		const { rows, totalRows } = res.data;
  // 		pageConfig.total = totalRows;
  // 		chartData.value = rows || [];
  // 	} else {
  // 		useGlobalMessage('error', res.message);
  // 	}
  // }
};

const dev = import.meta.env.DEV;
// 点击预览
const perviewClick = (item: any) => {
  console.log('点击图表预览', item);
  if (screenIp.value) {
    // 更新cookies
    if (dev) {
      // 注意：开发环境调试需要本地运行kunpeng前端，并将工作台的token给kunpeng页面。注入方式：在控制台cookie中新建Authorization，值为工作台/底座的token
      window.open(
        `http://localhost:8080/nanshan/preview/component/${item.id}?random=${Date.now()}`,
        '_blank',
        `width=${window.innerWidth},height=${window.innerHeight - 100},top=100,left=100,z-look=yes`
      );
    } else {
      updateCookies();
      window.open(`${screenIp.value}/nanshan/preview/component/${item.id}?random=${Date.now()}`, '_blank', `width=${window.innerWidth},height=${window.innerHeight - 100},top=100,left=100,z-look=yes`);
    }
  } else {
    const tempWindow = window.open('_blank');
    tempWindow!.location = `/screen/index.html?path=${item.filePath}&type=chart`;
  }
};

// 点击编辑
const editChartRef = ref();
const handleEdit = (record: any) => {
  const templateEditorInfo = { ...record };
  editChartRef.value.init(templateEditorInfo);
};
// 删除模板
const deleteClick = async (record: any) => {
  const res: any = await setDeleteChart([record.id]);
  if (res.code === 200) {
    useGlobalMessage('success', '删除成功');
    getChartList();
  }
};

// 提交审批
const pushApprove = async (record: any) => {
  const res1 = await pushChartApprove({
    id: record.id,
    status: 1,
  });
  if (res1.code !== 200) {
    useGlobalMessage('error', res1.message);
    return;
  }
  useGlobalMessage('success', '提交成功，请等待管理员审批！');
  getChartList();
};
// 审批状态
const getStatus = (status: number) => {
  let backWord = '';
  if (status === 0) {
    backWord = '正常';
  } else if (status === 1) {
    backWord = '待审批';
  } else if (status === 2) {
    backWord = '未通过';
  }
  return backWord;
};
</script>
<style scoped lang="scss">
.chart-templete {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--primary-bg-color);

  .search {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid var(--header-border-color);

    .search-wrap {
      padding-top: 0;
    }

    .search-wrapper {
      width: 200px;
      min-width: 100px;
      height: 38px;
      min-height: 32px;
      max-height: 38px;
      margin-right: 10px;
    }

    .search-select {
      margin-right: 10px;
    }

    .input-file {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      cursor: pointer;
      opacity: 0;
    }
  }

  .content-wrap {
    display: flex;
    flex: 1;
    overflow: hidden;

    .tag-search {
      box-sizing: border-box;
      width: 187px;
      min-width: 100px;
      max-width: 187px;
      height: 100%;
      padding: 10px 0;
      border-right: 1px solid var(--header-border-color);

      .tag-content {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 0 10px;
        overflow-y: auto;
      }

      .no-tag-content {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0 10px;
      }

      .tag-group {
        width: 100%;
        margin-bottom: 8px;

        .tag-group-name {
          height: 24px;
          min-height: 20px;
          margin-bottom: 4px;
          overflow: hidden;
          font-family: PingFangSC-Regular;
          font-size: 16px;
          font-weight: 700;
          line-height: 24px;
          color: var(--header-text-active-color);
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }

        .tag-item-name {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          width: 100%;
          padding: 2px 2px 2px 9px;
          color: var(--header-text-color);
          border-radius: 4px;

          :deep(.ant-checkbox) {
            margin-right: 4px;
          }

          &.checked {
            background-color: var(--modal-header-bg-color);
          }

          &:hover {
            background-color: var(--modal-header-bg-color);
          }
        }

        .tag-item {
          margin-bottom: 4px;
        }
      }
    }

    .content-list {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
    }

    .list {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      place-content: baseline start;
      padding: 4px;
      overflow-y: auto;

      .loading-icon {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }

      .item {
        padding: 4px;

        @media screen and (width >= 1600px) {
          width: calc(100% / 4);
        }

        @media screen and (width <= 1600px) {
          width: calc(100% / 3);
        }

        @media screen and (width <= 900) {
          width: calc(100% / 2);
        }

        @media screen and (width <= 750) {
          width: calc(100%);
        }

        .contain {
          box-sizing: border-box;
          height: 100%;
          background: var(--router-bg-color);
          border-radius: 4px;

          .img-box {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            height: 163px;
            overflow: hidden;
            text-align: center;
            background: var(--conent-bg-color-img);
            background-color: #000;
            border-bottom: 1px solid var(--header-border-color);

            .bottom-wrapper {
              position: absolute;
              bottom: 0;
              left: 0;
              display: flex;
              justify-content: space-between;
              width: 100%;
              height: 24px;
              color: #fff;

              .status {
                padding: 0 6px;
                color: #fff;

                &.normal {
                  color: green;
                }

                &.delete {
                  color: #f8f8f8;
                }

                &.fail {
                  color: red;
                }

                &.unpush {
                  color: aqua;
                }

                &.check {
                  color: dodgerblue;
                }
              }

              .bottom-content {
                display: flex;
                flex-direction: row-reverse;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 100%;
                padding: 0 3px;
                background-color: rgb(0 0 0 / 50%);
              }
            }

            &:hover {
              .hover-box {
                visibility: visible;
              }
            }

            .hover-box {
              position: absolute;
              top: 0;
              left: 0;
              display: flex;
              flex-wrap: wrap;
              place-content: center center;
              align-items: center;
              width: 100%;
              height: 100%;
              visibility: hidden;
              background-color: rgb(0 0 0 / 50%);
              border-radius: 4px 4px 0 0;

              .control-icon {
                position: absolute;
                top: 10px;
                right: 15px;
                font-size: 16px;
                color: #fff;
                cursor: pointer;
              }

              .btn {
                height: 20px;
                padding: 0 12px;
                margin-bottom: 5px;
                font-size: 12px;
                line-height: 20px;
                color: #fff;
                text-align: center;
                letter-spacing: 2px;
                cursor: pointer;
                background-color: var(--theme-color);
                border-radius: 4px;

                &:nth-of-type(1),
                &:nth-of-type(2),
                &:nth-of-type(3),
                &:nth-of-type(4),
                &:nth-of-type(5) {
                  margin-right: 5px;
                }
              }
            }

            &.active {
              border-color: var(--theme-color);
            }

            .time {
              color: #fff;
            }

            img {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              max-height: 100%;
              object-fit: cover;
              transform: translate(-50%, -50%);
            }
          }

          .item-bottom {
            width: 100%;
            padding: 6px 6px 8px;

            .title {
              display: flex;

              .name {
                width: 70%;
                overflow: hidden;
                font-size: 14px;
                font-weight: 500;
                color: var(--primary-text-color);
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .user {
                width: 30%;
                overflow: hidden;
                font-size: 13px;
                font-weight: 500;
                color: var(--secnd-text-color);
                text-align: right;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .tag-wrapper {
              position: relative;

              .tag-list {
                display: flex;
                flex-wrap: wrap;

                .tag-item {
                  flex-shrink: 0;
                  max-width: 100%;
                  padding: 0 6px;
                  margin-top: 2px;
                  margin-right: 6px;
                  font-family: Helvetica;
                  font-size: 14px;
                  line-height: 1.5;
                  color: #fff;
                  border-radius: 4px;
                }
              }

              .tag-opera {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 22px;
                height: 22px;
                cursor: pointer;
                background: var(--second-bg-color);
                border-radius: 4px;
              }

              .type-list {
                position: absolute;
                right: 0;
                bottom: 30px;
                display: flex;
                flex-wrap: wrap;
                width: 200px;
                max-height: 150px;
                padding: 8px;
                overflow-y: scroll;
                background: #fff;
                border-radius: 6px;

                .type-item {
                  height: 22px;
                  padding: 0 6px;
                  margin-right: 6px;
                  margin-bottom: 6px;
                  font-family: Helvetica;
                  font-size: 14px;
                  line-height: 22px;
                  color: #fff;
                  border-radius: 4px;
                }
              }
            }
          }
        }
      }
    }
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 40px;
    min-height: 40px;
    max-height: 50px;
  }
}
</style>
