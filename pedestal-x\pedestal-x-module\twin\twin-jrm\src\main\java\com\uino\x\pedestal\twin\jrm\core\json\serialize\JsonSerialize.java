package com.uino.x.pedestal.twin.jrm.core.json.serialize;


import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import org.springframework.lang.Nullable;

import java.util.Map;

/**
 * json序列化接口
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/13 17:27
 */
public interface JsonSerialize {

    /**
     * json to map
     *
     * @param jsonDefinition json描述
     * @return json map
     * <AUTHOR>
     * @date 2021/4/13 17:27
     */
    <E> Map<String, E> toJsonMap(JsonDefinition jsonDefinition);

    /**
     * object to json
     *
     * @param jsonMap json map
     * @return json描述
     * <AUTHOR>
     * @date 2021/4/13 17:27
     */
    <E> JsonDefinition toJsonDefinition(Map<String, E> jsonMap);

    /**
     * from json
     *
     * @param json      json字符串
     * @param typeClass 类型class
     * @param <T>       类型class泛型
     * @return
     * <AUTHOR>
     * @date 2021/4/13 17:27
     */
    <T> T fromJson(String json, Class<T> typeClass);

    /**
     * toJson
     *
     * @param obj 对象
     * @return json字符串
     * <AUTHOR>
     * @date 2021/4/13 17:27
     */
    String toJson(Object obj);

    /**
     * 获取json的第一个key
     *
     * @param json json字符串
     * @return 第一个key
     */
    @Nullable
    String firstKey(String json);
}
