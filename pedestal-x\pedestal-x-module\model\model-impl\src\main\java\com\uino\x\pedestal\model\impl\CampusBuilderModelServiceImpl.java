package com.uino.x.pedestal.model.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.uino.x.common.pojo.domain.node.ClassifyTreeNode;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.pedestal.model.api.service.CampusBuilderModelService;
import com.uino.x.pedestal.model.api.service.ThingsModelsService;
import com.uino.x.pedestal.model.common.constant.CampusBuilderModelConstant;
import com.uino.x.pedestal.model.common.exception.CampusBuilderModelException;
import com.uino.x.pedestal.model.impl.util.CampusBuilderModelUrlUtils;
import com.uino.x.pedestal.model.pojo.entity.ThingsModel;
import com.uino.x.pedestal.model.pojo.param.ThingsModelParam;
import com.uino.x.pedestal.model.pojo.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.uino.x.pedestal.model.common.constant.CampusBuilderModelConstant.*;

/**
 * 森园区模型库管理服务实现
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CampusBuilderModelServiceImpl implements CampusBuilderModelService {


    // 依赖注入
    private final ThingsModelsService thingsModelsService;

    @Override
    public CampusBuilderModelPageVo page(String url, Integer modelType) {
        log.debug("获取森园区模型页面数据，URL: {}, 模型类型: {}", url, modelType);

        // 参数验证
        validateParameters(url, modelType);

        try {
            // 构建查询参数
            final ThingsModelParam param = buildThingsModelParam(modelType);

            // 根据模型类型选择不同的处理逻辑
            final CampusBuilderModelPageVo result = PROJECT_MODEL_TYPE.equals(modelType)
                    ? buildProjectModelResponse(param, url)
                    : buildBasicModelResponse(param, url);

            log.debug("成功构建森园区模型页面数据，模型类型: {}", modelType);
            return result;
        } catch (Exception e) {
            log.error("获取森园区模型页面数据失败，URL: {}, 模型类型: {}", url, modelType, e);
            final CampusBuilderModelPageVo result = new CampusBuilderModelPageVo();
            result.setList(List.of());
            if (!PROJECT_MODEL_TYPE.equals(modelType)) {
                result.setIndustry(List.of());
                result.setModelUrl(CampusBuilderModelUrlUtils.buildResourceUrl(url, MODEL_PATH));
                result.setTextureUrl(CampusBuilderModelUrlUtils.buildResourceUrl(url, TEXTURE_PATH));
            }
            throw new CampusBuilderModelException(JSONObject.toJSONString(result));
        }
    }

    /**
     * 参数验证
     */
    private void validateParameters(String url, Integer modelType) {
        if (StringUtils.isBlank(url)) {
            throw new CampusBuilderModelException(CampusBuilderModelConstant.ERROR_URL_NULL).forbidden();
        }
        if (modelType == null) {
            throw new CampusBuilderModelException(CampusBuilderModelConstant.ERROR_MODEL_TYPE_NULL).forbidden();
        }
    }

    /**
     * 构建ThingsModelParam参数
     */
    private ThingsModelParam buildThingsModelParam(Integer modelType) {
        final ThingsModelParam param = new ThingsModelParam();
        param.setClassify(ROOT_CLASSIFY);
        param.setPageFrom(DEFAULT_PAGE_FROM);
        param.setPageSize(DEFAULT_PAGE_SIZE);
        param.setModelType(modelType);
        return param;
    }

    /**
     * 构建项目模型响应
     */
    private CampusBuilderModelPageVo buildProjectModelResponse(ThingsModelParam param, String url) {
        log.debug("构建项目模型响应，参数: {}", param);

        final PageResult<ThingsModel> pageResult = thingsModelsService.projectModelPage(param);
        final List<CampusBuilderProjectModelVo> projectModels = Optional.ofNullable(pageResult.getRows())
                .orElse(Collections.emptyList())
                .stream()
                .map(model -> convertToProjectModelVo(model, url))
                .collect(Collectors.toList());

        final CampusBuilderModelPageVo result = new CampusBuilderModelPageVo();
        result.setList(projectModels);

        log.debug("项目模型响应构建完成，模型数量: {}", projectModels.size());
        return result;
    }

    /**
     * 构建基础模型响应
     */
    private CampusBuilderModelPageVo buildBasicModelResponse(ThingsModelParam param, String url) {
        log.debug("构建基础模型响应，参数: {}", param);

        // 获取分类树和模型分页数据
        final ClassifyTreeNode classifyTree = thingsModelsService.getClassifyTree(param.getClassify(), param.getModelType());
        final PageResult<ThingsModel> pageResult = thingsModelsService.basicModelPage(param, false);

        // 按分类分组模型数据
        final Map<String, List<ThingsModel>> groupByClassify = Optional.ofNullable(pageResult.getRows())
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(ThingsModel::getClassify));

        // 构建响应对象
        final CampusBuilderModelPageVo result = new CampusBuilderModelPageVo();
        result.setIndustry(buildIndustryTree(classifyTree, groupByClassify));
        result.setModelUrl(CampusBuilderModelUrlUtils.buildResourceUrl(url, MODEL_PATH));
        result.setTextureUrl(CampusBuilderModelUrlUtils.buildResourceUrl(url, TEXTURE_PATH));

        log.debug("基础模型响应构建完成，行业分类数量: {}",
                Optional.ofNullable(result.getIndustry()).map(List::size).orElse(0));
        return result;
    }

    /**
     * 转换为项目模型VO
     */
    private CampusBuilderProjectModelVo convertToProjectModelVo(ThingsModel model, String url) {
        if (model == null) {
            log.warn("模型数据为空，跳过转换");
            return null;
        }

        final CampusBuilderProjectModelVo modelVo = new CampusBuilderProjectModelVo();
        modelVo.setId(model.getModelId());
        modelVo.setTitle(model.getTitle());
        modelVo.setType(PLACEMENT_TYPE);
        modelVo.setVersion(model.getVersion());
        modelVo.setSize(model.getSize());
        modelVo.setLocalurl(CampusBuilderModelUrlUtils.buildLocalUrl(model.getModelId()));
        modelVo.setPreview(CampusBuilderModelUrlUtils.buildPreviewUrl(url, model.getModelId()));

        return modelVo;
    }

    /**
     * 构建行业分类树
     */
    private List<CampusBuilderIndustryVo> buildIndustryTree(ClassifyTreeNode classifyTree,
                                                            Map<String, List<ThingsModel>> groupByClassify) {
        if (classifyTree == null) {
            log.warn("分类树为空，返回空列表");
            return Collections.emptyList();
        }

        return Optional.ofNullable(classifyTree.getChildren())
                .orElse(Collections.emptyList())
                .stream()
                .map(secondNode -> buildIndustryVo(secondNode, groupByClassify))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建行业VO
     */
    private CampusBuilderIndustryVo buildIndustryVo(ClassifyTreeNode secondNode,
                                                    Map<String, List<ThingsModel>> groupByClassify) {
        if (secondNode == null) {
            log.warn("行业节点为空，跳过构建");
            return null;
        }

        final CampusBuilderIndustryVo industryVo = new CampusBuilderIndustryVo();
        industryVo.setTitle(secondNode.getTitle());
        industryVo.setType(FOLDER_TYPE);

        final List<CampusBuilderCategoryVo> categories = buildCategoryList(secondNode, groupByClassify);
        industryVo.setChildren(categories);

        return industryVo;
    }

    /**
     * 构建分类列表
     */
    private List<CampusBuilderCategoryVo> buildCategoryList(ClassifyTreeNode secondNode,
                                                            Map<String, List<ThingsModel>> groupByClassify) {
        return Optional.ofNullable(secondNode.getChildren())
                .orElse(Collections.emptyList())
                .stream()
                .map(thirdNode -> buildCategoryVo(thirdNode, groupByClassify))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建分类VO
     */
    private CampusBuilderCategoryVo buildCategoryVo(ClassifyTreeNode thirdNode,
                                                    Map<String, List<ThingsModel>> groupByClassify) {
        if (thirdNode == null) {
            log.warn("分类节点为空，跳过构建");
            return null;
        }

        final CampusBuilderCategoryVo categoryVo = new CampusBuilderCategoryVo();
        categoryVo.setTitle(thirdNode.getTitle());
        categoryVo.setType(FOLDER_TYPE);

        final List<CampusBuilderModelItemVo> modelItems = buildModelItems(
                groupByClassify.getOrDefault(thirdNode.getKey(), Collections.emptyList()));
        categoryVo.setChildren(modelItems);

        return categoryVo;
    }

    /**
     * 构建模型项列表
     */
    private List<CampusBuilderModelItemVo> buildModelItems(List<ThingsModel> models) {
        if (models == null || models.isEmpty()) {
            return Collections.emptyList();
        }

        return models.stream()
                .map(this::convertToModelItemVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换为模型项VO
     */
    private CampusBuilderModelItemVo convertToModelItemVo(ThingsModel model) {
        if (model == null) {
            log.warn("模型数据为空，跳过转换");
            return null;
        }

        final CampusBuilderModelItemVo itemVo = new CampusBuilderModelItemVo();
        itemVo.setId(model.getModelId());
        itemVo.setTitle(model.getTitle());
        itemVo.setType(model.getType());
        itemVo.setVersion(model.getVersion());
        final JSONArray array = Optional.ofNullable(JSON.parseArray(model.getSize())).orElse(new JSONArray());
        itemVo.setSize(array.toJavaList(Double.class));
        return itemVo;
    }
}