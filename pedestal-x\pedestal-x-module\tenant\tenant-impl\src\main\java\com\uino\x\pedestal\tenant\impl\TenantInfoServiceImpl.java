package com.uino.x.pedestal.tenant.impl;

import cn.hutool.cron.CronUtil;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.uino.x.common.cache.impl.DefaultPrefixCacheOperation;
import com.uino.x.common.config.properties.MultiTenantProperties;
import com.uino.x.common.core.factory.ExecutorServiceFactory;
import com.uino.x.common.core.factory.PageFactory;
import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.common.core.util.FeignUtils;
import com.uino.x.common.core.util.TenantUtils;
import com.uino.x.common.datasource.properties.ModuleConfigProperties;
import com.uino.x.common.datasource.util.DataSourceConfigurationUtils;
import com.uino.x.common.datasource.util.TenantDataSource;
import com.uino.x.common.label.annotation.aop.RedissonLock;
import com.uino.x.common.label.constant.CommonConstant;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.objectstorage.template.ObjectStorageTemplate;
import com.uino.x.common.pojo.entity.BaseEntity;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.tool.base.*;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.common.constant.MinioBucketConstant;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.api.service.SysAppService;
import com.uino.x.pedestal.identity.api.service.SysCategoryInfoService;
import com.uino.x.pedestal.identity.api.service.SysMenuService;
import com.uino.x.pedestal.identity.pojo.domain.SysLoginUser;
import com.uino.x.pedestal.identity.pojo.entity.SysCategoryInfo;
import com.uino.x.pedestal.identity.pojo.entity.SysMenu;
import com.uino.x.pedestal.identity.pojo.param.SyncAppMenuParam;
import com.uino.x.pedestal.identity.pojo.param.SysMenuParam;
import com.uino.x.pedestal.license.api.LicenseInfoApi;
import com.uino.x.pedestal.license.pojo.LicenseInfo;
import com.uino.x.pedestal.message.api.service.RealTimeMessageService;
import com.uino.x.pedestal.message.pojo.entity.SysRealTimeMessage;
import com.uino.x.pedestal.microapp.api.service.MicroAppService;
import com.uino.x.pedestal.microapp.common.constant.MicroAppEnum;
import com.uino.x.pedestal.microapp.mapper.MicroAppMapper;
import com.uino.x.pedestal.microapp.pojo.param.MicroAppDataInitParam;
import com.uino.x.pedestal.model.common.properties.ModelConfigProperties;
import com.uino.x.pedestal.tenant.api.DataMigrationApi;
import com.uino.x.pedestal.tenant.api.service.TenantInfoService;
import com.uino.x.pedestal.tenant.common.enums.MigrationTypeEnum;
import com.uino.x.pedestal.tenant.dao.mapper.DataMigrationMapper;
import com.uino.x.pedestal.tenant.dao.mapper.TenantInfoMapper;
import com.uino.x.pedestal.tenant.impl.management.TenantInfoManagement;
import com.uino.x.pedestal.tenant.migrate.TenantMigrationInfo;
import com.uino.x.pedestal.tenant.migrate.factory.TenantMigrationFactory;
import com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties;
import com.uino.x.pedestal.tenant.pojo.entity.TenantInfo;
import com.uino.x.pedestal.tenant.pojo.param.TenantInfoParam;
import com.uino.x.pedestal.tenant.pojo.vo.TenantInfoVo;
import com.uino.x.pedestal.timer.api.service.SysTimersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 租户信息服务层实现类
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2021/11/18 16:19
 */
@Service
@EnableConfigurationProperties({MultiTenantProperties.class, DataMigrateProperties.class})
@Slf4j
@RequiredArgsConstructor
public class TenantInfoServiceImpl extends ServiceImpl<TenantInfoMapper, TenantInfo> implements TenantInfoService {

    public static final ExecutorService EXECUTOR_SERVICE = ExecutorServiceFactory.createThreadPool(TenantInfoServiceImpl.class);


    private final MultiTenantProperties multiTenantProps;
    private final DefaultPrefixCacheOperation<Number> businessCacheOperation;
    private final RealTimeMessageService realTimeMessageService;
    private final ModuleConfigProperties moduleDataSourceProps;

    private final SysTimersService sysTimersService;
    @Qualifier("com.uino.x.pedestal.license.api.LicenseInfoApi")
    private final LicenseInfoApi licenseInfoApi;
//    private final PluginInfoApi pluginInfoApi;
//    private final PluginInfoMapper pluginInfoMapper;
    private final TenantInfoManagement tenantInfoManagement;

    private final MicroAppService microAppService;

    private final MicroAppMapper microAppMapper;

    private final SysAppService sysAppService;

    private final SysMenuService sysMenuService;
    private final SysCategoryInfoService sysCategoryInfoService;
    private final DataMigrationMapper dMapper;
    private final ObjectStorageTemplate ost;
    private final DataMigrateProperties dataMigrateProps;

    @Override
    public PageResult<TenantInfoVo> page(TenantInfoParam tenantInfoParam) {

        ExecutorService executorService = EXECUTOR_SERVICE;
        final Long id = tenantInfoParam.getId();
        final String code = tenantInfoParam.getCode();
        final String name = tenantInfoParam.getName();

        //自定义排序
        if (tenantInfoParam.getSortField() == null) {
            tenantInfoParam.setSortField("t.create_time");
            tenantInfoParam.setSortRule("DESC");
        } else {
            tenantInfoParam.setSortField("t." + StringUtils.lowerToUnderscore(tenantInfoParam.getSortField()));
        }
        Page<Object> objectPage = PageFactory.defaultPage();
        // 分页查询
        Page<TenantInfoVo> tenantInfoVoPage = this.baseMapper.pageTenantInfoVo(objectPage, id, code, name, tenantInfoParam.getSortField(), tenantInfoParam.getSortRule());
        return new PageResult<>(tenantInfoVoPage);
    }

    @RedissonLock(enablePrefix = false, value = "'tenant:add:' + #tenantInfoParam.code", message = "当前租户正在创建中，请稍后再试")
    @Override
    public void copy(TenantInfoParam tenantInfoParam) {
        try {
            // 校验被复制的租户信息是否存在
            final LambdaQueryWrapper<TenantInfo> codeQueryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode()).eq(TenantInfo::getCode, tenantInfoParam.getOriginCode());
            AssertUtils.isTrue(this.count(codeQueryWrapper) > 0, HttpStatus.BAD_REQUEST, "被复制的租户不存在");
            // 根据租户名称以及编码检查唯一性
            checkNameAndCodeUnique(tenantInfoParam, false);
            // 根据租户编码创建数据库以及租户记录
            final String code = tenantInfoParam.getCode();
            final TenantInfo tenantInfo = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
            tenantInfo.setLicenseUpdateTime(new Date());
            this.baseMapper.insert(tenantInfo);

            // 初始化租户数据
            initTenantData(tenantInfoParam);
            initTenantFile(tenantInfoParam);

            // 给该租户初始化模型子应用
            initModelMicroAppAuthority(code);
            dMapper.initdata(code, tenantInfoParam.getOriginCode() + "_");

            // 设置租户许可到期时间
            setTenantLicenseExpiredTime(code, tenantInfo.getLicenseTotalTime());

            LicenseInfo masterLicenseInfo = licenseInfoApi.getMasterLicenseInfo();
            // 更新租户许可信息到Redis
            licenseInfoApi.updateOrSaveLicenseTenant(
                    tenantInfo.getSceneCount(),
                    tenantInfo.getUserCount(),
                    code,
                    masterLicenseInfo.getAuthUseDays(),
                    masterLicenseInfo.getAuthEndDate());
            
            businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 0L);

            final SysLoginUser user = IdentityContext.getSysLoginUserOrNull();
            if (user != null) {
                final SysRealTimeMessage realTimeMessage = SysRealTimeMessage.buildSysRealTimeMessage(user.getId(), "租户【" + tenantInfoParam.getName() + "】新建成功", null);
                realTimeMessageService.saveMessage(realTimeMessage);
            }
        } catch (Exception e) {
            log.error("租户创建失败", e);
            final SysLoginUser user = IdentityContext.getSysLoginUserOrNull();
            if (user != null) {
                final SysRealTimeMessage realTimeMessage = SysRealTimeMessage.buildSysRealTimeMessage(user.getId(), "租户【" + tenantInfoParam.getName() + "】新建失败，失败原因：" + e.getMessage(), null);
                realTimeMessageService.saveMessage(realTimeMessage);
            }
            this.baseMapper.deleteById(tenantInfoParam.getId());
        }
    }

    @RedissonLock(enablePrefix = false, value = "'tenant:add:' + #tenantInfoParam.code", message = "当前租户正在创建中，请稍后再试")
    @Override
    public void add(TenantInfoParam tenantInfoParam) {
        try {
            // 根据租户名称以及编码检查唯一性
            checkNameAndCodeUnique(tenantInfoParam, false);
            // 校验新增租户用户数场景数和授权时间
            //checkLicenseInfo(tenantInfoParam);
            // 根据租户编码创建数据库以及租户记录
            final String code = tenantInfoParam.getCode();
            final TenantInfo tenantInfo = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
            tenantInfo.setLicenseUpdateTime(new Date());
            this.baseMapper.insert(tenantInfo);

//            ExecutorService executor = Executors.newFixedThreadPool(1);
//            Future<?> initTenantDataFuture = executor.submit(() -> {
//                try {
            // 初始化租户数据
            initTenantData(tenantInfoParam);
            // TODO 添加租户 && 创建项目 初始数据统一走的复制，但是此复制彼非复制，业务文件数据不复制
            tenantInfoParam.setMigrationType(MigrationTypeEnum.INIT.getCode());
            initTenantFile(tenantInfoParam);
//                } catch (Exception e) {
//                    log.error("初始化租户数据初始化失败：", e);
//                }
//            });
            // 判断 Future 是否执行完毕
//            while (!initTenantDataFuture.isDone()) {
//                System.out.println("Task is not yet completed...");
//                try {
//                    Thread.sleep(1000); // 等待1秒钟再次检查
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//            initTenantDataFuture.get();
            // 给该租户初始化模型库权限相关数据
//             initModelPluginAuthority(code);

            // 给该租户初始化模型子应用
            initModelMicroAppAuthority(code);
            dMapper.initdata(code, "");

            // 设置租户许可到期时间
            setTenantLicenseExpiredTime(code, tenantInfo.getLicenseTotalTime());
            
            // 更新租户许可信息到Redis
            LicenseInfo masterLicenseInfo = licenseInfoApi.getMasterLicenseInfo();
            licenseInfoApi.updateOrSaveLicenseTenant(
                    tenantInfo.getSceneCount(),
                    tenantInfo.getUserCount(),
                    code,
                    masterLicenseInfo.getAuthUseDays(),
                    masterLicenseInfo.getAuthEndDate());
            
            businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 0L);
            final SysLoginUser user = IdentityContext.getSysLoginUserOrNull();
            if (user != null) {
                final SysRealTimeMessage realTimeMessage = SysRealTimeMessage.buildSysRealTimeMessage(user.getId(), "租户【" + tenantInfoParam.getName() + "】新建成功", null);
                realTimeMessageService.saveMessage(realTimeMessage);
            }
        } catch (Exception e) {
            log.error("租户创建失败", e);
            final SysLoginUser user = IdentityContext.getSysLoginUserOrNull();
            if (user != null) {
                final SysRealTimeMessage realTimeMessage = SysRealTimeMessage.buildSysRealTimeMessage(user.getId(), "租户【" + tenantInfoParam.getName() + "】新建失败，失败原因：" + e.getMessage(), null);
                realTimeMessageService.saveMessage(realTimeMessage);
            }
            this.baseMapper.deleteById(tenantInfoParam.getId());
        }

    }

    /**
     * 初始化租户数据
     *
     * @param tenantInfoParam 租户
     * <AUTHOR>
     * @date 2023/4/13 15:58
     */
    private void initTenantData(TenantInfoParam tenantInfoParam) {
        // 1. 当前服务
        this.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
        // 2. 其他服务
        final List<String> migrationServerList = dataMigrateProps.getMigrationServerList();
        final Map<String, String> feignConfig = moduleDataSourceProps.getFeignConfig();
        final String appName = SpringEnvUtils.getAppName();
        migrationServerList.stream()
                .filter(server -> !server.equals(appName))
                .forEach(serverName -> {
                    final String moduleName = serverName.substring(0, serverName.indexOf("-"));
                    final String server = feignConfig.get(moduleName);
                    if (StringUtils.isBlank(server)) {
                        return;
                    }
                    final DataMigrationApi serverApi = FeignUtils.createClient(DataMigrationApi.class, server, DataMigrationApi.API_PATH + "/" + moduleName);
                    serverApi.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
                });
//        DataMigrationApi twinClient = FeignUtils.createClient(DataMigrationApi.class, "twin-x");
//        DataMigrationApi modelClient = FeignUtils.createClient(DataMigrationApi.class, "model-x");
//        DataMigrationApi sceneClient = FeignUtils.createClient(DataMigrationApi.class, "scene-x");
        // 1.初始化系统数据
//        AsyncUtils.asyncExecutor(() -> {
//        });
//        this.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
        // 2.初始化孪生体数据
//        AsyncUtils.asyncExecutor(() -> {
//        });
//        twinClient.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
        // 3.初始化模型插件数据
//        modelClient.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
//        AsyncUtils.asyncExecutor(() -> {
//        });
        // 4.初始化场景数据
//        sceneClient.createDatabase(tenantInfoParam.getName(), tenantInfoParam.getCode().toLowerCase(), tenantInfoParam.getMigrationType());
//        AsyncUtils.asyncExecutor(() -> {
//        });
    }

    /**
     * 初始化租户数据
     *
     * @param tenantInfoParam 租户
     * <AUTHOR>
     * @date 2023/4/13 15:58
     */
    private void initTenantFile(TenantInfoParam tenantInfoParam) {
        if (Objects.equals(tenantInfoParam.getMigrationType(), MigrationTypeEnum.ALL.getCode())) {
            migrationFile(null, ModelConfigProperties.getRootBucket(), String.format("%s/", TenantUtils.master()), ModelConfigProperties.getRootBucket(), String.format("%s/", tenantInfoParam.getCode()));
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(4);
        // 只迁移基础数据，包括必要的模型和气泡和依赖文件
        migrationFile(countDownLatch, ModelConfigProperties.getRootBucket(), String.format("%s/%s/", TenantUtils.master(), ModelConfigProperties.getModelPath()), ModelConfigProperties.getRootBucket(), String.format("%s/%s/", tenantInfoParam.getCode(), ModelConfigProperties.getModelPath()));
        migrationFile(countDownLatch, ModelConfigProperties.getRootBucket(), String.format("%s/%s/", TenantUtils.master(), ModelConfigProperties.getTexturePath()), ModelConfigProperties.getRootBucket(), String.format("%s/%s/", tenantInfoParam.getCode(), ModelConfigProperties.getTexturePath()));
        migrationFile(countDownLatch, ModelConfigProperties.getRootBucket(), String.format("%s/%s/", TenantUtils.master(), MinioBucketConstant.BUBBLE), ModelConfigProperties.getRootBucket(), String.format("%s/%s/", tenantInfoParam.getCode(), MinioBucketConstant.BUBBLE));
        migrationFile(countDownLatch, ModelConfigProperties.getRootBucket(), String.format("%s/%s/", TenantUtils.master(), MinioBucketConstant.WEBASSETS), ModelConfigProperties.getRootBucket(), String.format("%s/%s/", tenantInfoParam.getCode(), MinioBucketConstant.WEBASSETS));
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.warn("初始化租户文件超时：", e);
        }
    }

    public void migrationFile(CountDownLatch countDownLatch,String originBucket, String originPrefix, String targetBucket, String targetPrefix){
        try {
            EXECUTOR_SERVICE.submit(() -> {
                try {
                    ost.copyObjects(originBucket,originPrefix,targetBucket,targetPrefix);
                } finally {
                    if (Objects.nonNull(countDownLatch)){
                        countDownLatch.countDown();
                    }
                }
            });
        }catch (Exception e){
            log.error("从{}/{}迁移数据到{}/{}时，发生异常",originBucket,originPrefix,targetBucket,targetPrefix,e);
        }
    }


    private void createDatabase(String name, String code, Integer migrationType) {
        try {
            this.baseMapper.createSchemaByTenantCode(code, DataSourceConstant.SYSTEM_X);
            // 根据租户编码新建数据源
            DataSourceProperty currentDataSourceProperty = DataSourceConfigurationUtils.getCurrentDataSourceProperty();
            currentDataSourceProperty.setSeata(false);
            DefaultDataSourceCreator dataSourceCreator = SpringIocUtils.mustGetBean(DefaultDataSourceCreator.class);
            try (TenantDataSource dataSource = DataSourceConfigurationUtils.newTenantDataSource(currentDataSourceProperty, dataSourceCreator, code, DataSourceConstant.SYSTEM_X)) {
                // 获取数据源,迁移信息
                final TenantMigrationInfo tenantMigrationInfo = new TenantMigrationInfo();
                tenantMigrationInfo.setTargetTenant(code);
                tenantMigrationInfo.setMigrationType(migrationType);
                tenantMigrationInfo.setName(name);
                tenantMigrationInfo.setSourceType(DataSourceConstant.SYSTEM_X);
                // 进行租户迁移
                tenantMigrationInfo.setCover(true);
                tenantMigrationInfo.setAllowMigrationDataTable(dataMigrateProps.getAllowMigrationDataTable());
                TenantMigrationFactory.of(dataSource, tenantMigrationInfo).get().migrate();
            }
        } catch (Exception e) {
            ThrowUtils.getThrow().internalServerError("初始化租户【" + code + "】系统数据源失败");
        }
    }

    /**
     * 给该租户初始化模型库权限相关数据
     *
     * <AUTHOR>
     * @date 2023/2/24 18:29
     */
//    private void initModelPluginAuthority(String tenantCode) {
//        Long modelNumber = pluginInfoMapper.selectCount(Wrappers.<PluginInfo>lambdaQuery().eq(PluginInfo::getCode, "modelLib").eq(BaseEntity::getStatus, 0).eq(PluginInfo::getType, 0));
//        if (modelNumber > 0) {
//            try {
//                pluginInfoApi.pluginDataLoad("modelLib", new DataManagerInitParam("DEVOPS", null, tenantCode));
//            } catch (Throwable e) {
//                log.warn("新建租户【{}】模型插件加载失败", tenantCode);
//            }
//        }
//    }

    /**
     * 给该租户初始化模型库子应用权限相关数据
     *
     * <AUTHOR>
     * @date 2023/2/24 18:29
     */
    private void initModelMicroAppAuthority(String tenantCode) {
        if (microAppMapper.existsMicroApp(MicroAppEnum.MODEL.getCode())) {
            try {
                microAppService.init(new MicroAppDataInitParam(MicroAppEnum.MODEL.getCode(), null, null, tenantCode));
            } catch (Throwable e) {
                log.warn("新建租户【{}】模型插件加载失败", tenantCode);
            }
        }
    }

    private void checkLicenseInfo(TenantInfoParam tenantInfoParam) {
        LicenseInfo licenseInfo = licenseInfoApi.getMasterLicenseInfo();
        String authEndDate = licenseInfo.getAuthEndDate();
        if (StringUtils.isBlank(authEndDate)) {
            log.error("获取主租户授权数据失败，请确认授权服务是否可用。");
            authEndDate = "19700101000000";
        } else {
            authEndDate += "240000";
        }
        long licenseSceneCount = licenseInfo.getSceneCount();
        long licenseUserCount = licenseInfo.getUserCount();
        Date licenseAuthEndDate = DateUtils.dateStrParse("yyyyMMddHHmmss", authEndDate);
        AssertUtils.isTrue(!tenantInfoParam.getLicenseExpiredTime().after(licenseAuthEndDate), HttpStatus.FORBIDDEN, "租户到期时间不能超过主租户到期时间");
        AssertUtils.isTrue(tenantInfoParam.getSceneCount().longValue() <= licenseSceneCount, HttpStatus.FORBIDDEN, "租户授权场景数不能超过主租户授权场景数");
        AssertUtils.isTrue(tenantInfoParam.getUserCount().longValue() <= licenseUserCount, HttpStatus.FORBIDDEN, "租户授权用户数不能超过主租户授权用户数");
    }

    /**
     * 根据租户名称以及编码检查唯一性
     *
     * @param tenantInfoParam 租户信息参数
     * @param excludeSelf     是否排除自己
     * <AUTHOR>
     * @date 2021/12/4 18:19
     */
    private void checkNameAndCodeUnique(TenantInfoParam tenantInfoParam, boolean excludeSelf) {
        AssertUtils.isTrue(tenantInfoParam.getLicenseTotalTime() <= System.currentTimeMillis(), "许可到期时间必须大于当前时间");
        // 构建根据名称/编码 QueryWrapper
        final LambdaQueryWrapper<TenantInfo> nameQueryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode()).eq(TenantInfo::getName, tenantInfoParam.getName());
        final LambdaQueryWrapper<TenantInfo> codeQueryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode()).eq(TenantInfo::getCode, tenantInfoParam.getCode());
        if (excludeSelf) {
            // 排除自己即排除id
            nameQueryWrapper.ne(TenantInfo::getId, tenantInfoParam.getId());
            codeQueryWrapper.ne(TenantInfo::getId, tenantInfoParam.getId());
        }
        final long nameCount = this.count(nameQueryWrapper);
        final long codeCount = this.count(codeQueryWrapper);
        AssertUtils.isTrue(nameCount == 0, HttpStatus.BAD_REQUEST, "租户名称重复");
        AssertUtils.isTrue(codeCount == 0, HttpStatus.BAD_REQUEST, "租户编码重复");
    }

    @RedissonLock(enablePrefix = false, value = "'tenant:delete:' + #tenantInfoParam.id", message = "当前租户正在删除中，请稍后再试")
    @Override
    public void delete(TenantInfoParam tenantInfoParam) {

        final String code = tenantInfoParam.getCode();
        final TenantInfo tenantInfo = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
        // 设置删除状态进行逻辑删除
        tenantInfo.setStatus(StatusEnum.DELETED.getCode());

        // this.baseMapper.updateStatusByCode(2,code);
        this.baseMapper.updateById(tenantInfo);
        // 删除对应数据库
        for (String schema : List.of(DataSourceConstant.SYSTEM_X, DataSourceConstant.SCENE_X)) {
            this.baseMapper.deleteSchemaByTenantCode(code + "_" + schema);
        }
        ost.removeObjects(ModelConfigProperties.getRootBucket(), code);
        DataSourceConfigurationUtils.removeTenantDataSources(code.toLowerCase(), DataSourceConstant.SYSTEM_X, DataSourceConstant.SCENE_X);

        /*final String deleteCode = JrmJsonUtils.DELETED_FLAG_PREFIX + code + StringConstant.UNDERSCORE + tenantInfoParam.getId();
        final TenantMigrationInfo migrationInfo = BeanUtil.copyProperties(tenantInfoParam, TenantMigrationInfo.class);
        migrationInfo.setMigrationType(MigrationTypeEnum.ALL.getCode());
        migrationInfo.setSourceTenant(code);
        migrationInfo.setTargetTenant(deleteCode);
        // 创建delete_库
        this.baseMapper.createSchemaByTenantCode(deleteCode);
        try {
            DataSourceConfigurationUtils.newTenantDataSource(deleteCode, DataSourceConstant.BASE_X);
        } catch (Exception e) {
            throw new ServiceException(HttpStatus.FORBIDDEN, "租户删除失败");
        }
        final DataSource dataSource = this.dataSource.getDataSource(code + StringConstant.UNDERSCORE + DataSourceConstant.BASE_X);
        // 备份删除的库
        TenantMigrationFactory.of(dataSource, migrationInfo).get().migrate();
        this.baseMapper.deleteSchemaByTenantCode(code);*/
        // 移除数据源
//        DataSourceConfigurationUtils.removeTenantDataSources(code.toLowerCase(), DataSourceConstant.SYSTEM_X);
//        businessCacheOperation.getCacheOperation().delete(code + StringConstant.COLON + CommonConstant.LICENSE_KEY);
//        businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 2L);
    }

    @Override
    public List<TenantInfo> getSyncTenantList1(TenantInfoParam tenantInfoParam) {

        final LambdaQueryWrapper<TenantInfo> wrapper = Wrappers.<TenantInfo>lambdaQuery()
                .eq(BaseEntity::getStatus, StatusEnum.ENABLE.getCode());

        return list(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(enablePrefix = false, value = "'tenant:change:' + #tenantInfoParam.code", message = "当前租户正在修改状态中，请稍后再试")
    @Override
    public void change(TenantInfoParam tenantInfoParam) {
        changeTenantAsync(tenantInfoParam);
    }

    private void changeTenantAsync(TenantInfoParam tenantInfoParam) {
        final String code = tenantInfoParam.getCode();
        final TenantInfo tenantInfo = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
//        String message;
        if (tenantInfoParam.getStatus().equals(StatusEnum.DISABLE.getCode())) {
            try {
                // 设置删除状态进行逻辑停用
                tenantInfo.setStatus(StatusEnum.DISABLE.getCode());
                this.baseMapper.updateById(tenantInfo);
                DataSourceConfigurationUtils.removeTenantDataSources(code.toLowerCase(), DataSourceConstant.SYSTEM_X);
                businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 1L);
//                message = "租户【" + tenantInfoParam.getName() + "】停用成功";
            } catch (Exception e) {
                log.error("租户停用失败", e);
//                message = "租户【" + tenantInfoParam.getName() + "】停用失败，失败原因：" + e.getMessage();
            }
        } else {
            try {
                tenantInfo.setStatus(StatusEnum.ENABLE.getCode());
                this.baseMapper.updateById(tenantInfo);
                // 根据租户编码新建数据源
                DataSourceConfigurationUtils.newTenantDataSourcesAddRouting(code.toLowerCase(), DataSourceConstant.SYSTEM_X);
                businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 0L);
//                message = "租户【" + tenantInfoParam.getName() + "】启用成功";
            } catch (Exception e) {
                log.error("租户启用失败", e);
//                message = "租户【" + tenantInfoParam.getName() + "】启用失败，失败原因：" + e.getMessage();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(enablePrefix = false, value = "'tenant:edit:' + #tenantInfoParam.code", message = "当前租户正在编辑中，请稍后再试")
    @Override
    public void edit(TenantInfoParam tenantInfoParam) {
        // 根据租户名称以及编码检查唯一性
//        checkNameAndCodeUnique(tenantInfoParam, true);
        // 更新租户信息
        final Date licenseExpiredTime = tenantInfoParam.getLicenseExpiredTime();
        final TenantInfo tenantInfo = this.baseMapper.selectById(tenantInfoParam.getId());
        AssertUtils.isTrue(tenantInfo.getCode().equals(tenantInfoParam.getCode()), HttpStatus.FORBIDDEN, "租户编码不允许修改");
        final TenantInfo tenantInfoUpdate = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
        // 如果许可到期时间不相等,则更新许可更新时间
        if (!tenantInfo.getLicenseExpiredTime().equals(licenseExpiredTime)) {
            tenantInfoUpdate.setLicenseUpdateTime(new Date());
        }
        this.updateById(tenantInfoUpdate);
        LicenseInfo masterLicenseInfo = licenseInfoApi.getMasterLicenseInfo();

        // 更新租户许可信息到redis
        licenseInfoApi.updateOrSaveLicenseTenant(
                tenantInfoParam.getSceneCount(),
                tenantInfoParam.getUserCount(),
                tenantInfoParam.getCode(),
                masterLicenseInfo.getAuthUseDays(),
                masterLicenseInfo.getAuthEndDate());

        // 获取当前时间
        LocalDate now = LocalDate.now();

        // 定义目标时间（例如：2024年12月31日 23:59:59）
        String targetTimeString = masterLicenseInfo.getAuthEndDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate targetDate = LocalDate.parse(targetTimeString, formatter);

        // 计算时间间隔（以天为单位）
        long daysBetween = Duration.between(now.atStartOfDay(), targetDate.atStartOfDay()).toDays();

        // 计算时间间隔的秒数
        long secondsBetween = daysBetween * 24 * 60 * 60;  // 转换为秒


//        long licenseTotalTime = tenantInfoParam.getLicenseTotalTime();
//        AssertUtils.isTrue(licenseTotalTime >= 0, HttpStatus.BAD_REQUEST, "授权时间不能小于当前时间");
        // 设置租户许可到期时间
        setTenantLicenseExpiredTime(tenantInfo.getCode(), secondsBetween);

        try {
            licenseInfoApi.updateOrSaveLicenseTenant(
                    tenantInfoParam.getSceneCount(),
                    tenantInfoParam.getUserCount(),
                    tenantInfo.getCode(),
                    masterLicenseInfo.getAuthUseDays(),
                    masterLicenseInfo.getAuthEndDate());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RedissonLock(enablePrefix = false, value = "'tenant:edit:' + #tenantInfoParam.code", message = "当前租户正在编辑中，请稍后再试")
    @Override
    public void edit1(TenantInfoParam tenantInfoParam) {
        // 根据租户名称以及编码检查唯一性
//        checkNameAndCodeUnique(tenantInfoParam, true);
        // 更新租户信息
        final Date licenseExpiredTime = tenantInfoParam.getLicenseExpiredTime();
        final TenantInfo tenantInfo = this.baseMapper.selectById(tenantInfoParam.getId());
        AssertUtils.isTrue(tenantInfo.getCode().equals(tenantInfoParam.getCode()), HttpStatus.FORBIDDEN, "租户编码不允许修改");
        final TenantInfo tenantInfoUpdate = BeanUtils.copyToBean(tenantInfoParam, TenantInfo.class);
        // 如果许可到期时间不相等,则更新许可更新时间
        if (!tenantInfo.getLicenseExpiredTime().equals(licenseExpiredTime)) {
            tenantInfoUpdate.setLicenseUpdateTime(new Date());
        }
        this.updateById(tenantInfoUpdate);
        
        // 更新租户许可信息到redis
        LicenseInfo masterLicenseInfo = licenseInfoApi.getMasterLicenseInfo();
        licenseInfoApi.updateOrSaveLicenseTenant(
                tenantInfoParam.getSceneCount(),
                tenantInfoParam.getUserCount(),
                tenantInfoParam.getCode(),
                masterLicenseInfo.getAuthUseDays(),
                masterLicenseInfo.getAuthEndDate());
        
        long licenseTotalTime = tenantInfoParam.getLicenseTotalTime();
        AssertUtils.isTrue(licenseTotalTime >= 0, HttpStatus.BAD_REQUEST, "授权时间不能小于当前时间");
        // 设置租户许可到期时间
        setTenantLicenseExpiredTime(tenantInfo.getCode(), licenseTotalTime);
    }

    @RedissonLock(value = "systemx_init", enableException = false)
    @Override
    public void initTenantData() {

        if (0 == this.baseMapper.tableCount()) {

            return;
        }
        final LambdaQueryWrapper<TenantInfo> queryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode());
        // 启动定时任务调度器
        CronUtil.getScheduler()
                // 设置秒级别的启用
                .setMatchSecond(true)
                // 启动定时器执行器
                .start();
        // 查询出所有租户
        final List<TenantInfo> list = this.list(queryWrapper);
        final int size = list.size();
        final ExecutorService threadPool = ExecutorServiceFactory.createThreadPool(TenantInfoServiceImpl.class, size, new ArrayBlockingQueue<>(Math.max(1, size >> 2)));
        AsyncUtils.asyncExecutor(list, tenantInfo -> {
            // 获取租户code
            final String tenantInfoCode = tenantInfo.getCode().toLowerCase();
            // 创建供flyway使用的数据源
            final String dataSourceKey = tenantInfoCode + StringConstant.UNDERSCORE + DataSourceConstant.SYSTEM_X;
            try (TenantDataSource dataSource = DataSourceConfigurationUtils.newTenantDataSource(tenantInfoCode, DataSourceConstant.SYSTEM_X)) {
                final TenantMigrationInfo tenantMigrationInfo = BeanUtils.copyToBean(tenantInfo, TenantMigrationInfo.class);
                tenantMigrationInfo.setMigrationType(MigrationTypeEnum.INIT.getCode());
                tenantMigrationInfo.setTargetTenant(tenantInfoCode);
                tenantMigrationInfo.setSourceType(DataSourceConstant.SYSTEM_X);
                // 初始化flyway最新的数据
                TenantMigrationFactory.of(dataSource, tenantMigrationInfo).get().migrate();
            }
            // 创建租户使用的数据源
            DataSourceConfigurationUtils.newTenantDataSourcesAddRouting(tenantInfoCode, DataSourceConstant.SYSTEM_X);
            sysTimersService.initTimerExe(dataSourceKey, tenantInfoCode);
            log.info("{} 租户数据初始化成功", tenantInfoCode);
        }, threadPool);
        sysTimersService.initTimerExe(DataSourceConstant.SYSTEM_X, TenantConstant.MASTER);
        threadPool.shutdown();
    }

    @Override
    public TenantInfoVo getCurrentTenantInfo() {

        return tenantInfoManagement.getCurrentTenantInfo();
    }

    @Override
    public List<TenantInfo> getSyncTenantList() {
        final LambdaQueryWrapper<TenantInfo> wrapper = Wrappers.<TenantInfo>lambdaQuery()
                .eq(BaseEntity::getStatus, StatusEnum.ENABLE.getCode());

        return list(wrapper);
    }

    @Override
    public TenantInfo getTenantByCode(String code) {
        // 查询出当前的租户信息
        final LambdaQueryWrapper<TenantInfo> queryWrapper = Wrappers.<TenantInfo>lambdaQuery().ne(TenantInfo::getStatus, StatusEnum.DELETED.getCode()).eq(TenantInfo::getCode, code);
        return getOne(queryWrapper, false);
    }

    /**
     * 设置租户许可到期时间
     *
     * @param code               租户编码
     * @param licenseExpiredTime 许可到期时间
     * <AUTHOR>
     * @date 2021/12/8 11:28
     */
    private void setTenantLicenseExpiredTime(String code, Long licenseExpiredTime) {
        log.info(code + "租户设置授权有效时间+" + licenseExpiredTime + "秒");
        businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.LICENSE_KEY, licenseExpiredTime, licenseExpiredTime, TimeUnit.SECONDS);
        businessCacheOperation.getCacheOperation().set(code + StringConstant.COLON + CommonConstant.STATUS, 0L);
    }

    /**
     * 当前租户信息
     *
     * @return 当前租户信息
     * <AUTHOR>
     * @date 2023-05-30 16:08
     */
    @Override
    public TenantInfoVo getCurrentTenant() {
        return tenantInfoManagement.getCurrentTenantInfo();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncAppMenu(SyncAppMenuParam param){
        String categoryCode = StringUtils.isBlank(param.getCategoryCode()) ? SysCategoryInfoService.PROJECT_BASE : param.getCategoryCode();
        SysCategoryInfo categoryInfo = sysCategoryInfoService.queryOneByCode(categoryCode);
        if (Objects.isNull(categoryInfo)){
            log.warn("没有找到{}分类的信息，应用&菜单数据同步终止",categoryCode);
            return;
        }

        // 步骤，先删除之前的菜单，然后创建应用，最后再绑定菜单
        try {
            LambdaQueryWrapper<SysMenu> menuQueryWrapper = new LambdaQueryWrapper<>();
            menuQueryWrapper.eq(SysMenu::getSysCategoryId, categoryInfo.getId());
            List<SysMenu> list = sysMenuService.list(menuQueryWrapper);
            for (SysMenu menu:list) {
                SysMenuParam sysMenuParam = new SysMenuParam();
                sysMenuParam.setId(menu.getId());
                sysMenuService.delete(sysMenuParam);
            }
            sysAppService.sync(param.getSysAppParam(),categoryInfo);
            sysMenuService.sync(param.getSysMenuParam(),categoryInfo);
        } catch (Exception e) {
            log.error("同步应用菜单数据发生异常：",e);
        }
    }
}
