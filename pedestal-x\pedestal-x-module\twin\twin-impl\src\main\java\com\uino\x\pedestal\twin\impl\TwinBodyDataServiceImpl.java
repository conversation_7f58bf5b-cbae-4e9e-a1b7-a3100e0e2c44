package com.uino.x.pedestal.twin.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.uino.x.common.core.factory.ExecutorServiceFactory;
import com.uino.x.common.core.factory.PageFactory;
import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.common.core.util.TenantUtils;
import com.uino.x.common.core.util.ZipUtils;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.label.enums.exp.ExpEnumOption;
import com.uino.x.common.objectstorage.template.ObjectStorageTemplate;
import com.uino.x.common.pojo.response.ResponseData;
import com.uino.x.common.pojo.result.PageResult;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.base.*;
import com.uino.x.pedestal.alarm.api.DeviceAlertApi;
import com.uino.x.pedestal.bubble.api.SysBubbleInfoApi;
import com.uino.x.pedestal.bubble.pojo.vo.SysBubbleInfoVo;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.dict.api.SysDictDataApi;
import com.uino.x.pedestal.file.common.domain.FileAsyInfo;
import com.uino.x.pedestal.file.common.properties.DownloadProperties;
import com.uino.x.pedestal.file.common.util.AsyncFileTask;
import com.uino.x.pedestal.file.common.util.FileDownloadUtils;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.pojo.domain.SysLoginUser;
import com.uino.x.pedestal.message.api.RealTimeMessageApi;
import com.uino.x.pedestal.message.pojo.entity.SysRealTimeMessage;
import com.uino.x.pedestal.model.api.ThingsModelsApi;
import com.uino.x.pedestal.model.pojo.entity.ThingsModel;
import com.uino.x.pedestal.performance.api.DevicePerformanceApi;
import com.uino.x.pedestal.placement.api.SceneApi;
import com.uino.x.pedestal.placement.api.service.PositionTransformService;
import com.uino.x.pedestal.placement.common.enums.CommonStatusEnum;
import com.uino.x.pedestal.placement.common.enums.CoordinateTypeEnum;
import com.uino.x.pedestal.placement.common.utils.SceneUtil;
import com.uino.x.pedestal.placement.dao.mapper.SceneRecordMapper;
import com.uino.x.pedestal.placement.pojo.entity.SceneData;
import com.uino.x.pedestal.placement.pojo.entity.SceneRecord;
import com.uino.x.pedestal.placement.pojo.param.ScenePlacementsParam;
import com.uino.x.pedestal.twin.api.service.TwinBodyDataService;
import com.uino.x.pedestal.twin.api.service.TwinClassModelMappingService;
import com.uino.x.pedestal.twin.api.service.TwinClassService;
import com.uino.x.pedestal.twin.common.constant.DefaultTwinConstant;
import com.uino.x.pedestal.twin.common.constant.TwinDictCodeConstant;
import com.uino.x.pedestal.twin.common.constant.TwinFixedColumnConstant;
import com.uino.x.pedestal.twin.common.enums.*;
import com.uino.x.pedestal.twin.common.exception.TwinBodyDataException;
import com.uino.x.pedestal.twin.common.utils.EasyExcelUtils;
import com.uino.x.pedestal.twin.common.utils.FormUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.dao.mapper.TwinAllMapper;
import com.uino.x.pedestal.twin.dao.mapper.TwinBodyDataMapper;
import com.uino.x.pedestal.twin.impl.excel.TwinBodyDataExcelImportListener;
import com.uino.x.pedestal.twin.impl.utils.TwinDataUtils;
import com.uino.x.pedestal.twin.jrm.JrmLockContext;
import com.uino.x.pedestal.twin.pojo.domain.*;
import com.uino.x.pedestal.twin.pojo.entity.TwinAll;
import com.uino.x.pedestal.twin.pojo.entity.TwinClass;
import com.uino.x.pedestal.twin.pojo.entity.TwinClassModelMapping;
import com.uino.x.pedestal.twin.pojo.param.*;
import com.uino.x.pedestal.twin.pojo.vo.TwinCbPointVo;
import com.uino.x.pedestal.twin.pojo.vo.TwinDataPointVo;
import com.uino.x.pedestal.twin.pojo.vo.TwinInfoVo;
import com.uino.x.pedestal.twin.pojo.vo.TwinSceneInfoVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 孪生体数据管理service实现
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/8/23 09:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TwinBodyDataServiceImpl implements TwinBodyDataService {

    private static final ExecutorService EXECUTOR_SERVICE = ExecutorServiceFactory.createThreadPool(TwinBodyDataServiceImpl.class);


    private final TwinBodyDataMapper twinBodyDataMapper;
    private final TwinClassService twinClassService;
    private final SceneApi sceneApi;
    private final SceneRecordMapper sceneRecordMapper;
    private final SysBubbleInfoApi sysBubbleInfoApi;
    private final ThingsModelsApi thingsModelsApi;
    private final RealTimeMessageApi realTimeMessageApi;
    private final TwinAllMapper twinAllMapper;
    private final JrmLockContext jrmLockContext;
    private final TwinClassModelMappingService twinClassModelMappingService;
    private final SysDictDataApi sysDictDataApi;
    private final DeviceAlertApi deviceAlertApi;
    private final DevicePerformanceApi devicePerformanceApi;
    private final ObjectStorageTemplate ost;
    private final ObjectMapper objectMapper;


    @Override
    public void exportExcel(TwinBodyExportExcelParam param, HttpServletResponse response) throws IOException {
        String id = param.getId();
        String ids = param.getDataIds();
        final TwinClass twinClass = Optional.ofNullable(twinClassService.getById(id)).orElseThrow(() -> new TwinBodyDataException("孪生对象不存在"));
        final List<FormItem> formItems = FormUtils.parseListToForm(twinClass.getForm());
        final String filename = "【" + twinClass.getName() + "】" + "孪生体数据";
        final List<List<String>> fixedHead = twinClassService.buildTwinDataFixedHead(twinClass);
        final String exportUnitDictTypeCode = "LUANSHENGTIDAOCHUZIDUANDANWEI";
        Map<String, String> codeValueMap = sysDictDataApi.getCodeValueMapByDictTypeCode(exportUnitDictTypeCode).stream()
                .collect(Collectors.toMap(a -> a.get("code"), a -> a.get("value")));
        if (CollectionUtils.isNotEmpty(codeValueMap)) {
            for (List<String> fixedHeadUnit : fixedHead) {
                if (fixedHeadUnit.size() == 2) {
                    String exportUnit = codeValueMap.get(fixedHeadUnit.get(0));
                    if (StringUtils.isNotBlank(exportUnit)) {
                        fixedHeadUnit.set(1, fixedHeadUnit.get(1) + exportUnit);
                    }
                }
            }
        }

        HashMap<String, String> fieldMap = (HashMap<String, String>) initTableHead(id);
        TwinBodyExportExcelSqlParam sqlParam = new TwinBodyExportExcelSqlParam();
        sqlParam.setHead(fieldMap);
        sqlParam.setTableName(getTableName(id));
        sqlParam.setDefaultTwin(TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId()));
        sqlParam.setDefaultTwin(TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId()));
        sqlParam.setDefaultTwin(TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId()));
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArr = ids.split(",");
            sqlParam.setDataIds(idArr);
        } else {
            sqlParam.setKeyword(param.getKeyword());
            sqlParam.setSceneId(param.getSceneId());
            JSONObject matchJson = param.getMatchJson();
            if (Objects.nonNull(matchJson)) {
                sqlParam.setMatchJson(matchJson);
            }
            sqlParam.setRoomName(param.getRoomName());
            sqlParam.setFloorName(param.getFloorName());
            sqlParam.setBuildingName(param.getBuildingName());
        }

        Page<Map<String, Object>> page = new Page<>(1, 10000);
        IPage<Map<String, Object>> twinList = buildTwinPage(sqlParam, page);
        long total = twinList.getTotal();
        //导出条数大于10000采用压缩包形式导出
        if (total < page.getSize()) {
            EasyExcelUtils.exportByFormItems(filename + ".xlsx", filename, fixedHead, formItems, twinList.getRecords(), true, response);
        } else {
            asyncExportExcel(twinClass, page, sqlParam, fixedHead, formItems);
        }
    }

    private IPage<Map<String, Object>> buildTwinPage(TwinBodyExportExcelSqlParam sqlParam, Page<Map<String, Object>> page) {
        IPage<Map<String, Object>> twinPage;
        final String name = sqlParam.getTableName();
        final String tableName = name.substring(name.indexOf(StringPool.DOT) + 1);
        if (DefaultTwinConstant.ROOM.endsWith(tableName)) {
            twinPage = twinBodyDataMapper.selectDefaultRoomTwin(getSceneDbPre(), page, sqlParam);
        } else if (DefaultTwinConstant.FLOOR.equals(tableName)) {
            twinPage = twinBodyDataMapper.selectDefaultFloorTwin(getSceneDbPre(), page, sqlParam);
        } else if (DefaultTwinConstant.BUILDING.equals(tableName)) {
            twinPage = twinBodyDataMapper.selectDefaultBuildingTwin(getSceneDbPre(), page, sqlParam);
        } else {
            twinPage = twinBodyDataMapper.selectTwinBodyData(page, sqlParam);
        }
        return twinPage;
    }

    private String getSceneDbPre() {
        String tenant = TenantUtils.getTenantByRequest();
        tenant = tenant.toLowerCase();
        return TenantConstant.MASTER.equals(tenant) ? DataSourceConstant.SCENE_X : tenant + StringConstant.UNDERSCORE + DataSourceConstant.SCENE_X;
    }

    /**
     * 异步导出孪生体数据并发送通知
     *
     * @param twinClass 孪生体信息
     * @param page      分页信息
     * @param sqlParam  sql查询参数
     * @param fixedHead 固定表头
     * @param formItems 表单属性
     * <AUTHOR>
     * @date 2022/01/21 17:08:34
     */
    public void asyncExportExcel(TwinClass twinClass, Page<Map<String, Object>> page, TwinBodyExportExcelSqlParam sqlParam, List<List<String>> fixedHead, List<FormItem> formItems) {
        final SysLoginUser sysLoginUser = IdentityContext.getSysLoginUser(null);
        final String twinName = twinClass.getName();
        AsyncUtils.asyncExecutorBean(() -> {
            try {
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String fileId = twinName + "孪生体数据" + DateUtils.nowDateFormatNoSeparator();
                FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(fileId + ".zip");
                String downloadPath = fileAsyInfo.getAbsolutePath();
                String tempPath = downloadPath + FileDownloadUtils.SEP + fileId;
                File tempDir = AsyncFileTask.createDownloadDir(downloadPath, tempPath);
                while (page.hasNext() || page.getCurrent() == 1 || page.getCurrent() == page.getPages()) {
                    IPage<Map<String, Object>> twinList = buildTwinPage(sqlParam, page);
                    long end = twinList.getCurrent() * twinList.getSize();
                    long start = end - twinList.getSize();
                    File file = new File(tempPath, "第" + start + "~" + end + "条数据.xlsx");
                    EasyExcelUtils.exportByFormItems(twinName, fixedHead, formItems, twinList.getRecords(), file);
                    page.setCurrent(page.getCurrent() + 1);
                }
                try {
                    File zip = ZipUtils.zip(tempDir);
                    DateTimeFormatter dateTimeFormatter2 = DateTimeFormatter.ofPattern("yyyyMMdd");
                    ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
                    ost.uploadObject(DownloadProperties.getTempPath(), dateTimeFormatter2.format(fileAsyInfo.getExpiredDate()) + "/" + zip.getName(), zip.getAbsolutePath());
                    // 压缩这个文件夹
                    realTimeMessageApi.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), String.format("导出【" + twinName + "】孪生体数据成功！请在%s日之前下载，过期后，文件将被删除", dateTimeFormatter.format(fileAsyInfo.getExpiredDate())), fileAsyInfo.getDownLoadPath()));
                } catch (Exception e) {
                    log.info("导出【" + twinName + "】孪生体数据失败：", e);
                } finally {
                    try {
                        FileUtils.deleteDirectory(tempDir);
                    } catch (IOException e) {
                        log.info("删除【" + twinName + "】孪生体数据缓存目录失败：", e);
                    }
                }
                log.warn("导出【" + twinName + "】孪生体数据成功");
            } catch (Exception e) {
                realTimeMessageApi.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), "导出【" + twinName + "】孪生体数据失败，请稍后再试！", null));
                log.warn("导出【" + twinName + "】孪生体数据失败，原因：", e);
                throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "导出【" + twinName + "】孪生体数据失败"));
            } finally {
                log.warn("导出【" + twinName + "】孪生体数据完成");
            }
        }, EXECUTOR_SERVICE);
        ResponseData.ok(null, String.format("正在导出【%s】孪生体数据，因导出的数据量较大，这可能需要一段时间，现在您可以进行其他操作，导出完成后将通知您", twinName));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TwinDataPointVo> saveBatchTwinBodyPointInfo(List<TwinDataPointParam> params) {
        Set<Long> twinClassIds = params.stream()
                .map(TwinDataPointParam::getTwinClassId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 当前用户
        final Long userId = IdentityContext.getSysLoginUserId();

        Map<Long, TwinClass> twinClassMap = twinClassService.getMapByIds(twinClassIds);

        for (TwinDataPointParam param : params) {
            Long uuid = param.getUuid();
            if (Objects.isNull(uuid)) {
                param.setUuid(IdUtils.getId());
                param.setCreateUser(userId);
            } else {
                param.setUpdateUser(userId);
            }
            param.setTableName(getPointTableName(param.getTwinClassId(), twinClassMap));

            // 添加孪生对象编码
            String twinClassCode = Optional.ofNullable(twinClassMap.get(param.getTwinClassId())).map(TwinClass::getCode).orElse(null);
            param.setTwinClassCode(twinClassCode);
        }

        // 批量保存孪生体数据
        saveBatchTwinDataByParams(params);

        return paramListToVoList(new TwinDataPointToVoParam(params, twinClassMap));
    }

    @Override
    public List<Map<String, Object>> getTwinDataBySceneId(TwinDataPointQueryParam param) {
        // 获取场景id对应关联的所有场景id
        List<Long> sceneIds = sceneApi.getAllVersionSceneIdsByMainSceneId(param.getSceneId());
        if (CollectionUtils.isEmpty(sceneIds)) {
            return new ArrayList<>();
        }

        // 当前场景数据
        final SceneRecord sceneRecord = Optional.ofNullable(sceneApi.querySceneRecordByUuid(param.getSceneId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "场景数据不存在")));
        // 所有版本的
        final Map<String, SceneData> sceneDataMap = sceneApi.querySceneDataAllChild(sceneRecord.getUuid()).stream().filter(e -> StringUtils.isNotBlank(e.getUserid())).collect(Collectors.toMap(e -> e.getParentSceneUUID() + StringConstant.UNDERSCORE + e.getUserid(), Function.identity(), (k1, k2) -> k1));

        final String userId = Optional.ofNullable(param.getParentUserId())
                .orElse(Optional.ofNullable(param.getUserBuildingId()).orElse(Optional.ofNullable(param.getUserFloorId()).orElse(param.getUserRoomId())));
        List<String> userIds = getTwinUserIds(sceneRecord.getUuid(), userId, sceneDataMap);

        // 孪生体数据相关
        List<TwinClass> twinClasses = twinClassService.getListByParam(new TwinClassListParam(null, TwinClassDataTypeEnum.DEFAULT_CODES, param.getTwinClassLevel(), param.getTwinClassIds(), true, param.getTwinClassGroupId(), param.getTwinClassCodes()));
        Set<Long> bubbleInfoIds = twinClasses.stream().map(TwinClass::getBubbleInfoId).collect(Collectors.toSet());
        Map<Long, SysBubbleInfoVo> bubbleInfoMap = sysBubbleInfoApi.getVoMapByIds(bubbleInfoIds);

        // 模型相关
        final Set<String> thingModelUuids = twinClasses.stream().map(TwinClass::getThingsModelUuid).filter(Objects::nonNull).collect(Collectors.toSet());
        final Map<String, ThingsModel> thingsModelMap = thingsModelsApi.getListByModelIds(thingModelUuids).stream().collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1));

        // 场景ID映射
        final Map<Long, Long> enableVersionMap = sceneApi.getEnableVersionMapBySceneIds(sceneIds);

        // 所属用户
        Long belongUser = Optional.ofNullable(param.getOnlyMine()).orElse(false) ? IdentityContext.getSysLoginUserId() : null;

        // 模型映射
        final Set<Long> twinClassIds = twinClasses.stream().map(TwinClass::getId).collect(Collectors.toSet());
        final Map<Long, List<TwinClassModelMapping>> modelMappingGroupMap = twinClassModelMappingService.getModelMappingGroupMap(twinClassIds);

        // 异步处理
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>(twinClasses.size());
        for (TwinClass twinClass : twinClasses) {
            final List<TwinClassModelMapping> modelMappings = Optional.ofNullable(modelMappingGroupMap.get(twinClass.getId())).orElse(new ArrayList<>());
            CompletableFuture<List<Map<String, Object>>> future = AsyncUtils.asyncExecutorBean(() -> getSingleTwinData(twinClass, param, bubbleInfoMap, thingsModelMap, sceneIds, belongUser, enableVersionMap, sceneRecord, userIds, sceneDataMap, modelMappings), EXECUTOR_SERVICE).exceptionally(ex -> {
                log.error("查询点位异常", ex);
                return new ArrayList<>();
            });
            futures.add(future);
        }

        // 合并
        List<Map<String, Object>> allDataList = new ArrayList<>(twinClasses.size());
        futures.forEach(e -> allDataList.addAll(e.join()));

        // 忽略列
        if (!CollectionUtils.isEmpty(param.getIgnoreKeys())) {
            return allDataList.stream().peek(e -> {
                for (String ignoreKey : param.getIgnoreKeys()) {
                    e.remove(ignoreKey);
                }
            }).collect(Collectors.toList());
        }

        return allDataList;
    }

    @Override
    public List<String> getTwinUserIds(Long sceneId, String userId, Map<String, SceneData> sceneDataMap) {

        final List<SceneData> sceneDataList = new ArrayList<>(sceneDataMap.values());

        // 本级和下级的userIds
        List<String> userIds = new ArrayList<>();

        // 当前的场景数据
        final SceneData sceneData = Optional.ofNullable(sceneDataMap.get(sceneId + StringConstant.UNDERSCORE + userId)).orElse(new SceneData());
        // 查询这个sceneData下一层的数据，通过原有的上下级campusBuilderId索取
        switch (Optional.ofNullable(sceneData.getDataType()).orElse("")) {
            case SceneUtil.BUILDING_NODE -> {
                // 建筑
                userIds = sceneDataList.stream().filter(e -> StringUtils.isNotBlank(e.getUserid()) && Objects.equals(e.getDataType(), SceneUtil.PLAN_NODE)).filter(e -> Objects.equals(e.getParentCBID(), sceneData.getCampusBuilderId())).map(SceneData::getUserid).collect(Collectors.toList());
                userIds.add(sceneData.getUserid());
            }
            case SceneUtil.PLAN_NODE -> {
                // 楼层
                userIds = sceneDataList.stream().filter(e -> StringUtils.isNotBlank(e.getUserid()) && Objects.equals(e.getDataType(), SceneUtil.ROOM_NODE)).filter(e -> Objects.equals(e.getParentCBID(), sceneData.getCampusBuilderId())).map(SceneData::getUserid).collect(Collectors.toList());
                userIds.add(sceneData.getUserid());
            }
            case SceneUtil.ROOM_NODE ->{
                // 房间
                userIds.add(sceneData.getUserid());
            }
            default ->{
                // 默认
                userIds.add("-1");
            }
        }
        return userIds;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer importExcel(MultipartFile file, String id, String type, String positionType) {

        String tableName = getTableName(id);
        TwinClass twinClass = getTwinClass(id);
        return jrmLockContext.<Integer>tableLock(AutoTableTypeEnum.TWIN.getTablePrefix() + twinClass.getCode(), key -> {

            // 上传的文件不能为空
            if (Objects.isNull(file) || file.isEmpty()) {
                throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "请选择文件"));
            }
            AssertUtils.isTrue(TwinDictCodeConstant.UPDATE.equals(type) || TwinDictCodeConstant.ADD.equals(type), HttpStatus.BAD_REQUEST, "操作类型不合法");
            List<Column> columnList = parseJsonToTwinData(id);
            InputStream inputStream;
            try {
                inputStream = file.getInputStream();
            } catch (IOException e) {
                log.error("Twin data file get InputStream fail!", e);
                throw new TwinBodyDataException(HttpStatus.FORBIDDEN, "文件流获取失败");
            }
            TwinBodyDataExcelImportListener twinBodyDataExcelImportListener = new TwinBodyDataExcelImportListener(twinBodyDataMapper, getClassType(id), getClassLevel(id), positionType, twinAllMapper);
            ExcelReaderBuilder readWorkBook = EasyExcel.read(inputStream, twinBodyDataExcelImportListener);
            ExcelReaderSheetBuilder sheet = readWorkBook.sheet();

            twinBodyDataExcelImportListener.setFormTwin(TwinDictCodeConstant.FORM.equals(twinClass.getDataType()));
            twinBodyDataExcelImportListener.setTableName(tableName);
            twinBodyDataExcelImportListener.setColumnList(columnList);
            twinBodyDataExcelImportListener.setType(type);
            twinBodyDataExcelImportListener.setTwinClassCode(twinClass.getCode());
            twinBodyDataExcelImportListener.setForm(FormUtils.parseListToForm(twinClass.getForm()));
            sheet.doRead();
            int successTotal = twinBodyDataExcelImportListener.getSuccessTotal();
            if (successTotal == 0) {
                throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "上传的导入文件不允许为空！"));
            }
            return twinBodyDataExcelImportListener.getSuccessTotal();
        });
    }

    @Override
    public void downloadTwinDataBySceneId(TwinDataPointQueryParam param, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> twinDataBySceneId = this.getTwinDataBySceneId(param);
        if (twinDataBySceneId.size() > 0) {
            String twinClassId = twinDataBySceneId.get(0).get("twinClassId").toString();
            List<String> ids = twinDataBySceneId.stream().map(e -> e.get("uuid").toString()).toList();
            TwinBodyExportExcelParam twinBodyExportExcelParam = new TwinBodyExportExcelParam();
            twinBodyExportExcelParam.setIdFlag(true);
            twinBodyExportExcelParam.setId(twinClassId);
            String dataIds = StringUtils.join(ids, ",");
            twinBodyExportExcelParam.setDataIds(dataIds);
            this.exportExcel(twinBodyExportExcelParam, response);
        }
    }

    private List<Map<String, Object>> getSingleTwinData(TwinClass twinClass, TwinDataPointQueryParam param, Map<Long, SysBubbleInfoVo> bubbleInfoMap, Map<String, ThingsModel> thingsModelMap, List<Long> sceneIds, Long userId, Map<Long, Long> enableVersionMap, SceneRecord sceneRecord, List<String> userIds, Map<String, SceneData> sceneDataMap, List<TwinClassModelMapping> modelMappings) {
        // 表名
        final String tableName = twinClassService.getTwinDataTableName(twinClass);

        // 孪生体信息
        List<Map<String, Object>> theDataList;
        if (!TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId())) {
            theDataList = twinBodyDataMapper.getTwinBodyData(new TwinDataQueryParam(tableName, param.getTwinDataIds(), sceneIds, param.getDataSources(), param.getParentUserId(), userId, param.getUserBuildingId(), param.getUserFloorId(), param.getUserRoomId()));
        } else {
            theDataList = twinBodyDataMapper.getDefaultTwinData(new DefaultTwinDataQueryParam(tableName, param.getTwinDataIds(), sceneRecord.getSceneCode(), userIds));
        }

        // 为每条数据添加附加信息，包括动态气泡信息
        theDataList.forEach(data -> {
            // 先应用模型映射，可能会改变模型UUID
            twinClassModelMappingService.setModelUuidByMapping(data, modelMappings);

            // 根据映射后的模型UUID和原始映射信息，动态选择气泡信息
            SysBubbleInfoVo bubbleInfoVo = getDynamicBubbleInfo(data, twinClass, modelMappings, bubbleInfoMap);

            // 获取模型信息
            final String modelUuid = (String) data.get(TwinFixedColumnConstant.ALIAS_THINGS_MODEL_UUID);
            final ThingsModel thingsModel = Optional.ofNullable(thingsModelMap.get(modelUuid)).orElse(new ThingsModel());

            // 添加附加信息
            addTwinClassInfoByData(data, twinClass, bubbleInfoVo, enableVersionMap, thingsModel, Collections.singletonList(sceneRecord), sceneDataMap, modelMappings);
        });

        return theDataList;
    }

    /**
     * 根据数据内容和模型映射动态获取气泡信息
     * @param data 孪生体数据
     * @param twinClass 孪生体分类
     * @param modelMappings 模型映射列表
     * @param bubbleInfoMap 气泡信息映射
     * @return 气泡信息
     */
    private SysBubbleInfoVo getDynamicBubbleInfo(Map<String, Object> data, TwinClass twinClass, List<TwinClassModelMapping> modelMappings, Map<Long, SysBubbleInfoVo> bubbleInfoMap) {
        // 默认使用孪生体分类的气泡信息
        SysBubbleInfoVo defaultBubbleInfo = Optional.ofNullable(bubbleInfoMap.get(twinClass.getBubbleInfoId())).orElse(new SysBubbleInfoVo());

        // 如果没有模型映射，直接返回默认气泡信息
        if (CollectionUtils.isEmpty(modelMappings)) {
            return defaultBubbleInfo;
        }

        // 遍历模型映射，找到匹配的映射
        for (TwinClassModelMapping modelMapping : modelMappings) {
            final String fieldName = modelMapping.getFieldName();
            final String condition = modelMapping.getCondition();
            final String content = modelMapping.getContent();
            final Long mappingBubbleInfoId = modelMapping.getBubbleInfoId();

            // 如果映射中没有气泡信息ID，跳过
            if (mappingBubbleInfoId == null) {
                continue;
            }

            // 匹配字段的具体值
            final String value = Optional.ofNullable(data.get(fieldName)).map(String::valueOf).orElse(null);

            // 是否匹配
            boolean isMatch = false;

            // 匹配条件判断
            switch (TwinConditionsEnum.getTwinConditionsEnumByCode(condition)) {
                case LIKE:
                    isMatch = StringUtils.contains(value, content);
                    break;
                case NOT_LIKE:
                    isMatch = !StringUtils.contains(value, content);
                    break;
                case INCLUDES:
                    isMatch = isMatchIncludes(value, content);
                    break;
                case NOT_INCLUDES:
                    isMatch = !isMatchIncludes(value, content);
                    break;
                case EQUALS:
                    isMatch = Objects.equals(value, content);
                    break;
                case NOT_EQUALS:
                    isMatch = !Objects.equals(value, content);
                    break;
                case BEGIN_WITH:
                    isMatch = StringUtils.startsWith(value, content);
                    break;
                case END_WITH:
                    isMatch = StringUtils.endsWith(value, content);
                    break;
                case IS_NULL:
                    isMatch = Objects.isNull(value);
                    break;
                case NOT_NULL:
                    isMatch = Objects.nonNull(value);
                    break;
                case IS_EMPTY:
                    isMatch = Objects.isNull(value) || StringUtils.isBlank(value) || Objects.equals("[]", value);
                    break;
                case NOT_EMPTY:
                    isMatch = Objects.nonNull(value) && StringUtils.isNotBlank(value) && !Objects.equals("[]", value);
                    break;
                default:
                    break;
            }

            // 如果匹配，返回映射中的气泡信息
            if (isMatch) {
                return Optional.ofNullable(bubbleInfoMap.get(mappingBubbleInfoId)).orElse(defaultBubbleInfo);
            }
        }

        // 如果没有匹配的映射，返回默认气泡信息
        return defaultBubbleInfo;
    }

    /**
     * 判断是否包含指定内容（用于INCLUDES条件判断）
     */
    private boolean isMatchIncludes(String value, String content) {
        if (Objects.isNull(content) || Objects.isNull(value)) {
            return false;
        }

        String theValue = value;
        if (StringUtils.startsWith(value, "[") && StringUtils.endsWith(value, "]")) {
            theValue = StringUtils.substring(value, 1, value.length() - 1);
        }

        final Set<String> contentSet = Arrays.stream(StringUtils.split(content, ","))
                .map(e -> "\"" + e + "\"")
                .collect(Collectors.toSet());

        final Set<String> valueSet = Arrays.stream(StringUtils.split(theValue, ","))
                .collect(Collectors.toSet());

        return valueSet.containsAll(contentSet);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TwinDataPointVo> saveCadTwinData(TwinDataCadParam param) {
        if (CollectionUtils.isEmpty(param.getCadDataList())) {
            return new ArrayList<>();
        }

        // 孪生对象
        TwinClass twinClass = Optional.ofNullable(twinClassService.getById(param.getTwinClassId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        // 获取孪生体动态列
        final List<FormItem> formItems = FormUtils.parseListToForm(twinClass.getForm());

        // 数据库名
        String tableName = twinClassService.getTwinDataTableName(twinClass);

        // 保存参数列表
        List<TwinDataPointParam> params = new ArrayList<>(param.getCadDataList().size());

        // 当前用户
        final Long userId = IdentityContext.getSysLoginUserId();

        for (Map<String, Object> data : param.getCadDataList()) {
            // 得到孪生体数据动态数据保存值
            final Map<String, Object> twinDataMap = buildTwinDataMap(formItems, data);

            // 构建保存参数
            TwinDataPointParam twinDataPointParam = buildTwinDataPointParam(data, twinClass, tableName, param.getSceneId(), param.getParentUserId(), twinDataMap, TwinDataSourceEnum.CAD, userId);

            // 添加到保存参数列表
            params.add(twinDataPointParam);
        }

        // 批量保存孪生体数据
        saveBatchTwinDataByParams(params);

        Map<Long, TwinClass> twinClassMap = new HashMap<>(2);
        twinClassMap.put(twinClass.getId(), twinClass);

        return paramListToVoList(new TwinDataPointToVoParam(params, twinClassMap));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TwinDataPointVo> saveGisTwinData(TwinDataGisParam param) {
        if (CollectionUtils.isEmpty(param.getGisDataList())) {
            return new ArrayList<>();
        }
        // 孪生对象
        TwinClass twinClass = Optional.ofNullable(twinClassService.getById(param.getTwinClassId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        // 获取孪生体动态列
        final List<FormItem> formItems = FormUtils.parseListToForm(twinClass.getForm());

        // 数据库名
        String tableName = twinClassService.getTwinDataTableName(twinClass);

        // 保存参数列表
        List<TwinDataPointParam> params = new ArrayList<>(param.getGisDataList().size());

        // 当前用户
        final Long userId = IdentityContext.getSysLoginUserId();

        for (Map<String, Object> data : param.getGisDataList()) {
            // 得到孪生体数据保存值
            final Map<String, Object> twinDataMap = buildTwinDataMap(formItems, data);

            // 构建保存参数
            TwinDataPointParam twinDataPointParam = buildTwinDataPointParam(data, twinClass, tableName, param.getSceneId(), param.getParentId(), twinDataMap, TwinDataSourceEnum.GIS, userId);

            // 坐标系
            final Object position = data.get(PositionTransformService.POSITION);
            if (Objects.nonNull(position)) {
                if (Objects.equals(param.getType(), CoordinateTypeEnum.GCJ02.name())) {
                    twinDataPointParam.setGcj02Position(String.valueOf(position));
                }
                if (Objects.equals(param.getType(), CoordinateTypeEnum.WGS84.name())) {
                    twinDataPointParam.setWgs84Position(String.valueOf(position));
                }
            }

            // 添加到保存参数列表
            params.add(twinDataPointParam);
        }

        // 批量保存孪生体数据
        saveBatchTwinDataByParams(params);

        Map<Long, TwinClass> twinClassMap = new HashMap<>(2);
        twinClassMap.put(twinClass.getId(), twinClass);

        return paramListToVoList(new TwinDataPointToVoParam(params, twinClassMap));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBatchTwinBodyPointInfo(List<TwinDataPointDeleteParam> params) {
        Set<Long> twinClassIds = params.stream().map(TwinDataPointDeleteParam::getTwinClassId).filter(Objects::nonNull).collect(Collectors.toSet());

        Map<Long, TwinClass> twinClassMap = twinClassService.getMapByIds(twinClassIds);

        for (TwinDataPointDeleteParam param : params) {
            param.setTableName(getPointTableName(param.getTwinClassId(), twinClassMap));
            twinBodyDataMapper.deleteDataPointInfo(param);
            deviceAlertApi.deleteByTwinUuidByLatest(param.getUuid());
            deviceAlertApi.deleteByTwinUuidByHistory(param.getUuid());
            devicePerformanceApi.deleteByTwinUuidByLatest(param.getUuid());
            devicePerformanceApi.deleteByTwinUuidByHistory(param.getUuid());
        }

        List<Long> twinUuids = params.stream().map(TwinDataPointDeleteParam::getUuid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(twinUuids)) {
            String tenant = TenantUtils.getTenantByRequest().toLowerCase();
            twinAllMapper.deleteAllBatch(twinUuids, DataSourceConstant.currentTenantTwinSchema(tenant));
        }
    }

    @Override
    public List<TwinDataPointVo> paramListToVoList(TwinDataPointToVoParam toVoParam) {
        final List<TwinDataPointParam> params = toVoParam.getParams();
        final Map<Long, TwinClass> twinClassMap = toVoParam.getTwinClassMap();

        // 气泡信息
        Set<Long> bubbleInfoIds = twinClassMap.values().stream().map(TwinClass::getBubbleInfoId).collect(Collectors.toSet());
        Map<Long, SysBubbleInfoVo> bubbleInfoMap = sysBubbleInfoApi.getVoMapByIds(bubbleInfoIds);

        // 模型映射
        final Set<Long> twinClassIds = twinClassMap.values().stream().map(TwinClass::getId).collect(Collectors.toSet());
        final Map<Long, List<TwinClassModelMapping>> modelMappingGroupMap = twinClassModelMappingService.getModelMappingGroupMap(twinClassIds);
        final Set<String> mappingModelUuids = modelMappingGroupMap.values().stream().flatMap(Collection::stream).map(TwinClassModelMapping::getThingsModelUuid).collect(Collectors.toSet());

        // 模型相关
        final Set<String> thingModelUuids = twinClassMap.values().stream().map(TwinClass::getThingsModelUuid).filter(Objects::nonNull).collect(Collectors.toSet());
        thingModelUuids.addAll(mappingModelUuids);
        final Map<String, ThingsModel> thingsModelMap = thingsModelsApi.getListByModelIds(thingModelUuids).stream().collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1));


        // 返回信息
        List<TwinDataPointVo> results = new ArrayList<>(params.size());
        for (TwinDataPointParam param : params) {
            TwinDataPointVo result = BeanUtils.copyToBean(param, TwinDataPointVo.class);
            if (Objects.nonNull(result.getTwinClassId())) {
                final TwinClass twinClass = Optional.ofNullable(twinClassMap.get(result.getTwinClassId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象不存在")));
                final SysBubbleInfoVo bubbleInfoVo = Optional.ofNullable(bubbleInfoMap.get(twinClass.getBubbleInfoId())).orElse(new SysBubbleInfoVo());

                // 模型映射获取具体的模型id
                twinClassModelMappingService.setModelUuidByMapping(result.getFormData(), modelMappingGroupMap.get(twinClass.getId()));
                final String thingsModelUuid = Optional.ofNullable(result.getFormData().get(TwinFixedColumnConstant.ALIAS_THINGS_MODEL_UUID))
                        .map(String::valueOf)
                        .orElse(twinClass.getThingsModelUuid());

                final ThingsModel thingsModel = Optional.ofNullable(thingsModelMap.get(thingsModelUuid)).orElse(new ThingsModel());

                result.setTwinClassName(twinClass.getName());
                result.setBubbleInfoVo(bubbleInfoVo);
                result.setThingsModelUuid(thingsModel.getModelId());
//                result.setForm(twinBodyDataManagement.buildFormValues(twinClass.getForm(), result.getFormData()));
                result.setForm(twinClass.getForm());
                result.setThingsModelType(thingsModel.getType());
                results.add(result);
            }
        }

        return results;
    }

    @Override
    public void exportTwinTemplate(Long twinClassId, HttpServletResponse response) throws IOException {
        exportTwinTemplate(twinClassId, null, response);
    }

    @Override
    public void exportTwinTemplate(Long twinClassId, List<String> coordinateSys, HttpServletResponse response) throws IOException {
        final TwinClass twinClass = Optional.ofNullable(twinClassService.getById(twinClassId)).orElseThrow(() -> new TwinBodyDataException("孪生对象不存在"));
        final List<FormItem> formItems = FormUtils.parseListToForm(twinClass.getForm());
        final LinkedList<List<String>> fixedHead = twinClassService.buildTwinDataFixedHead(twinClass);
        final String filename = "【" + twinClass.getName() + "】" + "孪生体新增模板.xlsx";

        // 排除坐标系存在
        // wgs84_position gcj02_position
        final boolean exist = !CollectionUtils.isEmpty(coordinateSys);

        final String exportUnitDictTypeCode = "LUANSHENGTIDAOCHUZIDUANDANWEI";
        Map<String, String> codeValueMap = sysDictDataApi.getCodeValueMapByDictTypeCode(exportUnitDictTypeCode).stream()
                .collect(Collectors.toMap(a -> a.get("code"), a -> a.get("value")));

        ArrayList<List<String>> removeList = new ArrayList<>();

        for (List<String> fixedHeadUnit : fixedHead) {
            if (fixedHeadUnit.size() == 2) {
                String headName = fixedHeadUnit.get(0);
                String exportUnit = codeValueMap.get(headName);
                if (StringUtils.isNotBlank(exportUnit)) {
                    fixedHeadUnit.set(1, fixedHeadUnit.get(1) + exportUnit);
                }
                // 添加判断坐标系
                if (exist) {
                    coordinateSys.forEach((coordinateSysUnit) -> {
                        // 当前headName是排除的坐标系
                        if (StringUtils.isNotBlank(coordinateSysUnit) && coordinateSysUnit.equals(headName)) {
                            removeList.add(fixedHeadUnit);
                        }
                    });
                }
            }
        }
        removeList.forEach(fixedHead::remove);

        // 判断是否为地图层级的孪生体，增加必填标识
        // TODO:删除此处的必填标识，可能会影响其他地方 暂未发现
//        if (Objects.equals(twinClass.getLevel(), TwinClassLevelEnum.MAP.getCode())) {
//            for (List<String> fixedHeadUnit : fixedHead) {
//                if (fixedHeadUnit.size() == 2) {
//                    if (Objects.equals(fixedHeadUnit.get(0), TwinFixedColumnConstant.COLUMN_WGS84_POSITION)
//                            || Objects.equals(fixedHeadUnit.get(0), TwinFixedColumnConstant.COLUMN_GCJ02_POSITION)
//                            || Objects.equals(fixedHeadUnit.get(0), TwinFixedColumnConstant.COLUMN_GIS_HEIGHT)) {
//                        fixedHeadUnit.set(1, fixedHeadUnit.get(1) + "（必填）");
//                    }
//                }
//            }
//        }

        EasyExcelUtils.exportByFormItems(filename, "孪生体数据", fixedHead, formItems, new ArrayList<>(), false, response);
    }

    @Override
    public PageResult<Map<String, Object>> page(TwinDataPageParam param) {
        // 孪生对象
        final TwinClass twinClass = Optional.ofNullable(twinClassService.getById(param.getTwinClassId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

        // 气泡信息
        final SysBubbleInfoVo bubbleInfoVo = Optional.ofNullable(sysBubbleInfoApi.getById(twinClass.getBubbleInfoId())).map(sysBubbleInfoApi::entityToVO).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "气泡信息不存在")));

        // 模型信息
        final ThingsModel thingsModel = Optional.ofNullable(twinClass.getThingsModelUuid()).flatMap(thingsModelsApi::getOneByModelId).orElse(new ThingsModel());

        // 数据库表名
        final String tableName = twinClassService.getTwinDataTableName(twinClass);

        // 场景ID
        final List<Long> sceneIds = sceneApi.getAllVersionSceneIdsByMainSceneId(param.getSceneId());

        // 场景数据
        final SceneRecord sceneRecord = Optional.ofNullable(sceneApi.querySceneRecordByUuid(param.getSceneId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "场景数据不存在")));
        final Map<String, SceneData> sceneDataMap = sceneApi.querySceneDataAllChild(sceneRecord.getUuid()).stream().collect(Collectors.toMap(SceneData::getCampusBuilderId, Function.identity(), (k1, k2) -> k1));

        // 点位数据源
        final List<String> dataSources = param.getDataSources();

        // 数据库查询参数
        final TwinDataQueryParam queryParam = new TwinDataQueryParam(tableName, sceneIds, dataSources);

        // 场景ID映射
        final Map<Long, Long> enableVersionMap = sceneApi.getEnableVersionMapBySceneIds(sceneIds);

        // 模型映射
        List<TwinClassModelMapping> modelMappings = twinClassModelMappingService.getAllByTwinClassId(twinClass.getId());

        // 分页信息
        final IPage<Map<String, Object>> page = twinBodyDataMapper.pageTwinBodyData(PageFactory.defaultPage(), queryParam);
        page.getRecords().forEach(e -> addTwinClassInfoByData(e, twinClass, bubbleInfoVo, enableVersionMap, thingsModel, Collections.singletonList(sceneRecord), sceneDataMap, modelMappings));

        return new PageResult<>(page);
    }

    private void paramsSetRoomInfo(List<TwinDataPointParam> params) {
        // 检验是否在房间里面
        final String sceneId = params.get(0).getSceneId();
        if (Objects.nonNull(sceneId)) {
            final String parentUserId = params.get(0).getParentUserId();
            final SceneRecord sceneRecord = Optional.ofNullable(sceneApi.querySceneRecordByUuid(Long.valueOf(sceneId))).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "场景数据不存在")));
            List<SceneData> currentSceneData = sceneApi.querySceneDataAllChild(sceneRecord.getUuid());
            SceneData querySceneData = currentSceneData.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getUserid()) && StringUtils.equals(parentUserId, e.getUserid())).findFirst().orElse(null);
            if (null == querySceneData) {
                return;
            }
            final List<SceneData> sceneDataList = currentSceneData.stream().filter(e -> Objects.equals(e.getDataType(), SceneUtil.ROOM_NODE)).filter(e -> Objects.equals(e.getParentCBID(), querySceneData.getCampusBuilderId())).toList();
            for (TwinDataPointParam param : params) {
                if (StringUtils.isBlank(param.getPosition())) {
                    continue;
                }

                final JSONArray cbPosition = JSONArray.parseArray(param.getPosition());
                if (cbPosition.size() != 3) {
                    continue;
                }
                TwinCbPointVo point = new TwinCbPointVo(new BigDecimal(cbPosition.getString(0)), new BigDecimal(cbPosition.getString(2)), new BigDecimal(cbPosition.getString(1)));

                for (SceneData sceneData : sceneDataList) {
                    if (StringUtils.isBlank(sceneData.getCorners())) {
                        continue;
                    }
                    final JSONArray corners = JSONArray.parseArray(sceneData.getCorners());
                    List<TwinCbPointVo> surface = new ArrayList<>(corners.size());
                    for (int i = 0; i < corners.size(); i++) {
                        JSONArray jsonArray = corners.getJSONArray(i);
                        surface.add(new TwinCbPointVo(new BigDecimal(jsonArray.getString(0)), new BigDecimal(jsonArray.getString(2)), new BigDecimal(jsonArray.getString(1))));
                    }

                    // 在房间里面
                    if (TwinDataUtils.pointInSurface(point, surface)) {
                        if (StringUtils.isBlank(sceneData.getHolescorners())) {
                            param.setUserRoomId(sceneData.getCampusBuilderId());
                            break;
                        }


                        boolean inHoles = false;
                        final JSONArray holesCorners = JSONArray.parseArray(sceneData.getHolescorners());
                        for (int i = 0; i < holesCorners.size(); i++) {
                            JSONArray holesCorner = holesCorners.getJSONArray(i);
                            List<TwinCbPointVo> holesSurface = new ArrayList<>(holesCorner.size());
                            for (int j = 0; j < holesCorner.size(); j++) {
                                JSONArray jsonArray = holesCorner.getJSONArray(j);
                                holesSurface.add(new TwinCbPointVo(new BigDecimal(jsonArray.getString(0)), new BigDecimal(jsonArray.getString(2)), new BigDecimal(jsonArray.getString(1))));

                                if (TwinDataUtils.pointInSurface(point, holesSurface)) {
                                    inHoles = true;
                                    break;
                                }
                            }

                            if (inHoles) {
                                break;
                            }
                        }

                        if (!inHoles) {
                            param.setUserRoomId(sceneData.getCampusBuilderId());
                            break;
                        }
                    }
                }
            }
        }
    }

    private void setOriginTableName(TwinDataScopeQueryParam param, TwinClass originTwinClass) {
        if (Objects.isNull(param.getOriginUuid())) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "原点孪生体ID不存在"));
        }

        // 填充原点对应的数据库表名
        param.setOriginTableName(twinClassService.getTwinDataTableName(originTwinClass));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatchTwinDataByParams(List<TwinDataPointParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }

        // 校验唯一标识
        checkUniqueValue(params);

        // 参数处理，增加依赖表
        List<TwinAll> TwinAllList = new ArrayList<>(params.size());
        for (TwinDataPointParam param : params) {
            final String gisHeight = Optional.ofNullable(param.getGisHeight())
                    .filter(StringUtils::isNotBlank).map(BigDecimal::new)
                    .map(e -> e.setScale(2, RoundingMode.HALF_UP))
                    .map(String::valueOf)
                    .orElse(null);
            param.setGisHeight(gisHeight);

            TwinAllList.add(TwinAll.of(param));
        }

        // 检测是否在房间内，填充房间相关信息
        paramsSetRoomInfo(params);

        // 保存孪生体
        twinBodyDataMapper.saveBatchDataPointInfo(params);

        // 保存依赖表
        String tenant = TenantUtils.getTenantByRequest();
        twinAllMapper.saveAllBatch(TwinAllList, DataSourceConstant.currentTenantTwinSchema(tenant));
    }

    @Override
    public void checkUniqueValue(List<TwinDataPointParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }

        // 是否有自定义属性
        final boolean hasFormData = params.stream().anyMatch(e -> !CollectionUtils.isEmpty(e.getFormData()));

        // 校验唯一标识
        if (hasFormData) {
            final Set<Long> twinClassIds = params.stream().map(TwinDataPointParam::getTwinClassId).collect(Collectors.toSet());
            // 孪生对象id分组
            final Map<Long, TwinClass> twinClassMap = twinClassService.listByIds(twinClassIds)
                    .stream()
                    .collect(Collectors.toMap(TwinClass::getId, Function.identity(), (k1, k2) -> k1));
            // 孪生体数据分类id分组
            final Map<Long, List<TwinDataPointParam>> paramsMap = params.stream().collect(Collectors.groupingBy(TwinDataPointParam::getTwinClassId));
            paramsMap.forEach((k, v) -> {
                // 校验孪生对象
                final TwinClass theTwinClass = Optional.ofNullable(twinClassMap.get(k))
                        .orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生对象不存在")));

//                jrmLockContext.tableLock(AutoTableTypeEnum.TWIN.getTablePrefix() + getTwinClass(String.valueOf(k)).getCode(), () -> {

                    // 获取唯一值表单元素列表
                    final List<FormItem> formItems = FormUtils.parseListToForm(theTwinClass.getForm())
                            .stream()
                            .filter(e -> Optional.ofNullable(e.getOptions().getUnique()).orElse(false) || Objects.equals(e.getModel(), TwinFixedColumnConstant.COLUMN_UNIQUE_CODE))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(formItems)) {
                        return;
                    }

                    // 校验表单配置的唯一值
                    for (FormItem formItem : formItems) {

                        final List<Object> dataList = v.stream()
                                .map(e -> e.getFormData().get(formItem.getModel()))
                                .filter(e -> Objects.nonNull(e) && !"".equals(e))
                                .toList();
                        final List<Object> distinctDataList = dataList.stream().distinct().collect(Collectors.toList());
                        if (dataList.size() != distinctDataList.size()) {
                            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "字段【" + formItem.getLabel() + "】唯一值重复"));
                        }

                        if (!CollectionUtils.isEmpty(distinctDataList)) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("tableName", v.get(0).getTableName());
                            map.put("ids", v.stream().map(TwinDataPointParam::getUuid).filter(Objects::nonNull).collect(Collectors.toList()));
                            map.put("rowName", formItem.getModel());
                            map.put("rowVal", distinctDataList);
                            List<Object> reList = twinBodyDataMapper.countBatchRowUnique(map);
                            if (reList.size() > 0) {
                                throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "字段【" + formItem.getLabel() + "】为唯一值，不能重复"));
                            }
                        }
                    }
//                });
            });
        }
    }

    @Override
    public List<SceneRecord> getSceneListByTwinCode(String twinClassCode) {
        final Optional<TwinClass> twinClass = Optional.ofNullable(twinClassService.findByCode(twinClassCode));
        if (twinClass.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果是默认孪生体
        if (TwinFixedColumnConstant.isDefaultTwin(twinClass.get().getGroupId())) {
            return sceneApi.getAllMainScene();
        }

        final List<Map<String, Object>> dataList = twinBodyDataMapper.selectTwinDataList(twinClassService.getTwinDataTableName(twinClass.get()), null);
        final Set<Long> sceneIds = dataList.stream()
                .map(e -> e.get(TwinFixedColumnConstant.COLUMN_SCENE_ID))
                .filter(Objects::nonNull)
                .filter(e -> !StringPool.EMPTY.equals(e))
                .map(e -> Long.valueOf(String.valueOf(e)))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(sceneIds)) {
            return new ArrayList<>();
        }

        return sceneApi.querySceneRecordListByUuids(sceneIds).stream().filter(e -> Objects.isNull(e.getParentUuid())).collect(Collectors.toList());
    }

    @Override
    public int correctTwinCbInfo(TwinInfoCorrectParam param) {
        // 查询对应孪生对象
        final LambdaQueryWrapper<TwinClass> twinClassWrapper = Wrappers.<TwinClass>lambdaQuery().eq(TwinClass::getLevel, TwinClassLevelEnum.PARK.getCode()).eq(TwinClass::getStatus, StatusEnum.ENABLE.getCode()).in(!CollectionUtils.isEmpty(param.getTwinClassIds()), TwinClass::getId, param.getTwinClassIds());
        final List<TwinClass> twinClasses = twinClassService.list(twinClassWrapper);

        // 如果孪生对象列表为空，直接返回
        if (CollectionUtils.isEmpty(twinClasses)) {
            return 0;
        }

        // 获取基准点位坐标
        final BigDecimal offsetPosX = param.getOffset().get(0);
        final BigDecimal offsetPosY = param.getOffset().get(1);
        if (Objects.isNull(offsetPosX) || Objects.isNull(offsetPosY)) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "偏移量坐标错误，不能校准"));
        }

        // 获取原始sceneId和启用的sceneId的映射
        final List<Long> sceneIds = sceneApi.getVersionSceneIds(param.getSceneId());

        // 校准每个孪生体
        List<CompletableFuture<Integer>> resultFutures = new ArrayList<>();
        for (TwinClass twinClass : twinClasses) {
            final String tableName = twinClassService.getTwinDataTableName(twinClass);

            final CompletableFuture<Integer> resultFuture = AsyncUtils.asyncExecutorBean(() -> buildCbPosParam(twinClass, tableName, sceneIds, param, offsetPosX, offsetPosY), EXECUTOR_SERVICE).exceptionally(ex -> {
                log.error("查询点位异常", ex);
                return 0;
            });
            resultFutures.add(resultFuture);
        }

        // 返回校准的点位信息
        int correctCount = 0;
        for (CompletableFuture<Integer> resultFuture : resultFutures) {
            correctCount += resultFuture.join();
        }
        return correctCount;
    }

    @Override
    public List<Map<String, Object>> getTwinDataByScope(TwinDataScopeQueryParam param) {
        // 判断参数
        if (CollectionUtil.isEmpty(param.getTargetTwinClassIds()) && CollectionUtil.isEmpty(param.getTargetTwinClassCodes())) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "目标孪生体分类ID列表或Code列表不能为空"));
        }

        // 目标孪生体
        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(param.getTargetTwinClassIds()), TwinClass::getId, param.getTargetTwinClassIds())
                .in(CollectionUtil.isNotEmpty(param.getTargetTwinClassCodes()), TwinClass::getCode, param.getTargetTwinClassCodes())
                .ne(TwinClass::getStatus, CommonStatusEnum.DELETED.getCode());

        final List<TwinClass> targetTwinClasses = twinClassService.list(wrapper);

        // 获取场景id对应关联的所有场景id
        List<Long> sceneIds = sceneApi.getAllVersionSceneIdsByMainSceneId(param.getSceneId());
        param.setSceneIds(sceneIds);

        if (Objects.nonNull(param.getOriginTwinClassId())) {
            // 原点对应的孪生对象
            final TwinClass originTwinClass = Optional.ofNullable(twinClassService.getById(param.getOriginTwinClassId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生体分类不存在")));

            // 填充原点对应的数据库表名
            setOriginTableName(param, originTwinClass);
        } else if (Objects.nonNull(param.getOriginTwinClassCode())) {
            // 原点对应的孪生对象
            final TwinClass originTwinClass = Optional.ofNullable(twinClassService.detailByCode(param.getOriginTwinClassCode())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "孪生体分类不存在")));

            // 填充原点对应的数据库表名
            setOriginTableName(param, originTwinClass);
        }else {
            if (CollectionUtils.isEmpty(param.getOriginPosition()) || param.getOriginPosition().size() != 3) {
                throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.FORBIDDEN, "原点坐标错误"));
            }
        }

        // 目标孪生体
        Set<Long> bubbleInfoIds = targetTwinClasses.stream().map(TwinClass::getBubbleInfoId).collect(Collectors.toSet());
        Map<Long, SysBubbleInfoVo> targetBubbleInfoMap = sysBubbleInfoApi.getVoMapByIds(bubbleInfoIds);

        // 模型相关
        final Set<String> thingModelUuids = targetTwinClasses.stream().map(TwinClass::getThingsModelUuid).filter(Objects::nonNull).collect(Collectors.toSet());
        final Map<String, ThingsModel> targetThingsModelMap = thingsModelsApi.getListByModelIds(thingModelUuids).stream().collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1));

        // 场景ID映射
        final Map<Long, Long> enableVersionMap = sceneApi.getEnableVersionMapBySceneIds(sceneIds);

        // 场景数据
        final SceneRecord sceneRecord = Optional.ofNullable(sceneApi.querySceneRecordByUuid(param.getSceneId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "场景数据不存在")));
        final Map<String, SceneData> sceneDataMap = sceneApi.querySceneDataAllChild(sceneRecord.getUuid()).stream().collect(Collectors.toMap(SceneData::getCampusBuilderId, Function.identity(), (k1, k2) -> k1));

        // 模型映射
        final Set<Long> twinClassIds = targetTwinClasses.stream().map(TwinClass::getId).collect(Collectors.toSet());
        final Map<Long, List<TwinClassModelMapping>> modelMappingGroupMap = twinClassModelMappingService.getModelMappingGroupMap(twinClassIds);

        // 附近的目标孪生体
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>(targetTwinClasses.size());
        for (TwinClass targetTwinClass : targetTwinClasses) {
            // 原点对应的数据库名称
            final String targetTableName = twinClassService.getTwinDataTableName(targetTwinClass);
            param.setTargetTableName(targetTableName);

            // 模型映射
            final List<TwinClassModelMapping> modelMappings = Optional.ofNullable(modelMappingGroupMap.get(targetTwinClass.getId())).orElse(new ArrayList<>());

            // 关联信息
            CompletableFuture<List<Map<String, Object>>> future = AsyncUtils.asyncExecutorBean(() -> {
                SysBubbleInfoVo bubbleInfoVo = Optional.ofNullable(targetBubbleInfoMap.get(targetTwinClass.getBubbleInfoId())).orElse(new SysBubbleInfoVo());
                ThingsModel thingsModel = Optional.ofNullable(targetThingsModelMap.get(targetTwinClass.getThingsModelUuid())).orElse(new ThingsModel());
                final List<Map<String, Object>> theList = twinBodyDataMapper.getTwinDataByScope(param);
                theList.forEach(e -> addTwinClassInfoByData(e, targetTwinClass, bubbleInfoVo, enableVersionMap, thingsModel, Collections.singletonList(sceneRecord), sceneDataMap, modelMappings));
                return theList;
            }, EXECUTOR_SERVICE).exceptionally(ex -> {
                log.error("查询点位异常", ex);
                return new ArrayList<>();
            });

            futures.add(future);
        }

        List<Map<String, Object>> allList = new ArrayList<>();
        futures.forEach(e -> allList.addAll(e.join()));

        return allList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDefaultTwin(List<DefaultTwinDataParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }

        // 保存孪生体相关数据
        final List<TwinDataPointParam> twinDataPointParams = params.stream().map(e -> BeanUtils.copyToBean(e, TwinDataPointParam.class)).collect(Collectors.toList());
        saveBatchTwinBodyPointInfo(twinDataPointParams);

        // 保存场景数据相关信息
        final List<DefaultTwinSceneDataParam> sceneDataParams = params.stream().map(DefaultTwinDataParam::getSceneData).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sceneDataParams)) {
            final Set<Long> sceneDataIds = sceneDataParams.stream().map(DefaultTwinSceneDataParam::getUuid).collect(Collectors.toSet());
            final Map<Long, SceneData> sceneDataMap = sceneApi.querySceneDataListByUuids(sceneDataIds).stream().collect(Collectors.toMap(SceneData::getUuid, Function.identity(), (k1, k2) -> k1));

            for (DefaultTwinSceneDataParam sceneDataParam : sceneDataParams) {
                final SceneData sceneData = sceneDataMap.get(sceneDataParam.getUuid());
                if (Objects.nonNull(sceneData)) {
                    BeanUtils.copy(sceneDataParam, sceneData, true);
                    sceneApi.updateSceneDataById(sceneData);
                }
            }
        }

    }

    @Override
    public List<Map<String, Object>> getTwinMapDataByScope(TwinMapDataScopeQueryParam param) {
        // 判断参数
        if (CollectionUtils.isEmpty(param.getTargetTwinClassIds()) && CollectionUtils.isEmpty(param.getTargetTwinClassCodes())) {
            throw ThrowUtils.getThrow().badRequest("目标孪生对象ID列表或Code列表不能为空");
        }

        // 目标孪生体
        final LambdaQueryWrapper<TwinClass> wrapper = Wrappers.<TwinClass>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(param.getTargetTwinClassIds()), TwinClass::getId, param.getTargetTwinClassIds())
                .in(CollectionUtils.isNotEmpty(param.getTargetTwinClassCodes()), TwinClass::getCode, param.getTargetTwinClassCodes())
                .ne(TwinClass::getStatus, StatusEnum.DELETED.getCode());

        final List<TwinClass> targetTwinClasses = twinClassService.list(wrapper);

        // 地图坐标类型
        final String columnName = Objects.equals(param.getPositionType(), "wgs84") ? "wgs84_position" : "gcj02_position";
        param.setTargetColumnName(columnName);
        // 模型相关
        final Set<String> thingModelUuids = targetTwinClasses.stream().map(TwinClass::getThingsModelUuid).filter(Objects::nonNull).collect(Collectors.toSet());
        final Map<String, ThingsModel> targetThingsModelMap = thingsModelsApi.getListByModelIds(thingModelUuids).stream().collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1));

        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>(targetTwinClasses.size());
        for (TwinClass targetTwinClass : targetTwinClasses) {
            // 原点对应的数据库名称
            final String targetTableName = twinClassService.getTwinDataTableName(targetTwinClass);
            param.setTargetTableName(targetTableName);

            // 关联信息
            CompletableFuture<List<Map<String, Object>>> future = AsyncUtils
                    .asyncExecutorBean(() -> {
                        ThingsModel thingsModel = Optional.ofNullable(targetThingsModelMap.get(targetTwinClass.getThingsModelUuid())).orElse(new ThingsModel());
                        final List<Map<String, Object>> theList = twinBodyDataMapper.getTwinMapDataByScope(param);
                        theList.forEach(e -> addTwinClassInfoByData(e, targetTwinClass, null, null, thingsModel, null, null, null));
                        return theList;
                    }, EXECUTOR_SERVICE)

                    .exceptionally(ex -> {
                        log.error("查询点位异常", ex);
                        return new ArrayList<>();
                    });

            futures.add(future);
        }

        List<Map<String, Object>> allList = new ArrayList<>();
        futures.forEach(e -> allList.addAll(e.join()));

        return allList;
    }

    @Override
    public TwinSceneInfoVo getTwinSceneInfoById(TwinSceneInfoParam param) {
        TwinSceneInfoVo twinSceneInfoVo = TwinSceneInfoVo.of(param);

        // 场景信息
        final Optional<SceneRecord> sceneRecordOptional = Optional.ofNullable(sceneApi.querySceneRecordByUuid(param.getSceneId()));
        if (sceneRecordOptional.isEmpty()) {
            return twinSceneInfoVo;
        }

        final SceneRecord sceneRecord = sceneRecordOptional.get();
        twinSceneInfoVo.setSceneCode(sceneRecord.getSceneCode());
        twinSceneInfoVo.setSceneName(sceneRecord.getName());

        // 场景物体信息
        final List<String> userIds = Stream.of(param.getUserRoomId(), param.getUserFloorId(), param.getUserBuildingId())
                .filter(Objects::nonNull)
                .toList();

        if (CollectionUtils.isEmpty(userIds)) {
            return twinSceneInfoVo;
        }

        // 查询场景物体信息
        final List<SceneData> sceneDataList = sceneApi.getSceneDataBySceneAndCbids(ScenePlacementsParam.build(param.getSceneId(), userIds));
        final Map<String, SceneData> sceneDataMap = sceneDataList.stream()
                .collect(Collectors.toMap(SceneData::getUserid, Function.identity(), (k1, k2) -> k1));

        // 房间名称
        Optional.ofNullable(twinSceneInfoVo.getUserRoomId())
                .map(sceneDataMap::get)
                .ifPresent(e -> twinSceneInfoVo.setRoomName(Optional.ofNullable(e.getSettingName()).orElse(e.getName())));

        // 楼层名称
        Optional.ofNullable(twinSceneInfoVo.getUserFloorId())
                .map(sceneDataMap::get)
                .ifPresent(e -> twinSceneInfoVo.setFloorName(Optional.ofNullable(e.getSettingName()).orElse(e.getName())));

        // 建筑名称
        Optional.ofNullable(twinSceneInfoVo.getUserBuildingId())
                .map(sceneDataMap::get)
                .ifPresent(e -> twinSceneInfoVo.setBuildingName(Optional.ofNullable(e.getSettingName()).orElse(e.getName())));

        return twinSceneInfoVo;
    }

    @Override
    public TwinSceneInfoVo getTwinSceneInfoByCode(TwinSceneInfoDefaultParam param) {
        TwinSceneInfoVo twinSceneInfoVo = new TwinSceneInfoVo();
        twinSceneInfoVo.setSceneCode(param.getSceneCode());

        // 场景信息
        final Optional<SceneRecord> sceneRecordOptional = Optional.ofNullable(sceneApi.getSceneBySceneCode(param.getSceneCode()));


        if (sceneRecordOptional.isEmpty()) {
            return twinSceneInfoVo;
        }

        final SceneRecord sceneRecord = sceneRecordOptional.get();
        twinSceneInfoVo.setSceneId(sceneRecord.getUuid());
        twinSceneInfoVo.setSceneName(sceneRecord.getName());

        // 点位信息
        final Optional<SceneData> sceneDataOptional = Optional.ofNullable(sceneApi.getSceneDataBySceneAndUserId(sceneRecord.getUuid(), param.getUserId()));
        if (sceneDataOptional.isEmpty()) {
            return twinSceneInfoVo;
        }

        final SceneData sceneData = sceneDataOptional.get();
        switch (sceneData.getDataType()) {
            case SceneUtil.ROOM_NODE:
                // 房间
                twinSceneInfoVo.setUserRoomId(sceneData.getCampusBuilderId());
                twinSceneInfoVo.setRoomName(Optional.ofNullable(sceneData.getSettingName()).orElse(sceneData.getName()));

                // 楼层
                SceneData floorSceneData = sceneApi.getSceneDataBySceneAndCbid(sceneRecord.getUuid(), sceneData.getParentCBID());
                if (Objects.nonNull(floorSceneData)) {
                    twinSceneInfoVo.setUserFloorId(floorSceneData.getUserid());
                    twinSceneInfoVo.setFloorName(Optional.ofNullable(floorSceneData.getSettingName()).orElse(floorSceneData.getName()));

                    // 建筑或园区
                    SceneData buildingSceneData = sceneApi.getSceneDataBySceneAndCbid(sceneRecord.getUuid(), floorSceneData.getParentCBID());
                    if (Objects.nonNull(buildingSceneData)) {
                        twinSceneInfoVo.setUserBuildingId(buildingSceneData.getUserid());
                        twinSceneInfoVo.setBuildingName(Optional.ofNullable(buildingSceneData.getSettingName()).orElse(buildingSceneData.getName()));
                    }
                }
                break;
            case SceneUtil.PLAN_NODE:
                // 楼层
                twinSceneInfoVo.setUserFloorId(sceneData.getUserid());
                twinSceneInfoVo.setFloorName(Optional.ofNullable(sceneData.getSettingName()).orElse(sceneData.getName()));

                // 建筑或园区
                SceneData buildingSceneData = sceneApi.getSceneDataBySceneAndCbid(sceneRecord.getUuid(), sceneData.getParentCBID());
                if (Objects.nonNull(buildingSceneData)) {
                    twinSceneInfoVo.setUserBuildingId(buildingSceneData.getUserid());
                    twinSceneInfoVo.setBuildingName(Optional.ofNullable(buildingSceneData.getSettingName()).orElse(buildingSceneData.getName()));
                }
                break;
            case SceneUtil.BUILDING_NODE:
                // 建筑或园区
                twinSceneInfoVo.setUserBuildingId(sceneData.getUserid());
                twinSceneInfoVo.setBuildingName(Optional.ofNullable(sceneData.getSettingName()).orElse(sceneData.getName()));
            default:
                break;
        }

        return twinSceneInfoVo;
    }

    /**
     * 区域坐标参数校验格式化
     *
     * @param region 区域坐标
     * @return 格式化的区域坐标
     * <AUTHOR>
     * @date 2022/6/6 14:02
     */
    private String regionParamFormat(List<List<BigDecimal>> region) {
        // 过滤出正确格式的坐标值
        final List<List<BigDecimal>> filters = region.stream()
                .filter(Objects::nonNull)
                .filter(e -> e.size() == 2)
                .collect(Collectors.toList());

        // 判断坐标值的长度是否够构成平面
        if (filters.size() < 3) {
            throw ThrowUtils.getThrow().badRequest("区域的范围坐标格式错误");
        }

        // 坐标值首位封边
        filters.add(filters.get(0));

        // 区域坐标格式化
        final String regionFormat = filters.stream()
                .map(e -> e.get(0) + " " + e.get(1))
                .collect(Collectors.joining(StringConstant.COMMA));

        return StringConstant.LEFT_BRACKET + regionFormat + StringConstant.RIGHT_BRACKET;
    }

    /**
     * 构建校准参数
     *
     * @param twinClass  孪生对象
     * @param tableName  表名
     * @param sceneIds   场景ID列表
     * @param param      参数
     * @param offsetPosX 偏移x
     * @param offsetPosY 偏移y
     * @return 校准点位个数
     * <AUTHOR>
     * @date 2021/12/14 17:22
     */
    private int buildCbPosParam(TwinClass twinClass, String tableName, List<Long> sceneIds, TwinInfoCorrectParam param, BigDecimal offsetPosX, BigDecimal offsetPosY) {
        TwinInfoParam twinInfoParam = new TwinInfoParam(tableName, null, null, null, null, param.getParentUserId(), sceneIds);
        final List<TwinInfoVo> twinInfoVos = twinBodyDataMapper.getTwinInfo(twinInfoParam);

        // 没有点位数据，跳出
        if (CollectionUtils.isEmpty(twinInfoVos)) {
            return 0;
        }

        // 判断点位类型，线面加上cbVertices属性
        if (!Objects.equals(twinClass.getDataType(), TwinClassDataTypeEnum.POINT.getCode())) {
            TwinVerticesParam twinVerticesParam = new TwinVerticesParam();
            twinVerticesParam.setTableName(tableName);

            final List<TwinCbPointVo> cbVertices = twinBodyDataMapper.getTwinSurfaceVertices(twinVerticesParam);
            final Map<Long, List<TwinCbPointVo>> cbVerticesMap = cbVertices.stream().collect(Collectors.groupingBy(TwinCbPointVo::getUuid));
            twinInfoVos.forEach(e -> e.setVertices(Optional.ofNullable(cbVerticesMap.get(e.getUuid())).orElse(new ArrayList<>())));
        }

        // 点位信息更改
        List<TwinPosParam> posParams = new ArrayList<>();
        for (TwinInfoVo twinInfoVo : twinInfoVos) {
            // position x,y坐标为空，跳出
            if (Objects.isNull(twinInfoVo.getPosX()) || Objects.isNull(twinInfoVo.getPosY())) {
                continue;
            }

            // 更新position x,y坐标
            twinInfoVo.setPosX(twinInfoVo.getPosX().add(offsetPosX));
            twinInfoVo.setPosY(twinInfoVo.getPosY().add(offsetPosY));
            if (!CollectionUtils.isEmpty(twinInfoVo.getVertices())) {
                for (TwinCbPointVo cbVertex : twinInfoVo.getVertices()) {
                    // CbVertices x,y坐标为空，跳出
                    if (Objects.isNull(cbVertex.getX()) || Objects.isNull(cbVertex.getY())) {
                        continue;
                    }
                    cbVertex.setX(cbVertex.getX().add(offsetPosX));
                    cbVertex.setY(cbVertex.getY().add(offsetPosY));
                }
            }

            TwinPosParam cbPosParam = BeanUtils.copyToBean(twinInfoVo, TwinPosParam.class, true);
            cbPosParam.setTableName(tableName);
            posParams.add(cbPosParam);
        }

        if (CollectionUtils.isEmpty(posParams)) {
            return 0;
        }

        // 校准点位
        twinBodyDataMapper.updateTwinPos(posParams);

        return posParams.size();
    }

    /**
     * 获取孪生体类型
     */
    private TwinClass getTwinClass(String id) {
        TwinClass twinClass = twinClassService.getById(id);
        if (Objects.isNull(twinClass)) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "未查询到对应的孪生体类型"));
        }
        return twinClass;
    }


    /**
     * 解析表信息json数据获取表名
     */
    private String getTableName(String id) {
        String database = DataSourceConstant.TWIN_X + ".";
        TwinClass twinClass = getTwinClass(id);
        String twinDataJson = twinClass.getStructure();
        if (StringUtils.isBlank(twinDataJson)) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象未配置表单"));
        }
        if (!TenantUtils.isMaster()) {
            database = TenantUtils.getTenantByRequest().toLowerCase() + "_" + database;
        }

        return database + JrmJsonUtils.getTableName(twinDataJson);
    }

    /**
     * 根据id获取孪生体类型
     */
    private TwinClassDataTypeEnum getClassType(String id) {
        return TwinClassDataTypeEnum.build(getTwinClass(id).getDataType());
    }

    /**
     * 根据id获取所在层级
     */
    private TwinClassLevelEnum getClassLevel(String id) {
        return TwinClassLevelEnum.build(getTwinClass(id).getLevel());
    }


    /**
     * 解析json数据为Column模型
     */
    private List<Column> parseJsonToTwinData(String id) {
        TwinClass twinClass = getTwinClass(id);
        List<Column> columnList = Lists.newArrayList();
        final JSONArray columns = JrmJsonUtils.getColumns(twinClass.getStructure());
        if (CollectionUtils.isNotEmpty(columns)) {
            columns.forEach((item) -> {
                JSONObject itemJson = (JSONObject) item;
                Column column = JSON.parseObject(JSON.toJSONString(itemJson), Column.class);
                columnList.add(column);
            });
        }
        return columnList;
    }


    /**
     * 获取表单属性
     *
     * @param id 孪生对象id
     * @return 表单模型分组
     */
    private Map<String, Map<String, String>> getForms(String id) {

        TwinClass twinClass = getTwinClass(id);
        List<FormItem> formItems = FormUtils.parseListToForm(twinClass.getForm());
        return formItems.stream().collect(Collectors.toMap(FormItem::getModel, v -> v.getOptions().getOptions().stream().collect(Collectors.toMap(DataOption::getLabel, DataOption::getValue)), (k1, k2) -> k2));
    }

    /**
     * 根据孪生体类型地图层级初始化表头
     */
    private Map<String, String> initTableHead(String id) {
        Map<String, String> levelField = Maps.newHashMap();
        TwinClass twinClass = getTwinClass(id);
        String twinLevel = twinClass.getLevel();
        //form表单表头字段
        Map<String, String> formHead = getFormHead(twinClass.getForm());
        formHead.put(TwinFixedColumnConstant.ALIAS_UUID, TwinFixedColumnConstant.ALIAS_UUID_NAME);
        //如果是默认分组内的孪生体
        if (TwinDictCodeConstant.TWIN_CLASS_DEFAULT.equals(twinClass.getGroupId())) {
            return formHead;
        }
        if (TwinDictCodeConstant.MAP.equals(twinLevel)) {
            levelField = sysDictDataApi.getDataMapByTypeCode(TwinDictCodeConstant.TWIN_CLASS_MAP_COLUMN);
        }
        if (TwinDictCodeConstant.PARK.equals(twinLevel)) {
            levelField = sysDictDataApi.getDataMapByTypeCode(TwinDictCodeConstant.TWIN_CLASS_PARK_COLUMN);
        }
        formHead.putAll(levelField);
        return formHead;
    }


    /**
     * 获取form表单head
     */
    private Map<String, String> getFormHead(String json) {
        List<FormItem> formItems = FormUtils.parseListToForm(json);
        //排除 上传文件按钮 关联孪生体
        return formItems.stream()
                .filter(formItem -> !TwinFixedColumnConstant.IGNORE_TWIN_FIELD.contains(formItem.getType()))
                .collect(Collectors.toMap(FormItem::getModel, FormItem::getLabel));
    }

    /**
     * 获取点位表名
     *
     * @param twinClassId  孪生对象ID
     * @param twinClassMap 孪生对象Map
     * @return 对应点位的表名
     * <AUTHOR>
     * @date 2021/9/22 16:03
     */
    private String getPointTableName(Long twinClassId, Map<Long, TwinClass> twinClassMap) {
        if (Objects.isNull(twinClassId)) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象id不能为空"));
        }

        // 孪生体对象
        TwinClass twinClass = Optional.ofNullable(twinClassMap.get(twinClassId)).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生对象不存在")));

        // 获取孪生体数据表名
        return twinClassService.getTwinDataTableName(twinClass);

    }


    /**
     * 构建孪生体点位数据保存参数
     *
     * @param data        源数据
     * @param twinClass   孪生对象
     * @param tableName   孪生体数据对应表名
     * @param sceneId     场景ID
     * @param parentId    parentId
     * @param twinDataMap 孪生体属性map
     * @param dataSource  数据源
     * @return 孪生体点位数据保存参数
     * <AUTHOR>
     * @date 2021/9/23 17:00
     */
    private TwinDataPointParam buildTwinDataPointParam(Map<String, Object> data, TwinClass twinClass, String tableName, Long sceneId, String parentId, Map<String, Object> twinDataMap, TwinDataSourceEnum dataSource, Long userId) {

        TwinDataPointParam twinDataPointParam = new TwinDataPointParam();
        twinDataPointParam.setUuid(IdUtils.getId());
        twinDataPointParam.setTwinClassId(twinClass.getId());
        twinDataPointParam.setDataType(twinClass.getDataType());
        twinDataPointParam.setSceneId(String.valueOf(sceneId));
        twinDataPointParam.setTableName(tableName);
        twinDataPointParam.setParentUserId(parentId);
        twinDataPointParam.setCreateUser(userId);

        // positionBody
        final String positionBody = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_POSITION_BODY)).map(String::valueOf).orElse(null);
        twinDataPointParam.setPositionBody(positionBody);

        // userRoomId
        final String userRoomId = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_USER_ROOM_ID)).map(String::valueOf).orElse(null);
        twinDataPointParam.setUserRoomId(userRoomId);

        // userBuildingId
        final String userBuildingId = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_USER_BUILDING_ID)).map(String::valueOf).orElse(null);
        twinDataPointParam.setUserBuildingId(userBuildingId);

        // userFloorId
        final String userFloorId = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_USER_FLOOR_ID)).map(String::valueOf).orElse(null);
        twinDataPointParam.setUserFloorId(userFloorId);

        // currentLevel
        final String currentLevel = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_CURRENT_LEVEL)).map(String::valueOf).orElse(null);
        twinDataPointParam.setCurrentLevel(currentLevel);

        // position
        final String position = Optional.ofNullable(data.get(TwinFixedColumnConstant.ALIAS_POSITION)).map(String::valueOf).orElse(null);
        twinDataPointParam.setPosition(position);

        // gisHeight
        final String gisHeight = Optional.ofNullable(data.get(PositionTransformService.HEIGHT)).map(String::valueOf).orElse(null);
        twinDataPointParam.setGisHeight(gisHeight);

        // formData
        twinDataPointParam.setFormData(twinDataMap);
        twinDataPointParam.setDataSource(dataSource.getCode());

        // 添加孪生对象编码
        twinDataPointParam.setTwinClassCode(twinClass.getCode());

        return twinDataPointParam;
    }

    private Map<String, Object> buildTwinDataMap(List<FormItem> formItems, Map<String, Object> data) {
        final List<String> formDataKeys = formItems.stream().map(FormItem::getModel).toList();
        final Map<String, Object> twinDataMap = new HashMap<>();
        for (String formDataKey : formDataKeys) {
            twinDataMap.put(formDataKey, data.get(formDataKey));
        }
        return twinDataMap;
    }

    @Override
    public List<Map<String, Object>> getTwinDataRegionWithin(TwinDataRegionQueryParam param) {
        // 参数信息
        final List<List<BigDecimal>> region = param.getRegion();
        param.setRegionFormat(regionParamFormat(region));

        // 场景信息
        List<Long> sceneIds = sceneApi.getAllVersionSceneIdsByMainSceneId(param.getSceneId());
        param.setSceneIds(sceneIds);

        // 目标孪生对象
        TwinClassListParam twinClassListParam = new TwinClassListParam();
        twinClassListParam.setDataTypes(Collections.singletonList(TwinClassDataTypeEnum.POINT.getCode()));
        twinClassListParam.setTwinClassCodes(param.getTwinClassCodes());
        twinClassListParam.setHasDefault(false);
        final List<TwinClass> twinClasses = twinClassService.getListByParam(twinClassListParam);

        Set<Long> bubbleInfoIds = twinClasses.stream().map(TwinClass::getBubbleInfoId).collect(Collectors.toSet());
        Map<Long, SysBubbleInfoVo> bubbleInfoVoMap = sysBubbleInfoApi.getVoMapByIds(bubbleInfoIds);

        // 模型相关
        final Set<String> thingModelUuids = twinClasses.stream().map(TwinClass::getThingsModelUuid).filter(Objects::nonNull).collect(Collectors.toSet());
        final Map<String, ThingsModel> thingsModelMap = thingsModelsApi.getListByModelIds(thingModelUuids).stream().collect(Collectors.toMap(ThingsModel::getModelId, Function.identity(), (k1, k2) -> k1));

        // 场景ID映射
        final Map<Long, Long> enableVersionMap = sceneApi.getEnableVersionMapBySceneIds(sceneIds);

        // 场景数据
        final SceneRecord sceneRecord = Optional.ofNullable(sceneApi.querySceneRecordByUuid(param.getSceneId())).orElseThrow(() -> new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "场景数据不存在")));
        final Map<String, SceneData> sceneDataMap = sceneApi.querySceneDataAllChild(sceneRecord.getUuid()).stream().collect(Collectors.toMap(SceneData::getCampusBuilderId, Function.identity(), (k1, k2) -> k1));

        // 模型映射
        final Set<Long> twinClassIds = twinClasses.stream().map(TwinClass::getId).collect(Collectors.toSet());
        final Map<Long, List<TwinClassModelMapping>> modelMappingGroupMap = twinClassModelMappingService.getModelMappingGroupMap(twinClassIds);

        // 区域范围内的孪生体
        List<CompletableFuture<List<Map<String, Object>>>> futures = new ArrayList<>(twinClasses.size());
        for (TwinClass twinClass : twinClasses) {
            // 原点对应的数据库名称
            final String targetTableName = twinClassService.getTwinDataTableName(twinClass);
            param.setTableName(targetTableName);

            // 模型映射
            final List<TwinClassModelMapping> modelMappings = Optional.ofNullable(modelMappingGroupMap.get(twinClass.getId())).orElse(new ArrayList<>());

            // 关联信息
            CompletableFuture<List<Map<String, Object>>> future = AsyncUtils.asyncExecutorBean(() -> {
                SysBubbleInfoVo bubbleInfoVo = Optional.ofNullable(bubbleInfoVoMap.get(twinClass.getBubbleInfoId())).orElse(new SysBubbleInfoVo());
                ThingsModel thingsModel = Optional.ofNullable(thingsModelMap.get(twinClass.getThingsModelUuid())).orElse(new ThingsModel());
                final List<Map<String, Object>> theList = twinBodyDataMapper.getTwinDataRegionWithin(param);
                theList.forEach(e -> addTwinClassInfoByData(e, twinClass, bubbleInfoVo, enableVersionMap, thingsModel, Collections.singletonList(sceneRecord), sceneDataMap, modelMappings));
                return theList;
            }, EXECUTOR_SERVICE).exceptionally(ex -> {
                log.error("查询点位异常", ex);
                return new ArrayList<>();
            });

            futures.add(future);
        }

        List<Map<String, Object>> allList = new ArrayList<>();
        futures.forEach(e -> allList.addAll(e.join()));

        return allList;
    }

    @Override
    public List<Object> countBatchRowUnique(Map<String, Object> map) {
        return twinBodyDataMapper.countBatchRowUnique(map);
    }


    @Override
    public List<Map<String, Object>> selectTwinDataListByUniqueCodes(String twinClassCode, Set<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }

        final TwinClass twinClass = twinClassService.findByCode(twinClassCode);
        if (Objects.isNull(twinClass)) {
            return Lists.newArrayList();
        }
        final String tableName = twinClassService.getTwinDataTableName(twinClass);

        return twinBodyDataMapper.selectTwinDataListByUniqueCodes(tableName, uniqueCodes);
    }

    @Override
    public List<DataStatistics> countTwinNumTop() {
        return twinBodyDataMapper.countTop();
    }


    @Override
    public List<DataStatistics> countTwinNumByDate(DataStatisticsParam param) {
        final List<String> noDefaultTableNames = twinBodyDataMapper.getNoDefaultTableNames();
        if (CollectionUtils.isEmpty(noDefaultTableNames)) {
            return Lists.newArrayList();
        }
        return twinBodyDataMapper.countTwinByDate(noDefaultTableNames, param.getStartDate(), param.getEndDate());
    }

    @Override
    public Map<String, Object> addTwinClassInfoByData(AddTwinClassInfoByDataUtils param) {
        return addTwinClassInfoByData(param.getData(), param.getTwinClass(), param.getBubbleInfoVo(), param.getEnableVersionMap(), param.getThingsModel(), param.getSceneRecords(), param.getSceneDataMap(), param.getModelMapping());
    }

    /**
     * 为孪生体数据添加对应孪生体分类信息
     *
     * @param data      孪生体数据
     * @param twinClass 孪生体分类
     * <AUTHOR>
     * @date 2021/9/1 15:44
     */
    private void addTwinClassInfoByData(Map<String, Object> data,
                                        TwinClass twinClass,
                                        SysBubbleInfoVo bubbleInfoVo,
                                        Map<Long, Long> enableVersionMap,
                                        ThingsModel thingsModel,
                                        List<SceneRecord> sceneRecords,
                                        Map<String, SceneData> sceneDataMap) {
        addTwinClassInfoByData(data, twinClass, bubbleInfoVo, enableVersionMap, thingsModel, sceneRecords, sceneDataMap, new ArrayList<>());
    }

    private Map<String, Object> addTwinClassInfoByData(Map<String, Object> data,
                                                       TwinClass twinClass,
                                                       SysBubbleInfoVo bubbleInfoVo,
                                                       Map<Long, Long> enableVersionMap,
                                                       ThingsModel thingsModel,
                                                       List<SceneRecord> sceneRecords,
                                                       Map<String, SceneData> sceneDataMap,
                                                       List<TwinClassModelMapping> modelMappings) {
        data.put(TwinFixedColumnConstant.ALIAS_TWIN_CLASS_ID, twinClass.getId());
        data.put(TwinFixedColumnConstant.ALIAS_TWIN_CLASS_NAME, twinClass.getName());
        data.put(TwinFixedColumnConstant.ALIAS_TWIN_CLASS_CODE, twinClass.getCode());
        data.put(TwinFixedColumnConstant.ALIAS_DATA_TYPE, twinClass.getDataType());
        data.put(TwinFixedColumnConstant.ALIAS_THINGS_MODEL_UUID, twinClass.getThingsModelUuid());
        data.put(TwinFixedColumnConstant.ALIAS_BUBBLE_INFO_VO, bubbleInfoVo);
        data.put(TwinFixedColumnConstant.ALIAS_THINGS_MODEL_TYPE, thingsModel.getType());
        String formJson = twinClass.getForm();
//        data.put(TwinFixedColumnConstant.ALIAS_FORM, buildFormValues(formJson, data));
        data.put(TwinFixedColumnConstant.ALIAS_FORM, formJson);
        data.put(TwinFixedColumnConstant.ALIAS_FORM_DATA, buildFormData(formJson, data));
        data.put(TwinFixedColumnConstant.ALIAS_DEFAULT_TWIN, false);

        // 默认孪生体
        if (TwinFixedColumnConstant.isDefaultTwin(twinClass.getGroupId()) && null != sceneRecords) {
            // 拿到userId
            final String userId = Optional.ofNullable(data.get(TwinFixedColumnConstant.COLUMN_USER_ID)).map(String::valueOf).orElse("");
            // 拿到场景编码
            final String sceneCode = Optional.ofNullable(data.get(TwinFixedColumnConstant.COLUMN_SCENE_CODE)).map(String::valueOf).orElse("");
            // 拿到场景id
            final Long sceneId = sceneRecords.stream().filter(e -> Objects.equals(e.getSceneCode(), sceneCode)).map(SceneRecord::getUuid).findFirst().orElse(0L);
            // 拿到当前层级的数据
            final SceneData sceneData = sceneDataMap.values().stream().filter(e -> StringUtils.isNotBlank(e.getUserid()) && Objects.equals(e.getParentSceneUUID(), sceneId) && StringUtils.equals(e.getUserid(), userId)).findFirst().orElse(new SceneData());//Optional.ofNullable(sceneDataMap.get(sceneId + StringConstant.UNDERSCORE + userId)).orElse(new SceneData());

            final String parentUserId = sceneDataMap.values().stream().filter(e -> Objects.equals(e.getCampusBuilderId(), sceneData.getParentCBID())).findAny().map(SceneData::getUserid).orElse(SceneUtil.OUTDOOR_NODE);
            final JSONObject cbData = new JSONObject();
            cbData.put("id", data.get(TwinFixedColumnConstant.COLUMN_UUID));
            if (StringUtils.isNotBlank(sceneData.getPosition())) {
                cbData.put("position", sceneData.getPosition());
            }
            if (StringUtils.isNotBlank(sceneData.getCorners())) {
                final JSONArray vertices = JSONArray.parseArray(sceneData.getCorners());
                cbData.put("vertices", vertices);
            }
            if (StringUtils.isNotBlank(sceneData.getConfigCamInfo())) {
                JSONObject configCamInfo = JSONObject.parseObject(sceneData.getConfigCamInfo());
                cbData.put("eye", configCamInfo.get("eye"));
                cbData.put("target", configCamInfo.get("target"));
                cbData.put("distance", configCamInfo.get("distance"));
            }

            data.put(TwinFixedColumnConstant.ALIAS_DEFAULT_TWIN, true);
            data.put(TwinFixedColumnConstant.COLUMN_SCENE_ID, sceneId);
            data.put(TwinFixedColumnConstant.COLUMN_POSITION_BODY, JSONObject.toJSONString(cbData));
            data.put(TwinFixedColumnConstant.COLUMN_DATA_SOURCE, TwinDataSourceEnum.TWIN.getCode());
            data.put(TwinFixedColumnConstant.COLUMN_PARENT_USER_ID, parentUserId);
            data.put(TwinFixedColumnConstant.ALIAS_SCENE_DATA, sceneData);
        }

        // 替换场景ID
        if (CollectionUtils.isNotEmpty(enableVersionMap)) {
            final Object sceneId = data.get(TwinFixedColumnConstant.COLUMN_SCENE_ID);
            if (Objects.nonNull(sceneId)) {
                data.put(TwinFixedColumnConstant.COLUMN_SCENE_ID, enableVersionMap.get(Long.valueOf(String.valueOf(sceneId))));
            }
        }

        // 替换模型UUID
        twinClassModelMappingService.setModelUuidByMapping(data, modelMappings);

        return data;
    }

    /**
     * 构建包含数据值的表单json
     *
     * @param formJson 表单json
     * @param data     数据
     * @return 包含数据值的表单json
     */
    public String buildFormValues(String formJson, Map<String, Object> data) {
        String formValues = "";

        if (StringUtils.isNotBlank(formJson)) {
            Form form = FormUtils.parseForm(formJson);
            List<FormItem> formItems = form.getList();
            for (FormItem formItem : formItems) {
                Object object = data.get(formItem.getModel());
                if (Objects.nonNull(object)) {
                    formItem.getOptions().setDefaultValue(object);
                }
            }

            try {
                formValues = objectMapper.writeValueAsString(form);
            } catch (JsonProcessingException e) {
                log.error("表单对象转换JSON失败", e);
            }
        }

        return formValues;
    }

    /**
     * 构建表单数据值
     *
     * @param formJson 表单json
     * @param data     数据
     * @return 表单数据值
     */
    public Map<String, Object> buildFormData(String formJson, Map<String, Object> data) {
        Map<String, Object> formData = new HashMap<>();

        if (StringUtils.isNotBlank(formJson)) {
            List<FormItem> formItems = FormUtils.parseForm(formJson).getList();
            for (FormItem formItem : formItems) {
                formData.put(formItem.getModel(), data.get(formItem.getModel()));
            }
        }

        return formData;
    }

    /**
     * 根据场景ID查询所有关联的场景ID，包括所有版本的主场景ID和所有版本的子场景ID
     *
     * @param sceneId 场景ID
     * @return 所有关联的场景ID
     * <AUTHOR>
     * @date 2022/8/12 13:49
     */
    private Set<Long> getAllSceneIds(Long sceneId) {
        Set<Long> sceneIds = new HashSet<>();
        sceneIds.add(sceneId);

        // 获取当前场景
        Optional<SceneRecord> sceneRecordOptional = Optional.ofNullable(sceneRecordMapper.queryOneByUuid(sceneId));
        if (sceneRecordOptional.isEmpty()) {
            return sceneIds;
        }

        // 获取当前场景所有版本
        final SceneRecord sceneRecord = sceneRecordOptional.get();
        final LambdaQueryWrapper<SceneRecord> wrapper = Wrappers.<SceneRecord>lambdaQuery()
                .eq(SceneRecord::getSceneCode, sceneRecord.getSceneCode());
        final List<SceneRecord> allVersionScenes = sceneRecordMapper.selectList(wrapper);
        final Set<Long> allVersionSceneUuids = allVersionScenes.stream().map(SceneRecord::getUuid).collect(Collectors.toSet());
        sceneIds.addAll(allVersionSceneUuids);

        // 获取所有版本的子场景
        final LambdaQueryWrapper<SceneRecord> childWrapper = Wrappers.<SceneRecord>lambdaQuery()
                .in(SceneRecord::getParentUuid, allVersionSceneUuids);
        final List<SceneRecord> allChildScenes = sceneRecordMapper.selectList(childWrapper);
        final Set<Long> allChildSceneUuids = allChildScenes.stream().map(SceneRecord::getUuid).collect(Collectors.toSet());
        sceneIds.addAll(allChildSceneUuids);

        return sceneIds;
    }

    /**
     * 根据场景ID删除所有的孪生体数据
     *
     * @param sceneId 场景ID
     * <AUTHOR>
     * @date 2022/8/12 13:49
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTwinDataBySceneId(Long sceneId) {
        // 场景ID
        final Set<Long> sceneIds = getAllSceneIds(sceneId);

        deleteTwinDataBySceneIds(sceneIds);
    }

    /**
     * 根据场景ID删除所有的孪生体数据
     *
     * @param sceneIds 场景ID列表
     * <AUTHOR>
     * @date 2022/8/12 13:49
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTwinDataBySceneIds(Set<Long> sceneIds) {
        // 孪生体分类
        TwinClassListParam twinClassListParam = new TwinClassListParam();
        twinClassListParam.setLevel(TwinClassLevelEnum.PARK.getCode());
        twinClassListParam.setHasDefault(false);
        final List<TwinClass> twinClasses = twinClassService.getListByParam(twinClassListParam);

        // 如果场景ID或孪生体分类为空，直接返回
        if (CollectionUtils.isEmpty(sceneIds) || CollectionUtils.isEmpty(twinClasses)) {
            return;
        }


        // 转化为参数
        final List<TwinDataSceneIdDeleteParam> params = twinClasses.stream()
                .map(twinClassService::getTwinDataTableName)
                .map(e -> new TwinDataSceneIdDeleteParam(e, sceneIds))
                .collect(Collectors.toList());

        twinBodyDataMapper.deleteAllBySceneIds(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> updateTwinData(TwinDataUpdateParam param) {
        // 根据孪生体分类code查询孪生体分类信息
        TwinClass twinClass = twinClassService.findByCode(param.getTwinClassCode());
        if (Objects.isNull(twinClass)) {
            throw new TwinBodyDataException(ExpEnumOption.of(HttpStatus.BAD_REQUEST, "孪生体分类不存在"));
        }

        // 获取表名
        String tableName = twinClassService.getTwinDataTableName(twinClass);

        // 先查询符合条件的记录获取uuid列表
        List<Long> uuids = twinBodyDataMapper.getUuidsByConditions(tableName, param.getConditions());

        if (uuids.isEmpty()) {
            return new ArrayList<>();
        }

        // 处理更新数据，特殊处理 position_body 字段
        Map<String, Object> processedUpdateData = processUpdateData(param.getUpdateData());

        // 执行更新操作
        twinBodyDataMapper.updateTwinData(tableName, processedUpdateData, param.getConditions());

        // 返回所有修改过数据的uuid字段数组
        return uuids;
    }

    /**
     * 处理更新数据，特殊处理 position_body 字段
     * @param updateData 原始更新数据
     * @return 处理后的更新数据
     */
    private Map<String, Object> processUpdateData(Map<String, Object> updateData) {
        if (updateData == null || updateData.isEmpty()) {
            return updateData;
        }

        Map<String, Object> processedData = new HashMap<>(updateData);

        // 只处理需要进 position_body 的字段
        Map<String, Object> positionBodyUpdates = new HashMap<>();
        for (Map.Entry<String, Object> entry : updateData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if ("engine".equals(key) || "scale".equals(key) || "angles".equals(key)) {
                positionBodyUpdates.put(key, value);
                processedData.remove(key); // 避免主表和 position_body 同时更新
            }
            // gis_height 不做 position_body 处理，直接留在 processedData
        }

        if (!positionBodyUpdates.isEmpty()) {
            String positionBodyUpdateSql = buildPositionBodyUpdateSql(positionBodyUpdates);
            if (positionBodyUpdateSql != null) {
                processedData.put("position_body", positionBodyUpdateSql);
            }
        }

        return processedData;
    }

    /**
     * 构建 position_body 字段的更新 SQL
     * @param updates 需要更新的字段
     * @return 更新 SQL 字符串
     */
    private String buildPositionBodyUpdateSql(Map<String, Object> updates) {
        if (updates.isEmpty()) {
            return null;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("JSON_SET(position_body");
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof String) {
                sql.append(", '$.").append(key).append("', '").append(value).append("'");
            } else if (value instanceof List || value.getClass().isArray()) {
                // 数组类型用 CAST('json' AS JSON) 拼接
                String jsonValue = JSON.toJSONString(value);
                sql.append(", '$.").append(key).append("', CAST('").append(jsonValue).append("' AS JSON)");
            } else {
                sql.append(", '$.").append(key).append("', ").append(value);
            }
        }
        sql.append(")");
        return sql.toString();
    }
}
