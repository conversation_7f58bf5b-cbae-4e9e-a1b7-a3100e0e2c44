package com.uino.x.pedestal.twin.impl.ddl;


import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.common.datasource.util.SqlUtils;
import com.uino.x.common.label.constant.StringConstant;
import com.uino.x.common.sql.maker.SchemaSqlFactory;
import com.uino.x.common.sql.maker.TableSql;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.common.tool.base.ThrowUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.twin.common.constant.Sql;
import com.uino.x.pedestal.twin.common.constant.TwinFixedColumnConstant;
import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.common.utils.ColumnUtils;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.dao.mapper.TwinBodyDataMapper;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.core.domain.JrmTransactionOption;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.pojo.domain.AlterColumn;
import com.uino.x.pedestal.twin.pojo.domain.Column;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Alter ddl
 *
 * <AUTHOR>
 * @version 1.0.3
 * @date 2022/1/5 17:30
 */
@Getter
@Slf4j
public class AlterJrmDdl extends AbstractJrmDdl implements JrmDdl {

    /**
     * 旧列列表
     */
    private List<? extends Column> oldColumnList = new ArrayList<>();

    /**
     * 新列列表
     */
    private List<Column> newColumnList = new ArrayList<>();

    /**
     * 开启后置调用处理
     */
    private boolean enableInvokePostProcess = false;

    /**
     * 所有alter列
     */
    private List<AlterColumn> alterColumnList = new ArrayList<>();

    /**
     * 删除的列
     */
    private List<AlterColumn> deletedColumnList = new ArrayList<>();

    /**
     * 新增的列
     */
    private List<AlterColumn> addColumnList = new ArrayList<>();

    /**
     * 改变的列
     */
    private List<AlterColumn> changeColumnList = new ArrayList<>();


    /**
     * 构造方法
     *
     * @param tableName 表名
     * @param formItem  表单列表
     * <AUTHOR>
     * @date 2022/1/5 17:27
     */
    public AlterJrmDdl(AutoTableTypeEnum autoTableType, String tableName, List<FormItem> formItem, String sourceStructure) {

        super(autoTableType, tableName, formItem, sourceStructure);
    }

    /**
     * 所有alter列
     *
     * @return {@link AlterJrmDdl}
     * <AUTHOR>
     * @date 2022/1/5 19:17
     */
    public AlterJrmDdl alterColumnList() {
        // 封装alterColumn列表
        alterColumnList = ColumnUtils.wrapAlterColumnList(newColumnList, oldColumnList);
        return this;
    }

    /**
     * 删除的列
     *
     * @return {@link AlterJrmDdl}
     * <AUTHOR>
     * @date 2022/1/5 19:17
     */
    public AlterJrmDdl deletedColumn() {
        // 删除列
        deletedColumnList =
                // 获取被删除的列-> 此处被删除只是进行列名更改,逻辑删除
                oldColumnList.stream()
                        .filter(old -> newColumnList.stream().noneMatch(c -> c.getCode().equals(old.getCode())))
                        .filter(old -> !TwinFixedColumnConstant.isDefaultTwinColumn(old.getName()))
                        .map(old -> {
                            // 标记为删除
                            final AlterColumn alterColumn = new AlterColumn(old);
                            alterColumn.setOld(old.getName());
                            alterColumn.setName(StringUtils.merge(JrmJsonUtils.DELETED_FLAG_PREFIX, old.getName(), StringConstant.UNDERSCORE, old.getCode()));
                            alterColumn.setRequired(Sql.NULL);
                            return alterColumn;
                        })
                        .collect(Collectors.toList());
        return this;
    }

    /**
     * 新增的列
     *
     * @return {@link AlterJrmDdl}
     * <AUTHOR>
     * @date 2022/1/5 19:17
     */
    public AlterJrmDdl addColumnList() {
        // 新增列
        addColumnList = alterColumnList.stream()
                .filter(c -> AlterColumn.AlterType.ADD_COLUMN.equals(c.getAlterType()))
                .collect(Collectors.toList());
        return this;
    }

    /**
     * 改变的列
     *
     * @return {@link AlterJrmDdl}
     * <AUTHOR>
     * @date 2022/1/5 19:17
     */
    public AlterJrmDdl changeColumnList() {
        // 改变列
        changeColumnList = acquireChangeColumnList();
        return this;
    }

    @Override
    public JrmDdl parse() {
        this.newColumnList = ColumnUtils.parseFormToColumn(formItem);
        if (StringUtils.isNotBlank(this.sourceStructure)) {
            this.oldColumnList = ColumnUtils.parseStructureToColumn(autoTableType, this.sourceStructure, AlterColumn.class, true);
        }
        return alterColumnList()
                .deletedColumn()
                .addColumnList().changeColumnList();
    }

    /**
     * 开启调用后置处理
     *
     * @return {@link AlterJrmDdl}
     * <AUTHOR>
     * @date 2022/5/13 16:52
     */
    public AlterJrmDdl enableInvokePostProcess() {

        this.enableInvokePostProcess = true;
        return this;
    }

    /**
     * 生成临时表
     */
    void generateTempTable() {
        final String targetTableName = JrmJsonUtils.TEMP_FLAG_PREFIX + tableName;
        final DataSource dataSource = SpringIocUtils.mustGetBean(DataSource.class);
        final Connection connection = DataSourceUtils.getConnection(dataSource);
        try (final TableSql tableSql = SchemaSqlFactory.getTableSql(dataSource, StringUtils.isNotBlank(connection.getSchema()) ? connection.getSchema() : DynamicDataSourceContextHolder.peek(), tableName)) {
            tableSql.output().setName(targetTableName);
            final String drop = tableSql.getDrop();
            final String create = tableSql.getCreate();
            SqlUtils.executeSql(connection.createStatement(), drop + create);
            SpringIocUtils.getBeanOp(TwinBodyDataMapper.class)
                    .orElseThrow(ThrowUtils.getThrowSupplier().internalServerError(String.format("异步生成临时表失败, %s", targetTableName)))
                    .copyData(tableName, targetTableName);
            log.info("已生成临时表: {}", targetTableName);
        } catch (Exception e) {
            log.info("临时表生成失败: {}", targetTableName);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void invoke() {
        // 生成临时表
        generateTempTable();
        // 过滤出基于旧列改变的操作 -> 必填
        final List<AlterColumn> oldColumnChangeUniqueList = changeColumnList.stream()
                // 改变的列一定是基于旧列的,所以不需要通过key判定是否是基于旧列,只要判定是否必填即可
                .filter(required -> Sql.NOT_NULL.equals(required.getRequired()))
                .collect(Collectors.toList());
        // 调用jrm改变列
        invokeJrmAlter(oldColumnChangeUniqueList, tableName, AlterColumn.AlterType.CHANGE_COLUMN.getName());
        changeColumnList.removeAll(oldColumnChangeUniqueList);
        // 优先调用jrm改变列进行逻辑删除
        invokeJrmAlter(deletedColumnList.stream().map(e->e.transformColumn(e)).collect(Collectors.toList()), tableName, AlterColumn.AlterType.CHANGE_COLUMN.getName());
        // 调用jrm新增列
        invokeJrmAlter(addColumnList.stream().map(e->e.transformColumn(e)).collect(Collectors.toList()), tableName, AlterColumn.AlterType.ADD_COLUMN.getName());
        // 调用jrm改变列
        invokeJrmAlter(changeColumnList.stream().map(e->e.transformColumn(e)).collect(Collectors.toList()), tableName, AlterColumn.AlterType.CHANGE_COLUMN.getName());
        // 执行后的处理
        if (enableInvokePostProcess) {
            // 异步删除临时表
            AsyncUtils.asyncExecutorBean(() -> {
                final String tempTable = JrmJsonUtils.TEMP_FLAG_PREFIX + tableName;
                int maxRetries = 5;
                int waitSeconds = 1;
                for (int attempt = 1; attempt <= maxRetries; attempt++) {
                    try {
                        TimeUnit.SECONDS.sleep(waitSeconds);
                        SpringIocUtils.getBeanOp(TwinBodyDataMapper.class)
                                .orElseThrow(ThrowUtils.getThrowSupplier().internalServerError(String.format("异步删除临时表失败, %s", tempTable)))
                                .dropTable(tempTable);
                        log.info("后置处理完成, 删除临时表: {}", tempTable);
                        // 删除成功，跳出循环
                        break;
                    } catch (Exception e) {
                        if (attempt == maxRetries) {
                            log.error("异步删除临时表失败请手动删除，已重试{}次: {}", maxRetries, tempTable, e);
                        } else {
                            log.warn("异步删除临时表失败，第{}次重试，等待{}秒: {}", attempt, waitSeconds, tempTable);
                            // 每次重试等待时间翻倍
                            waitSeconds *= 2;
                        }
                    }
                }
            }, JrmTransactionOption.THREAD_POOL);
        }
    }

    /**
     * 获取改变列列表
     *
     * @return 改变列列表
     * <AUTHOR>
     * @date 2021/10/10 14:52
     */
    private List<AlterColumn> acquireChangeColumnList() {

        // 先过滤出改变列
        final List<AlterColumn> changeColumnList = alterColumnList.stream()
                .filter(c -> AlterColumn.AlterType.CHANGE_COLUMN.equals(c.getAlterType()))
                .collect(Collectors.toList());
        boolean newColumnNotEmpty = !CollectionUtils.isEmpty(newColumnList);
        if (newColumnNotEmpty) {
            // 重置 create_time & update_time 顺序
            ColumnUtils.initDefaultColumn(autoTableType, true,
                            ColumnUtils.CREATE_TIME_COLUMN,
                            ColumnUtils.CREATE_USER_COLUMN,
                            ColumnUtils.UPDATE_TIME_COLUMN,
                            ColumnUtils.UPDATE_USER_COLUMN)
                    .forEach(c -> {
                        final AlterColumn alterColumn = new AlterColumn(c);
                        alterColumn.setOld(c.getName());
                        if (ColumnUtils.CREATE_TIME_COLUMN.equals(alterColumn.getName())) {
                            // create_user永远在新列列表最后一个列后面
                            Column createTimeBefore = newColumnList.get(newColumnList.size() - 1);
                            alterColumn.setAfter(createTimeBefore.getName());
                        } else if (ColumnUtils.CREATE_USER_COLUMN.equals(alterColumn.getName())) {
                            // create_time永远在create_user后面
                            alterColumn.setAfter(ColumnUtils.CREATE_TIME_COLUMN);
                        } else if (ColumnUtils.UPDATE_TIME_COLUMN.equals(alterColumn.getName())) {
                            // update_user永远在create_time后面
                            alterColumn.setAfter(ColumnUtils.CREATE_USER_COLUMN);
                        } else if (ColumnUtils.UPDATE_USER_COLUMN.equals(alterColumn.getName())) {
                            // update_time永远在update_user后面
                            alterColumn.setAfter(ColumnUtils.UPDATE_TIME_COLUMN);
                        }
                        changeColumnList.add(alterColumn);
                    });
        }
        for (AlterColumn changeColumn : changeColumnList) {
            for (AlterColumn deleteColumn : deletedColumnList) {

                final String oldName = deleteColumn.getOld();
                if (StringUtils.isNotBlank(oldName)) {
                    // 如果改变列中的after列已经决定被删除了,那么after改为逻辑删除后的列名
                    if (oldName.equals(changeColumn.getAfter())) {

                        changeColumn.setAfter(deleteColumn.getName());
                        break;
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(deletedColumnList)) {
            // 将所有被删除的列排在 update_time 后面
            for (int i = 0; i < deletedColumnList.size(); i++) {

                final AlterColumn deletedColumn = deletedColumnList.get(i);
                final AlterColumn alterColumn = new AlterColumn(deletedColumn);
                alterColumn.setOld(deletedColumn.getName());
                alterColumn.setAfter(0 == i ? ColumnUtils.UPDATE_TIME_COLUMN : deletedColumnList.get(i - 1).getName());
                changeColumnList.add(alterColumn);
            }
        }

        return changeColumnList;
    }


    /**
     * 调用jrm alter操作
     *
     * @param alterColumnList alter列列表
     * @param tableName       表名
     * @param kind            操作种类
     * <AUTHOR>
     * @date 2021/8/30 17:14
     */
    private void invokeJrmAlter(List<AlterColumn> alterColumnList, String tableName, String kind) {

        if (!CollectionUtils.isEmpty(alterColumnList)) {
            JrmJsonUtils.createAlter(tableName, kind, alterColumnList)
                    .forEach(req -> JrmBean.request(RequestType.ALTER, req));
        }
    }
}
