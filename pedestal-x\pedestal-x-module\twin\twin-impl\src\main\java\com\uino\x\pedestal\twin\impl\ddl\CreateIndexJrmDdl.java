package com.uino.x.pedestal.twin.impl.ddl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.common.exception.TwinClassException;
import com.uino.x.pedestal.twin.common.utils.JrmJsonUtils;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.uino.x.pedestal.twin.common.utils.JrmJsonUtils.VERSION_KEY;

/**
 * 创建索引jrm ddl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0.1
 */
@Slf4j
public class CreateIndexJrmDdl extends AbstractJrmDdl implements JrmDdl {


    /**
     * 构造方法
     *
     * @param autoTableType
     * @param tableName     表名
     * @param formItem      表单列表
     * <AUTHOR>
     */
    public CreateIndexJrmDdl(AutoTableTypeEnum autoTableType, String tableName, List<FormItem> formItem) {
        super(autoTableType, tableName, formItem, null);
    }

    /**
     * 允许的索引类型
     */
    static final List<String> ALLOW_INDEXES = List.of("NORMAL", "UNIQUE", "FULLTEXT", "SPATIAL");

    @Override
    protected String processorSourceStructure(String sourceStructure) {
        if (!formItem.stream().allMatch(fi -> ALLOW_INDEXES.contains(fi.getOptions().getIndexType()))) {
            throw new TwinClassException("孪生体属性索引类型不支持").forbidden();
        }
        final List<Map<String, Object>> createIndexes = new ArrayList<>();
        for (FormItem fi : formItem) {
            final String name = fi.getModel();
            final String key = fi.getKey();
            final String code = StringUtils.isNotBlank(key) ? key : IdUtils.get32UUID();
            final String indexType = fi.getOptions().getIndexType();
            final Map<String, Object> createIndexMap = new LinkedHashMap<>(8);
            final Map<String, Object> indexMap = new LinkedHashMap<>(8);
            indexMap.put("type",determineIndexType(indexType));
            indexMap.put("name", name + "_" + indexType + "_" + code);
            indexMap.put("tableName", tableName);
            indexMap.put("columnName", name);
            createIndexMap.put("index", indexMap);
            createIndexMap.put(VERSION_KEY, version);
            JrmJsonUtils.setTransaction(createIndexMap);
            createIndexes.add(createIndexMap);
        }

        return JSON.toJSONString(createIndexes);
    }

    /**
     * 确定索引类型
     *
     * @param indexType 索引类型
     * @return 索引类型
     * <AUTHOR>
     */
    private String determineIndexType(String indexType) {
        if ("NORMAL".equalsIgnoreCase(indexType)) {
            return "INDEX";
        }else if ("FULLTEXT".equalsIgnoreCase(indexType)) {
            if (SpringEnvUtils.isDameng()) {
                return "CONTEXT INDEX";
            }
        }
        return indexType + " INDEX";
    }

    @Override
    public void invoke() {
        final JSONArray array = JSONObject.parseArray(this.sourceStructure, Feature.OrderedField);
        for (int i = 0; i < array.size(); i++) {
            final String req = array.getJSONObject(i).toJSONString();
            try {
                JrmBean.request(RequestType.CREATE, req);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
    }
}
