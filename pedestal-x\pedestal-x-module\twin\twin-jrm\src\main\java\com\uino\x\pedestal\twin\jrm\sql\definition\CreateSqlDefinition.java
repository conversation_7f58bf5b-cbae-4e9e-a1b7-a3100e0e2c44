package com.uino.x.pedestal.twin.jrm.sql.definition;


import com.uino.x.pedestal.twin.jrm.core.domain.ArraysProcessorOptional;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.AbstractSqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.structure.AutoIncrementSortList;
import com.uino.x.pedestal.twin.jrm.sql.executor.RedisSqlExecutor;

import javax.sql.DataSource;

import static com.uino.x.pedestal.twin.common.constant.Sql.SPACE;

/**
 * ddl create相关sql描述
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/6/9 15:56
 */
public class CreateSqlDefinition<E> extends AbstractSqlDefinition {

    public CreateSqlDefinition(DataSource dataSource, JsonDefinition jsonDefinition) {
        super(dataSource, jsonDefinition, RequestType.CREATE);
    }

    @Override
    public AutoIncrementSortList<JsonSql> initJsonSqlList(Integer version) {

        // 通过select查询出 create json_sql_map 数据
        final String session = jsonDefinition().getSessionId();
        return RedisSqlExecutor
                .selectJsonSqlMapList(dataSource(), RequestType.CREATE, version, session);
    }

    @Override
    protected ArraysProcessorOptional acquireArraysProcessorOptional() {

        return new ArraysProcessorOptional("(", ",", ")");
    }
}
