[1;35m17:06:50.783[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m17:06:50.853[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m17:06:50.958[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m17:06:51.043[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m17:06:51.055[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SystemXApplication using Java 17.0.14 with PID 43096 (D:\x\pedestal-x\pedestal-x-app\system-x\target\classes started by Administrator in D:\x)
[1;35m17:06:51.059[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal-x"
[1;35m17:06:51.143[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m17:06:51.143[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m17:06:51.144[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=system-x.yml, group=DEFAULT_GROUP] success
[1;35m17:06:53.938[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'microAppFallbackFactory' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=microAppApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/microapp/api/config/MicroAppApiConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=projectPackApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/projectpack/api/config/ProjectPackApiConfig.class]]
[1;35m17:06:54.065[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.067[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.067[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.068[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.069[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.070[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.071[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.072[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.074[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.075[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.077[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.078[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.080[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.082[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.083[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.086[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.087[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.088[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.091[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.092[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.094[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.097[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.099[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.099[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.100[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.102[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.102[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.103[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.103[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.104[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.105[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.106[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.106[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.107[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.107[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.108[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.108[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.111[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.113[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.115[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.116[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.annotate:annotate}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.119[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.119[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.120[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.123[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.log:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.125[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.128[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.129[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.138[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.timer:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.142[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:54.146[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.viewpoint:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:06:55.071[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:06:55.084[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m17:06:55.160[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 53 ms. Found 0 Elasticsearch repository interfaces.
[1;35m17:06:55.185[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:06:55.188[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m17:06:55.212[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m17:06:55.239[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:06:55.241[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m17:06:55.282[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
[1;35m17:06:56.078[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=648f226e-c308-3833-857f-8f98c83d82fe
[1;35m17:06:56.122[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[registerStrategies,294][0;39m - No strategies provided for registration in engine [mysql]
[1;35m17:06:56.123[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [mysql] initialized with 0 strategies
[1;35m17:06:56.203[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m17:07:12.277[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m17:07:12.281[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m17:07:15.562[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10004 (http)
[1;35m17:07:15.639[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10004"]
[1;35m17:07:15.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m17:07:15.646[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m17:07:15.870[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m17:07:15.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 24725 ms
[1;35m17:07:18.768[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:07:20.304[0;39m [32m[redisson-netty-1-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:07:20.357[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:07:20.997[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:07:21.033[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:07:21.057[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:07:21.886[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m17:07:21.886[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [systemx]
[1;35m17:07:37.709[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,systemx} inited
[1;35m17:07:37.712[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-28 17:07:37","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1}]}
[1;35m17:07:56.323[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: ************************************* (MySQL 8.0)
[1;35m17:07:57.163[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 111 migrations (execution time 00:00.798s)
[1;35m17:07:57.191[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema `systemx`: 2025.08.27.15.05.19
[1;35m17:07:57.195[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema `systemx` is up to date. No migration necessary.
[1;35m17:07:57.988[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m17:07:58.206[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - micro-app-service-impl executor is create!
[1;35m17:07:58.962[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m17:07:59.316[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,781][0;39m - Authorization initialized
[1;35m17:07:59.316[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,782][0;39m - License status : Disabled
[1;35m17:08:24.548[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m17:08:24.617[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m17:08:24.741[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m17:08:24.861[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m17:08:24.872[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SystemXApplication using Java 17.0.14 with PID 41512 (D:\x\pedestal-x\pedestal-x-app\system-x\target\classes started by Administrator in D:\x)
[1;35m17:08:24.874[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal-x"
[1;35m17:08:24.973[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m17:08:24.974[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m17:08:24.975[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=system-x.yml, group=DEFAULT_GROUP] success
[1;35m17:08:28.417[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'microAppFallbackFactory' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=microAppApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/microapp/api/config/MicroAppApiConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=projectPackApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/projectpack/api/config/ProjectPackApiConfig.class]]
[1;35m17:08:28.552[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.553[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.555[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.556[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.557[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.558[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.559[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.561[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.562[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.564[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.565[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.567[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.568[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.570[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.571[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.574[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.575[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.577[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.579[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.580[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.582[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.584[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.586[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.586[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.587[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.588[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.590[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.591[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.591[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.592[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.592[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.593[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.594[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.594[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.595[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.595[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.599[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.601[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.602[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.605[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.annotate:annotate}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.608[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.610[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.610[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.611[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.611[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.613[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.log:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.615[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.617[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.619[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.627[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.timer:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.630[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:28.634[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.viewpoint:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:08:29.647[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:08:29.660[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m17:08:29.734[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 49 ms. Found 0 Elasticsearch repository interfaces.
[1;35m17:08:29.749[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:08:29.752[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m17:08:29.776[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m17:08:29.796[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:08:29.798[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m17:08:29.838[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
[1;35m17:08:30.663[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=648f226e-c308-3833-857f-8f98c83d82fe
[1;35m17:08:30.710[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[registerStrategies,294][0;39m - No strategies provided for registration in engine [mysql]
[1;35m17:08:30.711[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [mysql] initialized with 0 strategies
[1;35m17:08:30.790[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m17:08:46.876[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m17:08:46.882[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m17:08:50.048[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10004 (http)
[1;35m17:08:50.112[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10004"]
[1;35m17:08:50.119[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m17:08:50.120[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m17:08:50.318[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m17:08:50.319[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 25342 ms
[1;35m17:08:53.217[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:08:54.779[0;39m [32m[redisson-netty-1-4][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:08:54.807[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:08:55.448[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:08:55.488[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:08:55.524[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:08:56.362[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m17:08:56.362[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [systemx]
[1;35m17:09:12.162[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,systemx} inited
[1;35m17:09:12.165[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-28 17:09:12","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":16,"executeMillisTotal":16,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1}]}
[1;35m17:09:30.784[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: ************************************* (MySQL 8.0)
[1;35m17:09:31.469[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 111 migrations (execution time 00:00.654s)
[1;35m17:09:31.556[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema `systemx`: 2025.08.27.15.05.19
[1;35m17:09:31.560[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema `systemx` is up to date. No migration necessary.
[1;35m17:09:32.334[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m17:09:32.532[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - micro-app-service-impl executor is create!
[1;35m17:09:33.234[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m17:09:58.069[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,781][0;39m - Authorization initialized
[1;35m17:09:58.071[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[init,782][0;39m - License status : Disabled
[1;35m17:10:12.178[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":1,"activePeak":2,"activePeakTime":"2025-08-28 17:09:30","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-28 17:09:30","connectCount":2,"closeCount":1,"notEmptyWaitCount":1,"notEmptyWaitMillis":15643,"physicalConnectCount":1,"executeCount":28,"commitCount":3,"pstmtCacheHitCount":14,"pstmtCacheMissCount":14,"startTransactionCount":2,"transactionHistogram":[0,0,2],"connectionHoldTimeHistogram":[0,0,0,1],"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT version()","executeCount":2,"executeMillisMax":3,"executeMillisTotal":4,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'","executeCount":1,"executeMillisMax":7,"executeMillisTotal":7,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT DATABASE()","executeCount":3,"executeMillisMax":1,"executeMillisTotal":3,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL","executeCount":6,"executeMillisMax":12,"executeMillisTotal":25,"executeHistogram":[0,5,1],"executeAndResultHoldHistogram":[0,5,1],"concurrentMax":1,"inTransactionCount":1},{"sql":"SELECT @@foreign_key_checks","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT @@sql_safe_updates","executeCount":1,"executeMillisMax":0,"executeMillisTotal":0,"executeHistogram":[1],"executeAndResultHoldHistogram":[1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SHOW DATABASES LIKE 'RDSAdmin';","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT SUBSTRING_INDEX(USER(),'@',1)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1","executeCount":2,"executeMillisMax":7,"executeMillisTotal":9,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"inTransactionCount":1},{"sql":"SET foreign_key_checks=?, sql_safe_updates=?","executeCount":5,"executeMillisMax":6,"executeMillisTotal":19,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"inTransactionCount":1},{"sql":"SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `systemx`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`","executeCount":2,"executeMillisMax":5,"executeMillisTotal":8,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,1,1],"concurrentMax":1,"fetchRowCount":111,"fetchRowCountMax":111,"fetchRowHistogram":[1,0,0,1],"inTransactionCount":1},{"sql":"SELECT GET_LOCK(?,10)","executeCount":1,"executeMillisMax":11,"executeMillisTotal":11,"executeHistogram":[0,0,1],"executeAndResultHoldHistogram":[0,0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT RELEASE_LOCK('Flyway--613968976')","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[],"concurrentMax":1}]}
[1;35m17:11:12.187[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":1,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m17:11:29.018[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Directory info : D:\x
[1;35m17:11:29.020[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk usage : 10428497954
[1;35m17:11:29.022[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Disk info : Drive > D:\    Total space > 70179434496    Free space > 20884963328
[1;35m17:11:29.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Network info : {eth5={hostname=DESKTOP-38F0Q6A, ip=**********, network-arch=Realtek Gaming GbE Family Controller, ipnet=ipv4, mac=18-C0-4D-EE-B7-42}}
[1;35m17:11:29.025[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU ID : null
[1;35m17:11:29.027[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - CPU core : **********
[1;35m17:11:29.028[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System : Windows 11
[1;35m17:11:29.029[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System version : 10.0
[1;35m17:11:29.031[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System architecture: amd64
[1;35m17:11:29.033[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - System hostname : DESKTOP-38F0Q6A
[1;35m17:11:29.034[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - Total memory : 51397271552
[1;35m17:11:29.036[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.l.s.LicenseAuthorityAdapter[0;39m - [36m[printDeploymentLog,825][0;39m - User Info : [must]
companyNameZh=优锘
mmdID=uino
validityPeriod=development
[other]
companyNameEn=uino
releaseTime=************
[1;35m17:12:32.024[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":1,"poolingCount":1,"connectCount":0,"closeCount":0}
[1;35m17:12:32.031[0;39m [32m[nacos-grpc-client-executor-***********-83][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [294eb2cc-7971-4c55-9985-d1e8ec0de0e2_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 924
[1;35m17:12:32.031[0;39m [32m[nacos-grpc-client-executor-***********-83][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [294eb2cc-7971-4c55-9985-d1e8ec0de0e2_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 924
[1;35m17:12:32.182[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.l.i.ProductLicenseInfoServiceImpl[0;39m - [36m[<clinit>,145][0;39m - 当前机器标识: **********,**********
[1;35m17:12:44.841[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m17:12:44.919[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m17:12:45.053[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m17:12:45.150[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m17:12:45.160[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStarting,53][0;39m - Starting SystemXApplication using Java 17.0.14 with PID 52184 (D:\x\pedestal-x\pedestal-x-app\system-x\target\classes started by Administrator in D:\x)
[1;35m17:12:45.162[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.p.a.s.SystemXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal-x"
[1;35m17:12:45.248[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m17:12:45.249[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m17:12:45.249[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=system-x.yml, group=DEFAULT_GROUP] success
[1;35m17:12:48.859[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'microAppFallbackFactory' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=microAppApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/microapp/api/config/MicroAppApiConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=projectPackApiConfig; factoryMethodName=microAppFallbackFactory; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/pedestal/projectpack/api/config/ProjectPackApiConfig.class]]
[1;35m17:12:49.006[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.008[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.009[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.011[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.alarm:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.013[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.014[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.014[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.performance:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.017[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.019[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.021[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.023[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.024[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.026[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.027[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.029[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.031[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.036[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.038[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.040[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.042[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.045[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.twin:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.047[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.048[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.049[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.050[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.051[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.052[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.053[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.053[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.054[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.055[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.056[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.056[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.057[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.057[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.058[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.058[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.059[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.059[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.identity:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.061[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.063[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.066[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.placement:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.068[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.annotate:annotate}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.073[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.file:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.074[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.074[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.075[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.076[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.dict:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.078[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.log:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.082[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.tenant:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.085[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.bubble:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.087[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.config:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.098[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.timer:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.102[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.effectpackage:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:49.107[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean '${module.feign-config.viewpoint:${spring.application.name}}.FeignClientSpecification' with a different definition: replacing [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null] with [Generic bean: class=org.springframework.cloud.openfeign.FeignClientSpecification; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
[1;35m17:12:50.125[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:12:50.138[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[1;35m17:12:50.207[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 47 ms. Found 0 Elasticsearch repository interfaces.
[1;35m17:12:50.221[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:12:50.224[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[1;35m17:12:50.247[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
[1;35m17:12:50.276[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m17:12:50.278[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m17:12:50.322[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
[1;35m17:12:51.096[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=648f226e-c308-3833-857f-8f98c83d82fe
[1;35m17:12:51.142[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[registerStrategies,294][0;39m - No strategies provided for registration in engine [mysql]
[1;35m17:12:51.142[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.s.t.SqlTransformationEngine[0;39m - [36m[<init>,235][0;39m - SQL transformation engine [mysql] initialized with 0 strategies
[1;35m17:12:51.223[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-1} inited
[1;35m17:13:07.304[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2063][0;39m - {dataSource-1} closing ...
[1;35m17:13:07.309[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[close,2136][0;39m - {dataSource-1} closed
[1;35m17:13:10.450[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.t.TomcatWebServer[0;39m - [36m[initialize,111][0;39m - Tomcat initialized with port 10004 (http)
[1;35m17:13:10.512[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.h.Http11NioProtocol[0;39m - [36m[log,168][0;39m - Initializing ProtocolHandler ["http-nio-10004"]
[1;35m17:13:10.518[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardService[0;39m - [36m[log,168][0;39m - Starting service [Tomcat]
[1;35m17:13:10.518[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.StandardEngine[0;39m - [36m[log,168][0;39m - Starting Servlet engine: [Apache Tomcat/10.1.43]
[1;35m17:13:10.727[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.a.c.c.C.[.[.[/][0;39m - [36m[log,168][0;39m - Initializing Spring embedded WebApplicationContext
[1;35m17:13:10.728[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[0;39m - [36m[prepareWebApplicationContext,301][0;39m - Root WebApplicationContext: initialization completed in 25477 ms
[1;35m17:13:13.670[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:13:15.260[0;39m [32m[redisson-netty-1-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:13:15.290[0;39m [32m[redisson-netty-1-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:13:15.920[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m17:13:15.971[0;39m [32m[redisson-netty-5-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m17:13:16.004[0;39m [32m[redisson-netty-5-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m17:13:16.824[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[addDataSource,158][0;39m - dynamic-datasource - add a datasource named [systemx] success
[1;35m17:13:16.825[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.b.d.d.DynamicRoutingDataSource[0;39m - [36m[afterPropertiesSet,241][0;39m - dynamic-datasource initial loaded [1] datasource,primary datasource named [systemx]
[1;35m17:13:32.610[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSource[0;39m - [36m[init,975][0;39m - {dataSource-2,systemx} inited
[1;35m17:13:32.613[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":0,"poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-28 17:13:32","connectCount":0,"closeCount":0,"physicalConnectCount":1,"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":5,"executeMillisTotal":5,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1}]}
[1;35m17:13:51.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.FlywayExecutor[0;39m - [36m[info,41][0;39m - Database: ************************************* (MySQL 8.0)
[1;35m17:13:51.789[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbValidate[0;39m - [36m[info,41][0;39m - Successfully validated 111 migrations (execution time 00:00.626s)
[1;35m17:13:51.845[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Current version of schema `systemx`: 2025.08.27.15.05.19
[1;35m17:13:51.852[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[info,41][0;39m - Schema `systemx` is up to date. No migration necessary.
[1;35m17:13:52.674[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.o.FeignClientFactoryBean[0;39m - [36m[getTarget,472][0;39m - For 'system-x' URL not provided. Will try picking an instance via load-balancing.
[1;35m17:13:52.906[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - micro-app-service-impl executor is create!
[1;35m17:13:53.626[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.c.f.ExecutorServiceFactory[0;39m - [36m[createThreadPool,154][0;39m - tenant-info-service-impl executor is create!
[1;35m17:26:26.900[0;39m [32m[Druid-ConnectionPool-Log-**********][0;39m [34mINFO [0;39m [36mc.a.d.p.DruidDataSourceStatLoggerImpl[0;39m - [36m[log,80][0;39m - {"url":"*************************************?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true","dbType":"mysql","name":"systemx","activeCount":1,"activePeak":2,"activePeakTime":"2025-08-28 17:13:51","poolingCount":1,"poolingPeak":1,"poolingPeakTime":"2025-08-28 17:13:51","connectCount":2,"closeCount":1,"notEmptyWaitCount":1,"notEmptyWaitMillis":15636,"physicalConnectCount":1,"executeCount":28,"commitCount":3,"pstmtCacheHitCount":14,"pstmtCacheMissCount":14,"startTransactionCount":2,"transactionHistogram":[0,1,1],"connectionHoldTimeHistogram":[0,0,0,1],"sqlList":[{"sql":"SELECT 1 FROM DUAL","executeCount":1,"executeMillisMax":7,"executeMillisTotal":7,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT version()","executeCount":2,"executeMillisMax":4,"executeMillisTotal":5,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2]},{"sql":"select VARIABLE_VALUE from performance_schema.global_variables where variable_name = 'pxc_strict_mode'","executeCount":1,"executeMillisMax":6,"executeMillisTotal":6,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT @@GLOBAL.ENFORCE_GTID_CONSISTENCY","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT DATABASE()","executeCount":3,"executeMillisMax":2,"executeMillisTotal":5,"executeHistogram":[0,3],"executeAndResultHoldHistogram":[0,3],"concurrentMax":1,"fetchRowCount":3,"fetchRowCountMax":1,"fetchRowHistogram":[0,3]},{"sql":"SELECT variable_name FROM performance_schema.user_variables_by_thread WHERE variable_value IS NOT NULL","executeCount":6,"executeMillisMax":9,"executeMillisTotal":32,"executeHistogram":[0,6],"executeAndResultHoldHistogram":[0,6],"concurrentMax":1,"inTransactionCount":1},{"sql":"SELECT @@foreign_key_checks","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT @@sql_safe_updates","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SHOW DATABASES LIKE 'RDSAdmin';","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1},{"sql":"SELECT SUBSTRING_INDEX(USER(),'@',1)","executeCount":1,"executeMillisMax":2,"executeMillisTotal":2,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT COUNT(1) FROM information_schema.schemata WHERE schema_name=? LIMIT 1","executeCount":2,"executeMillisMax":2,"executeMillisTotal":4,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,2],"concurrentMax":1,"fetchRowCount":2,"fetchRowCountMax":1,"fetchRowHistogram":[0,2],"inTransactionCount":1},{"sql":"SET foreign_key_checks=?, sql_safe_updates=?","executeCount":5,"executeMillisMax":7,"executeMillisTotal":21,"executeHistogram":[0,5],"executeAndResultHoldHistogram":[0,5],"concurrentMax":1,"inTransactionCount":1},{"sql":"SELECT `installed_rank`,`version`,`description`,`type`,`script`,`checksum`,`installed_on`,`installed_by`,`execution_time`,`success` FROM `systemx`.`flyway_schema_history` WHERE `installed_rank` > ? ORDER BY `installed_rank`","executeCount":2,"executeMillisMax":5,"executeMillisTotal":7,"executeHistogram":[0,2],"executeAndResultHoldHistogram":[0,1,1],"concurrentMax":1,"fetchRowCount":111,"fetchRowCountMax":111,"fetchRowHistogram":[1,0,0,1],"inTransactionCount":1},{"sql":"SELECT GET_LOCK(?,10)","executeCount":1,"executeMillisMax":1,"executeMillisTotal":1,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[0,1],"concurrentMax":1,"fetchRowCount":1,"fetchRowCountMax":1,"fetchRowHistogram":[0,1]},{"sql":"SELECT RELEASE_LOCK('Flyway--613968976')","executeCount":1,"executeMillisMax":3,"executeMillisTotal":3,"executeHistogram":[0,1],"executeAndResultHoldHistogram":[],"concurrentMax":1}]}
[1;35m17:26:26.907[0;39m [32m[nacos-grpc-client-executor-***********-42][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [a0afaa79-628b-42ae-964c-4d2363654fab_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 925
[1;35m17:26:26.908[0;39m [32m[nacos-grpc-client-executor-***********-42][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [a0afaa79-628b-42ae-964c-4d2363654fab_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 925
