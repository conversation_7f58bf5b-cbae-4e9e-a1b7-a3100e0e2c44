package com.uino.x.pedestal.twin.jrm.sql.definition;


import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.AbstractSqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.structure.AutoIncrementSortList;
import com.uino.x.pedestal.twin.jrm.sql.executor.RedisSqlExecutor;

import javax.sql.DataSource;

import static com.uino.x.pedestal.twin.common.constant.Sql.SPACE;

/**
 * ddl drop相关sql描述
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/6/9 15:56
 */
public class DropSqlDefinition extends AbstractSqlDefinition {

    public DropSqlDefinition(DataSource dataSource, JsonDefinition jsonDefinition) {
        super(dataSource, jsonDefinition, RequestType.DROP);
    }

    @Override
    public AutoIncrementSortList<JsonSql> initJsonSqlList(Integer version) {

        // 通过select查询出 drop json_sql_map 数据
        final String session = jsonDefinition().getSessionId();
        return RedisSqlExecutor
                .selectJsonSqlMapList(dataSource(), RequestType.DROP, version, session);
    }
}
