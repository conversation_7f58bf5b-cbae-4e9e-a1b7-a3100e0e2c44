<!--
 * @Description: 三维场景预览-性能分析
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-05-05 14:14:58
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 10:22:13
-->
<template>
  <div class="performance-analysis keep-size">
    <div class="top-wrap">
      <div class="top-item">当前面数：{{ triangles }}</div>
      <div class="top-item">
        当前帧数：{{ fps }}/帧率小于16时，需要优化<span class="bug-icon" @click="clickShowBottom"><IconFont class="item-icon" type="icon-bug" />性能分析</span
        ><eye-outlined class="save-view" :style="{ color: hasView ? 'yellowgreen' : '#fff' }" title="保存当前视角" @click="saveCameraView" />
      </div>
      <div class="top-item" :title="levelBreadcrumb.join(' / ')">当前位置：{{ levelBreadcrumb.join(' / ') }}</div>
      <div class="top-item" :title="cameraInfo">当前视角：{{ cameraInfo }}</div>
    </div>
    <div v-show="showBottom" class="bottom-wrap">
      <div class="bottom-item">当前层对象：{{ currentObjects }}</div>
      <div class="bottom-item">当前层面数：{{ currentMeshs }}</div>
      <div class="bottom-item">
        <a-checkbox v-model:checked="showBoundingBox" class="tool-check" @change="toggleBoundingBox">展示包围盒</a-checkbox>
        <a-checkbox v-model:checked="showPerformanceTree" @change="toggleShow" class="tool-check">当前场景详情</a-checkbox>
      </div>
      <div v-show="showPerformanceTree" class="bottom-tree">
        <div class="tree-header">
          <div class="tree-header-item">类型</div>
          <div class="tree-header-item">名称</div>
          <div class="tree-header-item">ID</div>
          <div class="tree-header-item">面数</div>
          <div class="tree-header-item">操作</div>
        </div>
        <div class="tree-body">
          <a-tree
            ref="sceneTree"
            v-model:expandedKeys="expandedKeys"
            :block-node="true"
            :height="320"
            :selectable="false"
            :tree-data="treeData"
            :field-names="replaceFields"
            class="performance-tree"
            :default-expand-all="false"
          >
            <template #title="item: any">
              <div class="root-tree-item" @click.stop="flyTo(item)">
                <span :title="item.type"> {{ item.type }}</span>
                <span :title="item.name">
                  <div v-if="getTextWidth(item.name, 14) > 105" class="move" :style="{ '--i': moveTime(item) }">
                    {{ item.name }}
                  </div>
                  <div v-else>{{ item.name || '--' }}</div>
                </span>
                <span :title="item.id">
                  <div v-if="getTextWidth(item.id, 14) > 105" class="move" :style="{ '--i': moveTime(item) }">
                    {{ item.id }}
                  </div>
                  <div v-else>{{ item.id || '--' }}</div>
                </span>
                <span :title="item.facesNum">{{ item.facesNum }}</span>
                <span v-if="(item.type == '建筑' || item.type == '楼层') && !item.isFirst" class="handle-name" @click.stop="clickEnterLevel(item)">进入</span>
                <span v-else>--</span>
              </div>
            </template>
          </a-tree>
          <IconFont v-show="showBack" class="back-icon" type="icon-back" @click="clickBack" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { v4 } from 'uuid';
import { setFlattenData, flatten } from '@/utils/util';
import { saveCameraConfig, getScenesThing } from '@/api/business/scene';
import { useSceneStore } from '@/store/scene';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';

// 定义emit
const emit = defineEmits(['refreshTree']);
// 当前层级是否已经保存视角
const hasView = ref(false);
const sceneStore = useSceneStore();
// 帧率
const fps = ref(0);
// 面数
const triangles = ref(0);
// 对象
const currentObjects = ref(0);
// 面
const currentMeshs = ref(0);
// 展开的树节点
const expandedKeys = ref<any>([]);
// 替换对应字段
const replaceFields = ref({
  children: 'children',
  title: 'name',
  key: 'key',
});
// 展示性能分析树
const showBottom = ref(false);
// 点击切换展示性能分析
const clickShowBottom = () => {
  showBottom.value = !showBottom.value;
  // 还原点击的对象
  if (currentFlyObj) {
    currentFlyObj.style.outlineColor = null;
    currentFlyObj = null;
  }
  // 获取场景性能树
  if (showBottom.value) {
    getTreeData();
  } else {
    showBoundingBox.value = false;
    toggleBoundingBox();
  }
};
// 展示包围盒
const showBoundingBox = ref(false);
// 展示性能分析树
const showPerformanceTree = ref(false);
// boundingbox切换
const toggleBoundingBox = () => {
  if (showBoundingBox.value) {
    if (sceneStore.thingjsVersion === 1) {
      window.app.level.current.style.boundingBox = true;
    } else {
      window.app.level.current.helper.boundingBox.visible = true;
    }
  } else {
    if (sceneStore.thingjsVersion === 1) {
      window.app.level.current.style.boundingBox = false;
    } else {
      window.app.level.current.helper.boundingBox.visible = false;
    }
  }
};
// 点击保存视角
const saveCameraView = (item: any, type = 0) => {
  let currentLevelData = item;
  if (type === 0) {
    const levelDatas = setFlattenData(window.campusManagerIns.curCampusData.children);
    const id = window.app.level.current.id;
    const levelType = window.app.level.current.type;
    if (levelType === 'Campus') {
      currentLevelData = levelDatas.find((item: any) => item.name === '室外');
    } else if (levelType === 'Floor') {
      const findBuilding = levelDatas.find((node) => `${node.userid}` === `${window.app.level.current.parent.id}`);
      if (findBuilding) {
        currentLevelData = levelDatas.find((node) => findBuilding.parentSceneUUID === node.parentSceneUUID && `${node.userid}` === `${window.app.level.current.id}`);
      } else {
        currentLevelData = levelDatas.find((node) => `${node.userid}` === `${window.app.level.current.id}`);
      }
    } else if (levelType === 'Thing' || levelType === 'Entity') {
      if (currentThingLevelData) {
        currentLevelData = currentThingLevelData;
      } else {
        useGlobalMessage('warning', '当前物体信息不存在');
        return;
      }
    } else {
      currentLevelData = levelDatas.find((item: any) => {
        return `${item.userid}` === `${id}`;
      });
    }
  }
  const param = {
    camType: type,
    mainUuid: currentLevelData.parentSceneUUID,
    configCamInfo: JSON.stringify({
      eye: window.app.camera.position.toString(),
      target: window.app.camera.target.toString(),
      distance: window.app.camera.distance,
    }),
    childUuid: currentLevelData.uuid,
  };
  console.log('保存视角传参', param);
  saveCameraConfig(param).then((res: any) => {
    if (res.code === 200) {
      currentLevelData.configCamInfo = param.configCamInfo;
      useGlobalMessage('success', '视角保存成功！');
      hasView.value = true;
      // 刷新左侧树数据
      emit('refreshTree');
      if (type === 0) {
        // 更新场景数据
        window.campusManagerIns.updateCampusData();
      }
    }
  });
};
// 层级面包屑
const levelBreadcrumb = ref<string[]>(['园区']);
// 树数据
const treeData = ref<any[]>([]);
// 展示返回按钮
const showBack = ref(false);
// 当前飞向的物体
let currentFlyObj: any = null;
// 飞向物体
const flyTo = (item: any) => {
  // 还原点击的对象
  if (currentFlyObj) {
    currentFlyObj.style.outlineColor = null;
    currentFlyObj = null;
  }
  const objId = item.id ? window.app.level.current.query(`#${item.id}`)[0] : null;
  const objUuid = item.uuid ? window.app.level.current.query(`##${item.uuid}`)[0] || window.app.level.current.query(`[uuid=${item.uuid}]`)[0] : null;
  const obj = objId || objUuid;
  if (obj) {
    window.app.camera.fit(obj);
    obj.style.outlineColor = '#00ff00';
    currentFlyObj = obj;
  }
};
const toggleShow = (val: any) => {
  if (!val.target.checked) {
    // 还原点击的对象
    if (currentFlyObj) {
      currentFlyObj.style.outlineColor = null;
      currentFlyObj = null;
    }
  }
};
// 点击进入层级
const clickEnterLevel = (item: any) => {
  // 还原点击的对象
  if (currentFlyObj) {
    currentFlyObj.style.outlineColor = null;
    currentFlyObj = null;
  }
  const objId = window.app.level.current.query(`#${item.id}`)[0];
  const objUuid = window.app.level.current.query(`##${item.uuid}`)[0] || window.app.level.current.query(`[uuid=${item.uuid}]`)[0];
  const obj = objId || objUuid;
  if (obj) {
    window.app.level.change(obj);
  }
};
// 点击返回
const clickBack = () => {
  // 还原点击的对象
  if (currentFlyObj) {
    currentFlyObj.style.outlineColor = null;
    currentFlyObj = null;
  }
  window.app.level.back();
};
// 获取当前层级的场景树
const getTreeData = () => {
  treeData.value = [];
  const currentType = window.app.level.current.type;
  if (currentType === 'Campus') {
    showBack.value = false;
    const { current } = window.app.level;
    let facesNum = 0;
    if (sceneStore.thingjsVersion === 1) {
      facesNum = current.node.getFacesNumber();
    } else {
      facesNum = current.getGeometryInfo().triangles;
    }
    const campusParam = {
      id: current.id,
      name: current.name,
      facesNum: facesNum,
      key: v4(),
      uuid: current.uuid,
      type: '园区',
      children: [] as any[],
      isFirst: true,
    };
    // eslint-disable-next-line no-underscore-dangle
    let things = [];
    if (sceneStore.thingjsVersion === 1) {
      window.app.level.current._traverseObjects((obj: any) => {
        things.push(obj);
      }, true);
    } else {
      things = flatten(window.app.level.current.children);
    }
    things.forEach((v: any) => {
      const { id, uuid, name, type } = v;
      let facesNum = 0;
      if (sceneStore.thingjsVersion === 1) {
        facesNum = v.node.getFacesNumber();
      } else {
        facesNum = v.getGeometryInfo().triangles;
      }
      campusParam.children.push({
        id,
        facesNum,
        key: v4(),
        uuid,
        type: type === 'Building' ? '建筑' : type === 'Floor' ? '楼层' : type === 'Room' ? '房间' : '对象',
        name,
        isLeaf: true,
      });
    });
    campusParam.children.sort((a, b) => (a.facesNum > b.facesNum ? -1 : 0));
    treeData.value.push(campusParam);
  } else if (currentType === 'Building') {
    showBack.value = true;
    const { current } = window.app.level;
    let facesNum = 0;
    if (sceneStore.thingjsVersion === 1) {
      facesNum = current.node.getFacesNumber();
    } else {
      facesNum = current.getGeometryInfo().triangles;
    }
    const buildingParam = {
      id: current.id,
      facesNum,
      key: v4(),
      uuid: current.uuid,
      scopedSlots: { title: 'custom' },
      name: current.name,
      type: '建筑',
      children: [] as any[],
      isFirst: true,
    };
    // eslint-disable-next-line no-underscore-dangle
    let things = [];
    if (sceneStore.thingjsVersion === 1) {
      window.app.level.current._traverseObjects((obj: any) => {
        things.push(obj);
      }, true);
    } else {
      things = flatten(window.app.level.current.children);
    }
    things.forEach((v: any) => {
      const { id, uuid, name, type } = v;
      let facesNum = 0;
      if (sceneStore.thingjsVersion === 1) {
        facesNum = v.node.getFacesNumber();
      } else {
        facesNum = v.getGeometryInfo().triangles;
      }
      buildingParam.children.push({
        id,
        facesNum,
        key: v4(),
        uuid,
        type: type === 'Building' ? '建筑' : type === 'Floor' ? '楼层' : type === 'Room' ? '房间' : '对象',
        name,
        isLeaf: true,
      });
    });
    buildingParam.children.sort((a, b) => (a.facesNum > b.facesNum ? -1 : 0));
    treeData.value.push(buildingParam);
  } else if (currentType === 'Floor') {
    showBack.value = true;
    const { current } = window.app.level;
    let facesNum = 0;
    if (sceneStore.thingjsVersion === 1) {
      facesNum = current.node.getFacesNumber();
    } else {
      facesNum = current.getGeometryInfo().triangles;
    }
    const floorParam = {
      id: current.id,
      facesNum,
      key: v4(),
      uuid: current.uuid,
      scopedSlots: { title: 'custom' },
      name: current.name,
      type: '楼层',
      children: [] as any[],
      isFirst: true,
    };
    // eslint-disable-next-line no-underscore-dangle
    let things = [];
    if (sceneStore.thingjsVersion === 1) {
      window.app.level.current._traverseObjects((obj: any) => {
        things.push(obj);
      }, true);
    } else {
      things = flatten(window.app.level.current.children);
    }
    things.forEach((v: any) => {
      const { id, uuid, name, type } = v;
      let facesNum = 0;
      if (sceneStore.thingjsVersion === 1) {
        facesNum = v.node.getFacesNumber();
      } else {
        facesNum = v.getGeometryInfo().triangles;
      }
      floorParam.children.push({
        id,
        facesNum,
        key: v4(),
        uuid,
        type: type === 'Room' ? '房间' : '对象',
        name,
        isLeaf: true,
      });
    });
    floorParam.children.sort((a, b) => (a.facesNum > b.facesNum ? -1 : 0));
    treeData.value.push(floorParam);
  }
  // 只有一个时，展开第一个
  if (treeData.value.length === 1) {
    expandedKeys.value = [treeData.value[0].key];
  }
};
// 滚动时间
const moveTime = (v: any) => {
  if (v.name.length > 30) {
    return 12;
  }
  if (v.name.length > 20) {
    return 9;
  }
  return 6;
};
// 获取字段长度
const getTextWidth = (str: string, fontSize: number) => {
  let result = 10;
  const ele = document.createElement('span');
  // 字符串中带有换行符时，会被自动转换成<br/>标签，若需要考虑这种情况，可以替换成空格，以获取正确的宽度
  // str = str.replace(/\\n/g,' ').replace(/\\r/g,' ');
  ele.innerText = str;
  // 不同的大小和不同的字体都会导致渲染出来的字符串宽度变化，可以传入尽可能完备的样式信息
  ele.style.fontSize = `${fontSize}px`;
  // 由于父节点的样式会影响子节点，这里可按需添加到指定节点上
  document.documentElement.append(ele);
  result = ele.offsetWidth;
  document.documentElement.removeChild(ele);
  return result;
};
// 当前物体层级数据
let currentThingLevelData: any = '';
// 监听层级切换-处理当前位置面包屑/更新当前层级面和对象数据
window.app.on(
  THING.EventType.EnterLevel,
  '*',
  (e: any) => {
    currentThingLevelData = '';
    // 还原点击的对象
    if (currentFlyObj) {
      currentFlyObj.style.outlineColor = null;
      currentFlyObj = null;
    }
    const { current } = e;
    if (current.type === 'Campus') {
      levelBreadcrumb.value = ['园区'];
      // 处理当前层级面和对象
      // eslint-disable-next-line no-underscore-dangle
      let objectLength = 0;
      if (sceneStore.thingjsVersion === 1) {
        const things = [];
        current._traverseObjects((obj: any) => {
          things.push(obj);
        }, true);
        objectLength = things.length;
      } else {
        const objs = flatten(current.children);
        objectLength = objs?.length;
      }
      currentObjects.value = objectLength;
      let facesNum = 0;
      if (sceneStore.thingjsVersion === 1) {
        facesNum = current.node.getFacesNumber();
      } else {
        facesNum = current.getGeometryInfo().triangles;
      }
      currentMeshs.value = facesNum;
      // 处理左侧树选中
      const outDoor = window.campusManagerIns.curCampusData.children.find((child: any) => child.dataType === 'outdoors');
      if (outDoor) {
        sceneStore.currentLevelUuid = [`${outDoor.uuid}`];
        // 是否保存视角
        hasView.value = !!outDoor.configCamInfo;
      }
    } else if (current.type === 'Building') {
      const currentBuidingData = window.campusManagerIns.curCampusData.children.find((child: any) => `${child.userid}` === `${current.id}`);
      if (currentBuidingData) {
        // 处理左侧树选中
        levelBreadcrumb.value[1] = `${currentBuidingData.settingName || currentBuidingData.name}(${currentBuidingData.userid || ''})`;
        if (levelBreadcrumb.value[2]) {
          levelBreadcrumb.value.splice(2);
        }
        sceneStore.currentLevelUuid = [`${currentBuidingData.uuid}`];
        // 是否保存视角
        hasView.value = !!currentBuidingData.configCamInfo;
      }
      // 处理当前层级面和对象
      let objectLength = 0;
      if (sceneStore.thingjsVersion === 1) {
        const things = [];
        current._traverseObjects((obj: any) => {
          things.push(obj);
        }, true);
        objectLength = things.length;
      } else {
        const objs = flatten(current.children);
        objectLength = objs?.length;
      }
      currentObjects.value = objectLength;
      let facesNum = 0;
      if (sceneStore.thingjsVersion === 1) {
        facesNum = current.node.getFacesNumber();
      } else {
        facesNum = current.getGeometryInfo().triangles;
      }
      currentMeshs.value = facesNum;
    } else if (current.type === 'Floor') {
      const currentBuidingData = window.campusManagerIns.curCampusData.children.find((child: any) => `${child.userid}` === `${current.parent.id}`);
      if (currentBuidingData) {
        levelBreadcrumb.value[1] = `${currentBuidingData.settingName || currentBuidingData.name}(${currentBuidingData.userid || ''})`;
        const currentFloorData = currentBuidingData.children.find((child: any) => `${child.userid}` === `${current.id}`);
        if (currentFloorData) {
          levelBreadcrumb.value[2] = `${currentFloorData.settingName || currentFloorData.name}(${currentFloorData.userid || ''})`;
          if (levelBreadcrumb.value[3]) {
            levelBreadcrumb.value.splice(3);
          }
          // 处理左侧树选中
          sceneStore.currentLevelUuid = [`${currentFloorData.uuid}`];
          setTimeout(() => {
            // 处理左侧树展开
            sceneStore.expandedKeys = Array.from(new Set([...sceneStore.expandedKeys, currentBuidingData.uuid]));
          }, 300);
          // 是否保存视角
          hasView.value = !!currentFloorData.configCamInfo;
        }
      }
    } else if (current.type === 'Room') {
      const currentBuidingData = window.campusManagerIns.curCampusData.children.find((child: any) => `${child.userid}` === `${current.parent.parent.id}`);
      if (currentBuidingData) {
        levelBreadcrumb.value[1] = `${currentBuidingData.settingName || currentBuidingData.name}(${currentBuidingData.userid || ''})`;
        const currentFloorData = currentBuidingData.children.find((child: any) => `${child.userid}` === `${current.parent.id}`);
        if (currentFloorData) {
          levelBreadcrumb.value[2] = `${currentFloorData.settingName || currentFloorData.name}(${currentFloorData.userid || ''})`;
          const currentRoomData = currentFloorData.children.find((child: any) => `${child.userid}` === `${current.id}`);
          if (currentRoomData) {
            levelBreadcrumb.value[3] = `${currentRoomData.name}(${currentRoomData.userid})`;
          }
          // 是否保存视角
          hasView.value = !!currentRoomData?.configCamInfo;
        }
      }
    } else if (current.type === 'Thing' || current.type === 'Entity') {
      const parent = current.parent;
      if (parent.type === 'Campus') {
        const findDevice = allPalcements.value.find((item: any) => `${item.userid}` === `${current.id}`);
        if (findDevice) {
          levelBreadcrumb.value[1] = `${findDevice.name}(${findDevice.userid})`;
          currentThingLevelData = findDevice;
          // 是否保存视角
          hasView.value = !!findDevice.configCamInfo;
          // 视角飞行
          if (hasView.value) {
            const toCamInfo = findDevice.configCamInfo;
            if (toCamInfo) {
              const to = JSON.parse(toCamInfo);
              eyeFly(to);
            }
          }
        }
      } else if (parent.type === 'Floor') {
        const currentBuidingData = window.campusManagerIns.curCampusData.children.find((child: any) => `${child.userid}` === `${current.parent.parent.id}`);
        if (currentBuidingData) {
          levelBreadcrumb.value[1] = `${currentBuidingData.settingName || currentBuidingData.name}(${currentBuidingData.userid || ''})`;
          const currentFloorData = currentBuidingData.children.find((child: any) => `${child.userid}` === `${current.parent.id}`);
          if (currentFloorData) {
            levelBreadcrumb.value[2] = `${currentFloorData.settingName || currentFloorData.name}(${currentFloorData.userid || ''})`;
            const findDevice = allPalcements.value.find((item: any) => `${item.userid}` === `${current.id}`);
            if (findDevice) {
              levelBreadcrumb.value[3] = `${findDevice.name}(${findDevice.userid})`;
              currentThingLevelData = findDevice;
              // 是否保存视角
              hasView.value = !!findDevice.configCamInfo;
              // 视角飞行
              if (hasView.value) {
                const toCamInfo = findDevice.configCamInfo;
                if (toCamInfo) {
                  const to = JSON.parse(toCamInfo);
                  eyeFly(to);
                }
              }
            }
          }
        }
      }
    }
    // 处理当前层级面和对象
    let objectLength = 0;
    if (sceneStore.thingjsVersion === 1) {
      const things = [];
      current._traverseObjects((obj: any) => {
        things.push(obj);
      }, true);
      objectLength = things.length;
    } else {
      const objs = flatten(current.children);
      objectLength = objs?.length;
    }
    currentObjects.value = objectLength;
    let facesNum = 0;
    if (sceneStore.thingjsVersion === 1) {
      facesNum = current.node.getFacesNumber();
    } else {
      facesNum = current.getGeometryInfo().triangles;
    }
    currentMeshs.value = facesNum;

    // 获取当前层级性能树
    if (showBottom.value) {
      getTreeData();
    }
  },
  'performanceTag'
);
// 当前视角
const cameraInfo = ref('');
// 监听摄像机位置
window.app.on(
  THING.EventType.CameraChangeEnd,
  (ev: any) => {
    const eyeArr = ev.position;
    const targetArr = ev.target;
    eyeArr.forEach((item: any, index: number) => {
      eyeArr[index] = Number(item).toFixed(2);
    });
    targetArr.forEach((item: any, index: number) => {
      targetArr[index] = Number(item).toFixed(2);
    });
    cameraInfo.value = `pos[${eyeArr.join(',')}] target[${targetArr.join(',')}]`;
  },
  'performanceTag'
);
// 飞到设置视角
const eyeFly = (e: any) => {
  if (e && e.eye && e.target) {
    const positionArr = typeof e.eye === 'string' ? e.eye.replace(' ', ',').split(',') : e.eye;
    const targetArr = typeof e.target === 'string' ? e.target.replace(' ', ',').split(',') : e.target;
    const position: number[] = [];
    const target: number[] = [];
    positionArr.forEach((item: string) => position.push(Number(item)));
    targetArr.forEach((item: string) => target.push(Number(item)));
    if (sceneStore.thingjsVersion === 1) {
      window.app.camera.flyTo({
        position,
        target,
        radius: e.distance,
        time: 1000,
      });
    } else {
      window.app.camera.flyTo({
        position,
        target,
        distance: e.distance,
        duration: 1000,
      });
    }
  }
};
// 当前场景所有物体
const allPalcements = ref([]);
const queryAllPlacements = () => {
  getScenesThing({ uuid: window.campusManagerIns.curCampusData.uuid })
    .then((res: any) => {
      console.log('所有物体res', res);
      if (res.code === 200) {
        allPalcements.value = res.data || [];
      } else {
        allPalcements.value = [];
      }
    })
    .catch(() => {
      allPalcements.value = [];
    });
};
onMounted(() => {
  // 当前视角
  const eyeArr = window.app.camera.position;
  const targetArr = window.app.camera.target;
  eyeArr.forEach((item: any, index: number) => {
    eyeArr[index] = Number(item).toFixed(2);
  });
  targetArr.forEach((item: any, index: number) => {
    targetArr[index] = Number(item).toFixed(2);
  });
  cameraInfo.value = `pos[${eyeArr.join(',')}] target[${targetArr.join(',')}]`;
  // 处理当前层级面和对象
  let facesNum = 0;
  let objectLength = 0;
  if (sceneStore.thingjsVersion === 1) {
    const things = [];
    window.app.level.current._traverseObjects((obj: any) => {
      things.push(obj);
    }, true);
    objectLength = things.length;
  } else {
    const objs = flatten(window.app.level.current.children);
    objectLength = objs?.length;
  }
  if (sceneStore.thingjsVersion === 1) {
    facesNum = window.app.level.current.node.getFacesNumber();
  } else {
    facesNum = window.app.scene.renderState.triangles;
  }
  // 处理当前层级面和对象
  currentObjects.value = objectLength;
  currentMeshs.value = sceneStore.thingjsVersion === 1 ? facesNum : window.app.level.current?.getGeometryInfo().triangles;
  // 获取帧率面数数据
  setInterval(() => {
    fps.value = sceneStore.thingjsVersion === 1 ? window.app.renderStates.fps : window.app.fpsCounter;
    triangles.value = sceneStore.thingjsVersion === 1 ? window.app.level.current?.node.getFacesNumber() : window.app.scene.renderState.triangles;
  }, 200);
  // 处理当前层级是否保存视角
  const outDoor = window.campusManagerIns.curCampusData.children.find((child: any) => child.dataType === 'outdoors');
  if (outDoor) {
    // 是否保存视角
    hasView.value = !!outDoor.configCamInfo;
  }
  // 查询当前场景所有的物体
  queryAllPlacements();
});
onBeforeUnmount(() => {
  if (currentFlyObj) {
    currentFlyObj.style.outlineColor = null;
    currentFlyObj = null;
  }
  window.app.off(THING.EventType.EnterLevel, null, 'performanceTag');
  window.app.off(THING.EventType.CameraChangeEnd, null, 'performanceTag');
});
</script>
<style scoped lang="scss">
.performance-analysis.keep-size {
  box-sizing: border-box;
  width: 463px;
  padding: 12px 16px;
  font-family: 'PingFang Medium';
  font-size: 14px;
  font-weight: 400;
  line-height: 26px;
  color: #fff;
  pointer-events: all;
  user-select: text;
  background: rgb(29 31 36 / 60%);
  border-radius: 4px;

  :deep(.ant-tree-switcher) {
    line-height: 38px;
  }

  .top-wrap {
    .top-item {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .bug-icon {
        display: inline-block;
        margin-left: 4px;
        line-height: 26px;
        color: var(--theme-color);
        cursor: pointer;
      }

      .save-view {
        display: inline-block;
        margin-left: 4px;
        line-height: 26px;
        cursor: pointer;
      }

      .anticon {
        margin-right: 1px;
        font-size: 16px;
        vertical-align: -3px;
      }
    }
  }

  .bottom-wrap {
    width: 100%;

    .bottom-item {
      color: #fff;

      :deep(.ant-checkbox-wrapper) {
        color: #fff;
      }
    }
  }

  .bottom-tree {
    width: 100%;

    .tree-header {
      display: flex;
      width: 100%;
      height: 37px;
      font-family: 'PingFang Bold';
      font-size: 14px;
      font-weight: 500;
      line-height: 37px;
      color: #fff;
      border-bottom: 1px solid rgb(255 255 255 / 20%);

      .tree-header-item {
        text-align: center;

        &:nth-of-type(1) {
          width: 124px;
        }

        &:nth-of-type(2) {
          width: 81px;
        }

        &:nth-of-type(3) {
          width: 81px;
        }

        &:nth-of-type(4) {
          width: 81px;
        }

        &:nth-of-type(5) {
          width: 64px;
        }
      }
    }

    .tree-body {
      position: relative;
      width: 100%;
      height: 370px;
      overflow: hidden;

      .root-tree-item {
        display: flex;
        align-items: center;
        width: 100%;
        overflow: hidden;
        color: #fff;
        transition: all 1s;

        span {
          box-sizing: border-box;
          display: block;
          margin-right: 5px;
          overflow: hidden;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;

          &:nth-of-type(1) {
            flex: 1;
            text-align: left;
          }

          &:nth-of-type(2) {
            width: 81px;
          }

          &:nth-of-type(3) {
            width: 81px;
          }

          &:nth-of-type(4) {
            width: 81px;
          }

          &:nth-of-type(5) {
            width: 47px;
          }
        }

        .handle-name {
          color: var(--theme-color);
        }

        .move {
          width: 110px !important;
          white-space: nowrap;
          animation: move calc(1s * var(--i)) infinite linear normal;
        }

        @keyframes move {
          0% {
            transform: translateX(0);
          }

          100% {
            transform: translateX(-100%);
          }
        }

        &:hover {
          color: var(--theme-color);
        }
      }

      :deep(.ant-tree) {
        font-family: 'PingFang Medium';
        font-size: 14px;
        font-weight: 400;
        color: #fff;
        background: transparent;
      }

      .back-icon {
        position: absolute;
        right: 20px;
        bottom: 0;
        font-size: 30px;
        color: var(--theme-color);
        cursor: pointer;
      }
    }
  }
}
</style>
