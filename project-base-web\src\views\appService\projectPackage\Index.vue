<!--
* @Author: 开发者
* @Change: 更改者
* @Date: 年-月-日
* @Description: 项目工程包
-->
<template>
  <div class="plugin-manage">
    <!-- 搜索栏 -->
    <div class="search-wrap">
      <div class="search-content">
        <div class="search-item">
          <span class="search-label">关键字</span>
          <a-input v-model:value="searPar.name" placeholder="请输入名称查询" class="search-input" @keyup.enter="clickSearch()" />
        </div>
        <div class="search-btns">
          <a-button type="primary" class="search-btn" @click="clickSearch()"> 查询 </a-button>
          <a-button class="search-btn" @click="reset()"> 重置 </a-button>
        </div>
      </div>
      <div class="table-handle">
        <a-button v-if="hasPerm('project-pack:add')" class="handle-btn" type="primary" @click="handel('add')">新增项目工程</a-button>
      </div>
    </div>
    <div class="table-wrap">
      <div ref="table" class="table-content">
        <a-table class="table" :scroll="{ y: scrollY }" :pagination="false" size="small" :loading="confirmLoading" :row-key="(record: any) => record.id" :columns="columns" :data-source="tabData">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'createTime'">
              {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template v-if="column.dataIndex === 'url'">
              {{ getUrl(record.url) }}
              <CopyOutlined style="font-size: 16px" title="复制链接" @click="copySkipUrl(record.url)" />
            </template>
            <!-- <template v-if="column.dataIndex === 'status'">
              <a-popconfirm placement="top" :title="record.status === 0 ? '确定停用该工程包？' : '确定启用该工程包？'" @confirm="() => editStatus(record)">
                <a-switch :disabled="!hasPerm('project-pack:change-status')" checked-children="启用" un-checked-children="停用" :checked="record.status === 0" />
              </a-popconfirm>
            </template> -->
            <template v-if="column.dataIndex === 'action'">
              <div class="table-actions">
                <a :href="getUrl(record.url)" target="_blank">打开</a>
                <a v-if="hasPerm('project-pack:edit')" @click="handel('edit', record)">编辑</a>
                <a v-if="hasPerm('project-pack:delete')" @click="showComfirm(record)">删除</a>
              </div>
            </template>
          </template>
        </a-table>
        <div class="pagination">
          <a-pagination v-if="tabData.length > 0" v-bind="paginationConfig" @change="paginationChange" />
        </div>
      </div>
    </div>
    <!-- 新增工程包弹窗 -->
    <AddPlugin ref="addPluginRef" @ok="handelOnBack" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue';
import { Modal } from 'ant-design-vue';
import storage from 'store2';
import dayjs from 'dayjs';
import clipboard from 'clipboard';
import AddPlugin from './AddPlugin.vue';
import useTableScrollY from '@/hooks/useTableScrollY';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { packagePage, packageDelete, packageEdit, changeStatus } from '@/api/appService';

// 表头
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    customRender: (text: any) => {
      // console.log(text)
      return `${text.index + 1}`;
    },
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '上传时间',
    dataIndex: 'createTime',
    width: 200,
    ellipsis: true,
  },
  {
    title: '项目大小',
    dataIndex: 'size',
    width: 100,
    ellipsis: true,
  },
  {
    title: '访问地址',
    dataIndex: 'url',
    ellipsis: true,
  },
  // {
  //   title: '启用状态',
  //   dataIndex: 'status',
  //   width: 100,
  //   ellipsis: true,
  // },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150,
  },
];
const confirmLoading = ref(false);
const tabData: any = ref([]);
const table = ref();
const { scrollY } = useTableScrollY(table);
// 搜索栏的筛选条件
const searPar = ref<{
  name: string | null;
  pageNo: number;
  pageSize: number;
  total: number;
}>({
  name: '', // 关键字
  pageNo: 1,
  pageSize: 10,
  total: 0,
});
// 分页配置
const paginationConfig = computed(() => ({
  total: searPar.value.total,
  current: searPar.value.pageNo,
  pageSize: searPar.value.pageSize,
  showTotal: (total: number) => `共 ${total} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  size: 'small' as any,
}));

// 分页变化
const paginationChange = (current: number, pageSize: number) => {
  searPar.value.pageNo = current;
  searPar.value.pageSize = pageSize;
  getData();
};
onMounted(() => {
  getData();
});

// 重置
const reset = () => {
  searPar.value.name = '';
  clickSearch();
};
// 点击搜搜
const clickSearch = () => {
  searPar.value.pageNo = 1;
  searPar.value.pageSize = 10;
  getData();
};
// 获取表单数据
const getData = () => {
  tabData.value = [];
  confirmLoading.value = true;
  packagePage({
    name: searPar.value.name,
    pageNo: searPar.value.pageNo,
    pageSize: searPar.value.pageSize,
  })
    .then((res: any) => {
      if (res.code === 200) {
        const { rows, pageNo, pageSize, totalRows } = res.data;
        tabData.value = rows;
        searPar.value.pageNo = pageNo;
        searPar.value.pageSize = pageSize;
        searPar.value.total = totalRows;
      }
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
// #region 表格操作栏

// 卸载插件
const showComfirm = (record: any) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除当前工程包？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      deleteHandel(record);
    },
  });
};
// 删除插件
const deleteHandel = (record: any) => {
  packageDelete({ id: record.id }).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '删除成功');
      clickSearch();
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
// 改变状态 0 启用 1 停用 2删除
const editStatus = (record: any) => {
  changeStatus({
    id: record.id,
    status: record.status ? 0 : 1,
  }).then((res: any) => {
    if (res.code === 200) {
      useGlobalMessage('success', `${`${record.status}` === '0' ? '禁用' : '启用'}${record.name}成功`);
      clickSearch();
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
// #endregion
// #region 新增编辑弹窗
const addPluginRef = ref();
const handel = (type: string, record: any = null) => {
  addPluginRef.value.init(type, record);
};
// 回调
const handelOnBack = () => {
  clickSearch();
};
// #endregion
/**
 * 复制访问链接
 *
 */
const copySkipUrl = (url: string) => {
  clipboard.copy(getUrl(url));
  useGlobalMessage('success', '复制成功');
};
const getUrl = (url: string) => {
  // 判断url是否以"xxv"开头，如果不是，则添加"osr/resource/"前缀
  const processedUrl = url && url.startsWith('xxv') ? url : `osr/resource/${url}`;
  return `${window.baseConfig.hostUrl}/${processedUrl}index.html`;
};
</script>

<style lang="scss" scoped>
.plugin-manage {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0 10px;
  overflow: hidden;
  background: var(--primary-bg-color);
  border-radius: 4px;

  .nav-wrap {
    box-sizing: border-box;
    display: flex;
    border-bottom: 1px solid var(--primary-border-color);

    .nav {
      box-sizing: border-box;
      height: 55px;
      margin-right: 40px;
      font-family: 'PingFang Bold';
      line-height: 55px;
      cursor: pointer;

      &.active {
        color: var(--theme-color);
        border-bottom: 3px solid var(--theme-color);
      }
    }
  }

  .table-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;

    .table-handle {
      padding: 16px 0;

      .handle-btn {
        margin-right: 10px;
        border-radius: 4px;
      }
    }

    .table-content {
      flex: 1;

      .table {
        height: calc(100% - 40px);
      }

      .pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        text-align: right;
      }
    }
  }
}
</style>
