{"groups": [{"name": "data-migrate", "type": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.version-baseline", "type": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties$VersionBaseLine", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties", "sourceMethod": "getVersionBaseline()"}], "properties": [{"name": "data-migrate.allow-migration-data-table", "type": "java.util.Set<java.lang.String>", "description": "允许迁移数据的表", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.ignore-info-map", "type": "java.util.Map<java.lang.String,java.util.List<com.uino.x.common.datasource.util.IgnoreInfo>>", "description": "忽略的数据库表映射", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.migration-server-list", "type": "java.util.List<java.lang.String>", "description": "迁移服务列表", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.password", "type": "java.lang.String", "description": "压缩密码", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.tag", "type": "java.lang.String", "description": "版本号", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties"}, {"name": "data-migrate.version-baseline.business-data", "type": "java.lang.String", "description": "业务数据", "sourceType": "com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties$VersionBaseLine"}], "hints": [], "ignored": {"properties": []}}