<!--
 * @Description: 右侧面板-新增孪生体面板
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-03 17:29:30
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-27 10:17:54
-->
<template>
  <div id="add-twin" class="add-twin keep-px">
    <div id="add-twin-head" class="add-twin-top">
      <span class="title"> 新增{{ activeTwin?.title || activeTwin?.twinClassName || '孪生体' }}</span>
      <div class="btn-wrap">
        <!-- <a-button class="btn" title="保存" @click="clickSave">
                    <template #icon><IconFont title="保存" class="func-icon" type="icon-save"></IconFont></template>
                </a-button> -->
        <a-button class="btn" title="设置点位视角" @click="clickSetView">
          <template #icon>
            <IconFont title="设置点位视角" class="func-icon" type="icon-view" />
          </template>
        </a-button>
        <a-button class="btn" title="定位" @click="clickLocation">
          <template #icon>
            <IconFont title="定位" class="func-icon" type="icon-location" />
          </template>
        </a-button>
        <!-- <a-button class="btn" title="关闭" @click="reset">
          <template #icon><IconFont title="关闭" class="func-icon" type="icon-close" /></template>
        </a-button> -->
      </div>
    </div>
    <div class="add-twin-bottom">
      <div class="tab-wrap">
        <div class="tab-item" :class="{ active: activeTab === 2 }" @click="toggleTab(2)">字段属性</div>
        <div class="tab-item" :class="{ active: activeTab === 1 }" @click="toggleTab(1)">设置</div>
        <div class="tab-item" v-if="codeRule.show" :class="{ active: activeTab === 3 }" @click="toggleTab(3)">编码规则</div>
        <div class="tab-item" v-else></div>
      </div>
      <div class="tab-content">
        <div v-show="activeTab === 1" class="twin-prop-content">
          <a-form ref="twinPropRef" :model="twinProp" label-align="left" class="twin-prop-form keep-px">
            <a-form-item class="special-form-item">
              <template #label>
                放大倍数<unlock-outlined v-if="scaleModel === 2" title="非等比缩放" style="margin-left: 3px; font-size: 12px; cursor: pointer" @click.stop="changeScaleModel" />
                <lock-outlined v-if="scaleModel === 1" title="等比缩放" style="margin-left: 3px; font-size: 12px; cursor: pointer" @click.stop="changeScaleModel" />
              </template>
              <div class="special-input-wrap">
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.scale[0]"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    step="0.1"
                    class="special-input"
                    :min="0.01"
                    :max="1000"
                    @change="(value: number | string) => changeVal(value, 'scale')"
                  />
                  <span class="input-notice">X</span>
                </div>
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.scale[1]"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    :disabled="scaleModel === 1"
                    step="0.1"
                    :min="0.01"
                    :max="1000"
                    class="special-input"
                    @change="(value: number | string) => changeVal(value, 'scale')"
                  />
                  <span class="input-notice">Y</span>
                </div>
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.scale[2]"
                    :min="0.01"
                    :max="1000"
                    :disabled="scaleModel === 1"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    step="0.1"
                    class="special-input"
                    @change="(value: number | string) => changeVal(value, 'scale')"
                  />
                  <span class="input-notice">Z</span>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="离地高度(m)">
              <a-input-group compact class="normal-group">
                <a-input-number
                  v-model:value="twinProp.offsetHeight"
                  :formatter="numberFormat"
                  :parser="numberParse"
                  step="0.1"
                  class="normal-input"
                  @change="(value: number | string) => changeVal(value, 'offsetHeight')"
                />
              </a-input-group>
            </a-form-item>
            <a-form-item label="旋转角度" class="special-form-item">
              <div class="special-input-wrap">
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.angles[0]"
                    :min="-180"
                    :max="180"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    step="0.1"
                    class="special-input"
                    @change="(value: number | string) => changeVal(value, 'angles')"
                  />
                  <span class="input-notice">X</span>
                </div>
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.angles[1]"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    step="0.1"
                    class="special-input"
                    @change="(value: number | string) => changeVal(value, 'angles')"
                  />
                  <span class="input-notice">Y</span>
                </div>
                <div class="special-input-item">
                  <a-input-number
                    v-model:value="twinProp.angles[2]"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    step="0.1"
                    class="special-input"
                    @change="(value: number | string) => changeVal(value, 'angles')"
                  />
                  <span class="input-notice">Z</span>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="模型大小" class="special-form-item">
              <div class="special-input-wrap">
                <div class="special-input-item">
                  <a-input-number v-model:value="twinProp.size[0]" :formatter="numberFormat" :parser="numberParse" step="0.1" class="special-input" :disabled="true" />
                  <span class="input-notice">长</span>
                </div>
                <div class="special-input-item">
                  <a-input-number v-model:value="twinProp.size[1]" :formatter="numberFormat" :parser="numberParse" step="0.1" :disabled="true" class="special-input" />
                  <span class="input-notice">高</span>
                </div>
                <div class="special-input-item">
                  <a-input-number v-model:value="twinProp.size[2]" :formatter="numberFormat" :parser="numberParse" step="0.1" :disabled="true" class="special-input" />
                  <span class="input-notice">宽</span>
                </div>
              </div>
            </a-form-item>
            <div v-if="activeTwin?.dataType === 'LINE'" class="line-twin-form">
              <a-form-item label="类型">
                <a-input-group compact class="normal-group">
                  <a-select v-model:value="twinProp.lineType" @change="(value: any) => changeVal(value, 'lineType')">
                    <a-select-option value="Line">Line</a-select-option>
                    <a-select-option value="PolygonLine">PolygonLine</a-select-option>
                    <a-select-option value="RouteLine">RouteLine</a-select-option>
                  </a-select>
                </a-input-group>
              </a-form-item>
              <a-form-item v-show="twinProp.lineType !== 'Line'" label="宽度">
                <a-input-group compact class="normal-group">
                  <a-input-number
                    v-model:value="twinProp.lineWidth"
                    :formatter="numberFormat"
                    :parser="numberParse"
                    :min="0.01"
                    step="0.1"
                    class="normal-input"
                    @change="(value: number | string) => changeVal(value, 'lineWidth')"
                  />
                </a-input-group>
              </a-form-item>
              <a-form-item label="颜色">
                <div class="twin-color-wrap">
                  <pick-colors
                    v-model:value="twinProp.lineColor"
                    :theme="theme"
                    popup-container="body"
                    format="rgb"
                    :size="32"
                    show-alpha
                    @change="(value: string | string[]) => changeColor(value, 'lineColor')"
                  />
                </div>
              </a-form-item>
            </div>
            <div v-if="activeTwin?.dataType === 'SURFACE'" class="line-twin-form">
              <a-form-item label="区域颜色">
                <div class="twin-color-wrap">
                  <pick-colors
                    v-model:value="twinProp.regionColor"
                    :theme="theme"
                    popup-container="body"
                    format="rgb"
                    :size="32"
                    show-alpha
                    @change="(value: string | string[]) => changeColor(value, 'regionColor')"
                  />
                </div>
              </a-form-item>
              <a-form-item label="边框颜色">
                <div class="twin-color-wrap">
                  <pick-colors
                    v-model:value="twinProp.regionLineColor"
                    :theme="theme"
                    popup-container="body"
                    format="rgb"
                    :size="32"
                    show-alpha
                    @change="(value: string | string[]) => changeColor(value, 'regionLineColor')"
                  />
                </div>
              </a-form-item>
            </div>
            <a-form-item label="当前视角" class="special-form-item">
              <div class="special-input-wrap">
                <div class="special-input-item">
                  <a-input v-model:value="currentPos" class="special-input-eye" />
                  <span class="input-notice">P</span>
                </div>
                <div class="special-input-item">
                  <a-input v-model:value="currentTarget" class="special-input-eye" />
                  <span class="input-notice">T</span>
                </div>
                <a-tooltip title="清除视角">
                  <a-button class="sync-btn" @click="clearEye">
                    <template #icon><eye-invisible-outlined /></template>
                  </a-button>
                </a-tooltip>
              </div>
            </a-form-item>
            <!-- 模型可以贴图 -->
            <a-form-item label="选取贴图" v-if="activeTwin?.dataType === 'POINT' && activeTwin?.thingsModelType !== 'Particles'">
              <a-input v-model:value="modelImgConfig.img" data-type="selectTree" readonly placeholder="请选择图标" @focus="handleModelImg" id="ttpt">
                <template #suffix>
                  <down-outlined
                    :style="{
                      transform: `rotate(${bubbleVisible ? '180deg' : '0deg'})`,
                    }"
                  />
                </template>
              </a-input>
              <teleport to="body">
                <SelectBubble
                  v-if="bubbleVisible"
                  :bubble-info-id="modelImgConfig.bubbleInfoId"
                  @check-bubble="checkBubble"
                  class="tt-sel"
                  :style="{ '--width': `${ttSize.width}px`, '--top': `${ttSize.top}px`, '--minWidth': '500px' }"
                />
              </teleport>
            </a-form-item>
          </a-form>
        </div>
        <div v-show="activeTab === 2" class="twin-custom-prop">
          <AntGenerateForm :key="updataKey" ref="myDesignFormRef" :data="formDatas" :value="targetData" :upload-config="uploadConfig" @change="updateFlag = true">
            <template #extendContent="{ element, data, disable, onBack }">
              <AssociatedTwins :element="element" :data="data" :disable="disable" @on-back="(res: any) => onBack(res)" />
            </template>
          </AntGenerateForm>
        </div>
        <div v-show="activeTab === 3" class="twin-prop-content">
          <a-form ref="codeRuleRef" :model="codeRule" label-align="left" class="twin-prop-form keep-px">
            <a-form-item label="前缀">
              <a-input v-model:value="codeRule.prefix" class="normal-input" />
            </a-form-item>
            <a-form-item label="后缀">
              <a-input v-model:value="codeRule.suffix" class="normal-input" />
            </a-form-item>
            <a-form-item label="起始值">
              <a-input-group compact class="normal-group">
                <a-input-number v-model:value="codeRule.begin" :formatter="numberFormat" :parser="numberParse" step="1" class="normal-input" />
              </a-input-group>
            </a-form-item>
            <a-form-item label="间隔数">
              <a-input-group compact class="normal-group">
                <a-input-number v-model:value="codeRule.step" :min="1" :formatter="numberFormat" :parser="numberParse" step="1" class="normal-input" />
              </a-input-group>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="tab-btn">
        <div class="save-btn-wrap">
          <a-button type="primary" @click="clickSave">保存</a-button>
          <div v-if="updateFlag" class="notice-icon">*</div>
        </div>
        <a-button @click="reset">取消</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed, nextTick, onBeforeUnmount } from 'vue';
import { AntGenerateForm } from '@ti-cli/vue3-form-ant3';
import PickColors from 'vue-pick-colors';
import _ from 'lodash';
import AssociatedTwins from '../extend/AssociatedTwins.vue';
import { saveTwinBodyPointInfo } from '@/api/business/scene';
import { detailFileBatch } from '@/api/develop/attachmentManage';
import { useSceneStore } from '@/store/scene';
import { useSettingStore } from '@/store/setting';
import { getSaveParam } from '@/utils/campusPlacement/disposeSaveParam';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import useEmitter from '@/hooks/useEmiter';
import DragMove from '@/utils/dragMove';
import { useCookies } from 'vue3-cookies';
import axios from 'axios';
import { generateRequestId } from '@/utils/util';
import SelectBubble from '@/components/SelectBubble.vue';
const tenant: any = axios.defaults.headers.common['Tenant'];

const { cookies } = useCookies();
const sceneStore = useSceneStore();
const settingStore = useSettingStore();
const emmiter = useEmitter();
const scaleModel = ref(1); // 放大倍数模式 1 等比缩放 2 固定缩放
const changeScaleModel = () => {
  scaleModel.value = scaleModel.value === 1 ? 2 : 1;
};
// 当前设备视角
const currentPos = ref('');
const currentTarget = ref('');
// 清除视角
const clearEye = () => {
  window.campusPlacementIns.twinObject.userData.eye = '';
  window.campusPlacementIns.twinObject.userData.target = '';
  window.campusPlacementIns.twinObject.userData.distance = 0;
  currentPos.value = '';
  currentTarget.value = '';
  useGlobalMessage('success', '清除视角成功，保存后生效！');
  updateFlag.value = true;
};

// 回滚已上传的文件
const rollbackUploadedFiles = async (fileIds: string[]) => {
  try {
    await detailFileBatch({ ids: fileIds });
    console.log('🗑️ 已删除上传失败时的文件:', fileIds);
  } catch (error) {
    console.error('❌ 删除文件失败:', error);
  }
};

// 处理延迟上传的文件
const processDelayedUploads = async (formData: any) => {
  // 检查是否有表单数据
  if (!formDatas.value?.list) return [];

  const uploadedFileIds: string[] = []; // 记录已上传的文件ID，用于回滚

  try {
    for (const field of formDatas.value.list) {
      if (field.type === 'uploadFile' && formData[field.model]) {
        const fileList = formData[field.model];

        for (let i = 0; i < fileList.length; i++) {
          const fileItem = fileList[i];

          // 检查是否是延迟上传的文件（有 realFile 属性）
          if (fileItem.response && fileItem.response.realFile) {
            try {
              // 手动上传真实文件
              const uploadResult = await uploadSingleFile(fileItem.response.realFile);

              // 记录上传成功的文件ID
              uploadedFileIds.push(uploadResult.data);

              // 更新文件信息
              fileItem.response = {
                data: uploadResult.data,
                status: 'done',
              };
              fileItem.status = 'done';
            } catch (error) {
              fileItem.status = 'error';
              // 如果有文件上传失败，删除已上传的文件
              if (uploadedFileIds.length > 0) {
                await rollbackUploadedFiles(uploadedFileIds);
              }
              throw new Error(`文件 ${fileItem.name} 上传失败`);
            }
          }
        }
      }
    }

    // 返回已上传的文件ID列表，用于表单提交失败时的回滚
    return uploadedFileIds;
  } catch (error) {
    // 如果整个过程失败，确保清理已上传的文件
    if (uploadedFileIds.length > 0) {
      await rollbackUploadedFiles(uploadedFileIds);
    }
    throw error;
  }
};

// 上传单个文件 - 使用真实文件对象
const uploadSingleFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('bucketName', 'twinfile');

  const response = await axios.post(`${window.baseConfig.appApi}/systemx/sys-file-info/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${cookies.get(`ACCESS_TOKEN_${tenant}`)}`,
      Tenant: tenant,
      'X-Security-FreshToken': generateRequestId(),
    },
  });

  return response.data;
};

// 上传配置
const uploadConfig = ref({
  action: `${window.baseConfig.appApi}/systemx/sys-file-info/upload`,
  headers: {
    Authorization: `Bearer ${cookies.get(`ACCESS_TOKEN_${tenant}`)}`,
    Tenant: tenant,
    'X-Security-FreshToken': generateRequestId(),
  },
  generateRequestId: generateRequestId,
  data: { bucketName: 'twinfile' },
  delayUpload: true, // 🔑 启用延迟上传
});
// 点击设置点位视角
const clickSetView = () => {
  currentPos.value = JSON.stringify(window.app.camera.position);
  currentTarget.value = JSON.stringify(window.app.camera.target);
  window.campusPlacementIns.twinObject.userData.eye = JSON.stringify(window.app.camera.position);
  window.campusPlacementIns.twinObject.userData.target = JSON.stringify(window.app.camera.target);
  window.campusPlacementIns.twinObject.userData.distance = window.app.camera.distance;
  useGlobalMessage('success', '点位视角设置成功，保存后生效！');
  updateFlag.value = true;
};
// 点击定位
const clickLocation = () => {
  const eye = window.campusPlacementIns.twinObject?.userData.eye;
  const target = window.campusPlacementIns.twinObject?.userData.target;
  const distance = window.campusPlacementIns.twinObject?.userData.distance;
  if (eye && target) {
    window.app.camera.flyTo({
      position: JSON.parse(eye),
      target: JSON.parse(target),
      radius: distance,
      time: 1500,
      duration: 1500,
    });
  } else {
    if (sceneStore.thingjsVersion === 1) {
      window.app.camera.flyTo({
        object: window.campusPlacementIns.twinObject,
        time: 1500,
      });
    } else {
      window.app.camera.flyTo({
        target: window.campusPlacementIns.twinObject,
        duration: 1500,
      });
    }
  }
};
// 判断面板值是否改变
const updateFlag = ref(false);
const codeRuleRef = ref();
// 点击保存
const clickSave = async () => {
  let uploadedFileIds: string[] = [];

  try {
    // 获取自定义表单数据
    let formData = await myDesignFormRef.value.getData();

    // 🔑 处理延迟上传的文件
    uploadedFileIds = await processDelayedUploads(formData);

    // 批量复制展示编码规则
    let codeRuleData: any = null;
    if (window.campusPlacementIns?.twinObject?.type === 'GroupThing') {
      if (codeRule.value.prefix || codeRule.value.suffix) {
        codeRuleData = codeRule.value;
      }
    }
    if (window.campusPlacementIns?.twinObject.userData.dataType === 'POINT') {
      formData = { ...formData, _modelStyleImg: modelImgConfig.img };
    }
    // 获取参数
    const param = getSaveParam(formData, codeRuleData);
    console.log('保存数据', param);
    // 请求保存接口
    saveTwinBodyPointInfo(param)
      .then(async (res) => {
        // 重置各项数据-资产摆点重置
        if (sceneStore.currentCheckFunc?.code === 'assetPut') {
          reset();
        }
        if (!(window.campusPlacementIns?.twinObject?.type === 'Thing' || window.campusPlacementIns?.twinObject?.type === 'Entity')) {
          reset();
        }
        updateFlag.value = false;
        if (res.code === 200) {
          if (!(window.campusPlacementIns?.twinObject?.type === 'Thing' || window.campusPlacementIns?.twinObject?.type === 'Entity')) {
            useGlobalMessage('success', '点位保存成功！');
          } else {
            useGlobalMessage('success', '点位保存成功, 移动拖拽后可继续保存！');
          }
          // 移动当前摆点点位
          movePlacePoint();
          // 通知资产管理摆点更新列表
          emmiter.emit('updateTable');
          // 更新点位数据
          window.campusTwinsManagerIns.updateTwinData(sceneStore.toolsFunc.onlyMyData, () => {
            const uuids = res.data.map((item: any) => item.uuid);
            if (uuids.length) {
              // 重新加载当前层级同步点位
              window.campusTwinsManagerIns.loadModelByUuid({
                uuids: uuids,
              });
            }
          });
        } else {
          useGlobalMessage('error', res.message);
          // 表单提交失败，回滚已上传的文件
          if (uploadedFileIds.length > 0) {
            await rollbackUploadedFiles(uploadedFileIds);
          }
        }
      })
      .catch(async (error) => {
        useGlobalMessage('error', '保存失败');
        // 表单提交失败，回滚已上传的文件
        if (uploadedFileIds.length > 0) {
          await rollbackUploadedFiles(uploadedFileIds);
        }
      });
  } catch (error) {
    console.error('文件上传失败:', error);
    useGlobalMessage('error', '文件上传失败，请重试');
    // 文件上传失败，不继续提交表单
  }
};
// 移动当前摆点点位
const movePlacePoint = () => {
  if (!(window.campusPlacementIns?.twinObject?.type === 'Thing' || window.campusPlacementIns?.twinObject?.type === 'Entity')) return;
  const object = window.campusPlacementIns?.twinObject;
  if (object) {
    const x = object.boundingBox.size[0] || 0;
    object.position = [object.position[0] + x, object.position[1], object.position[2]];
  }
};
// 重置新增点位各项数据
const reset = () => {
  // 重置摆点控制器
  if (window.campusPlacementIns) {
    window.campusPlacementIns.reset(true, true);
  }
  sceneStore.placeType = '';
  sceneStore.activeTwin = null;
};

// 字段属性
// 表单定义
const formDatas = ref(null);
// 表单数据
const targetData = ref(null);
// 更新key
const updataKey = ref(100);
// dom
const myDesignFormRef = ref();
// 当前主题
const theme = computed(() => settingStore.modeName);
// 格式化处理
const numberFormat = (value: any) => `${Number(value)}`.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
const numberParse = (value: any) => value.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
// 设置
// 输入框值改变
const changeVal = (val: number | string, type: string) => {
  updateTwinObejct(val, type);
};
// 颜色改变
const changeColor = (value: string | string[], type: string) => {
  updateTwinObejct(value, type);
};
/**
 * 更新三维孪生对象
 * val 值
 * type 更新类型 scale angles offsetHeight等
 */
const updateTwinObejct = _.debounce((val: number | string, type: string) => {
  if (val === null) {
    return;
  }
  switch (type) {
    case 'scale': // 缩放
      if (scaleModel.value === 1) {
        twinProp.value.scale = [twinProp.value.scale[0], twinProp.value.scale[0], twinProp.value.scale[0]];
      }
      const paramScale = { type, data: twinProp.value.scale };
      // 更新三维
      window.campusPlacementIns.updateThing(paramScale);
      break;
    case 'angles': // 旋转角度
      const paramAngles = { type, data: twinProp.value.angles };
      // 更新三维
      window.campusPlacementIns.updateThing(paramAngles);
      break;
    case 'offsetHeight': // 离地高度
      const paramHeight = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(paramHeight);
      break;
    case 'lineColor': // 线段颜色
      const lineColor = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(lineColor);
      break;
    case 'lineType': // 线段类型
      const lineType = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(lineType);
      break;
    case 'lineWidth': // 线段宽度
      const lineWidth = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(lineWidth);
      break;
    case 'regionColor': // 面区域颜色
      const regionColor = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(regionColor);
      break;
    case 'regionLineColor': // 面边框颜色
      const regionLineColor = { type, data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(regionLineColor);
      break;
    case 'modelImg': //模型贴图
      const modelPar = { type: 'modelImg', data: val };
      // 更新三维
      window.campusPlacementIns.updateThing(modelPar);
      break;
    default:
      break;
  }
}, 500);
// 选中tab-默认设置
const activeTab = ref(2);
// 切换tab
const toggleTab = (val: number) => {
  activeTab.value = val;
  if (activeTab.value === 1 && !observer) {
    nextTick(() => {
      observer = new ResizeObserver(handleResize);
      const ttpt = document.getElementById('ttpt');
      if (ttpt) {
        observer.observe(ttpt);
      }
    });
  }
};
// 当前摆点孪生体属性值
const twinProp = computed(() => {
  return sceneStore.twinProp;
});
// 当前摆点孪生体
const activeTwin = computed(() => sceneStore.activeTwin);
// 批量复制展示编码规则
const codeRule = ref({
  show: false,
  prefix: '',
  suffix: '',
  begin: 0,
  step: 1,
});
// 处理数据
const disposeInitData = (form: any, formData: any) => {
  if (JSON.stringify(formData) === '{}' || !formData) {
    formDatas.value = form;
  } else {
    const labelValue = ['input', 'number', 'textarea', 'radio', 'date', 'editor'];
    form.list.forEach((item: any) => {
      if (labelValue.includes(item.type)) {
        if (formData[item.model] !== undefined) {
          item.options.defaultValue = formData[item.model];
        }
      }
      if (item.type === 'number') {
        item.options.defaultValue = formData[item.model] !== '' ? formData[item.model] : 0;
      }
      if (item.type === 'checkbox') {
        try {
          item.options.defaultValue = formData[item.model] ? JSON.parse(formData[item.model]) : [];
        } catch (error) {
          item.options.defaultValue = [];
        }
      }
      if (item.type === 'select') {
        if (item.options.multiple) {
          try {
            item.options.defaultValue = formData[item.model] ? JSON.parse(formData[item.model]) : [];
          } catch (error) {
            item.options.defaultValue = [];
          }
        } else {
          item.options.defaultValue = formData[item.model];
        }
      }
      if (item.type === 'uploadFile') {
        try {
          item.options.defaultValue = formData[item.model] ? JSON.parse(formData[item.model]) : [];
        } catch (error) {
          item.options.defaultValue = [];
        }
      }
      if (item.type === 'releative') {
        try {
          item.options.defaultValue = formData[item.model] ? JSON.parse(formData[item.model]) : [];
        } catch (error) {
          item.options.defaultValue = [];
        }
      }
    });
    formDatas.value = form;
  }
};
// 监听摆点初始化数据
watch(
  () => sceneStore.activeTwin,
  (val, oldVal) => {
    if (!_.isEmpty(val)) {
      // 自定义属性
      nextTick(() => {
        const form = JSON.parse(val.form);
        // 批量复制-孪生体编码置空并不可编辑
        if (window.campusPlacementIns.twinObject.type === 'GroupThing') {
          codeRule.value = {
            show: true,
            prefix: '',
            suffix: '',
            begin: 0,
            step: 1,
          };
          const unique = form.list.find((item: any) => item.model === 'UniqueCode');
          if (unique) {
            unique.options.disabled = true;
            if (val.formData) {
              val.formData.UniqueCode = '';
            }
          }
        }
        disposeInitData(form, val.formData);
        // formDatas.value = form;
        targetData.value = val.formData || null;
        const bodyData = val?.position_body;
        if (bodyData) {
          const jsonBodyData = JSON.parse(bodyData);
          modelImgConfig.img = jsonBodyData?.styleImg || '';
          if (jsonBodyData.position && jsonBodyData.target) {
            currentPos.value = jsonBodyData.position;
            currentTarget.value = jsonBodyData.target;
          }
        }
        updataKey.value++;
      });
    }
  },
  { immediate: true }
);
watch(
  () => sceneStore.twinProp,
  () => {
    updateFlag.value = true;
  },
  {
    deep: true,
  }
);
// esc事件监听
const escEvent = (e: any) => {
  if (e.keyCode === 27) {
    reset();
  }
};
let dragMove: any = null;

let observer: ResizeObserver | null = null;
onMounted(() => {
  // 初始化拖拽
  const moveBox = document.querySelector('#place-twin-modal');
  const dragBox = document.querySelector('#add-twin-head');
  const limitBox = document.body;
  dragMove = new DragMove(dragBox, limitBox, moveBox, 1);
  document.addEventListener('keyup', escEvent);
  document.addEventListener('click', hideSelectModal);
});
// 关闭选择模型和气泡弹框
const hideSelectModal = (e: any) => {
  const path = e.path || (e.composedPath && e.composedPath());
  const result = path.find((item: any) => item.dataset && item.dataset.type === 'selectTree');
  if (result) {
    return;
  }
  bubbleVisible.value = false;
};
const ttSize = ref({
  width: 0,
  top: 0,
});
const handleResize = (entries: any) => {
  for (const entry of entries) {
    // const { width, height } = entry.contentRect;
    const st = entry.target.getClientRects()[0];
    if (st) {
      const { width, top } = st;
      console.log(`宽度：${width},top${top}px`);
      ttSize.value.width = width;
      ttSize.value.top = top + 40;
      // 这里可以执行针对宽高变化的操作
    }
  }
};
onBeforeUnmount(() => {
  document.removeEventListener('keyup', escEvent);
  if (dragMove) {
    dragMove.destroy();
  }
  // 在组件销毁前取消观察
  if (observer) {
    observer.disconnect();
  }
  document.removeEventListener('click', hideSelectModal);
});
//#region 模型贴图
const modelImgConfig = reactive({
  img: '',
  bubbleInfoId: '',
  name: '',
});
// 选择气泡
const bubbleVisible = ref(false);
const handleModelImg = () => {
  bubbleVisible.value = true;
};
const checkBubble = (item: any) => {
  modelImgConfig.bubbleInfoId = item.id;
  modelImgConfig.name = item.name;
  modelImgConfig.img = item.imgUrl;
  bubbleVisible.value = false;
  changeVal(modelImgConfig.img, 'modelImg');
};
//#endregion
</script>
<style scoped lang="scss">
.add-twin.keep-px {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  // max-height: 520px;
  overflow: hidden;
  background: var(--primary-bg-color);

  .line-twin-form {
    margin-bottom: 18px;
  }

  .add-twin-top {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    min-height: 48px;
    padding: 0 10px;
    overflow: hidden;
    cursor: move;
    background: var(--primary-bg-color);
    border-bottom: 1px solid var(--header-border-color);

    .func-icon {
      font-size: 16px;
    }

    .title {
      display: flex;
      flex: 1;
      align-items: center;
      width: 100%;
      overflow: hidden;
      font-family: 'PingFang Bold';
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-text-color);
      white-space: nowrap;

      .name {
        display: inline-block;
        overflow: hidden;
        font-family: 'PingFang Medium';
        font-weight: 400;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .btn-wrap {
      width: 76px;
      min-width: 76px;
      margin-left: 5px;

      .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        margin-right: 6px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .add-twin-bottom {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 48px);
    overflow: hidden;

    .tab-wrap {
      display: flex;
      width: 100%;
      height: 42px;

      .tab-item {
        position: relative;
        width: 100%;
        height: 42px;
        font-family: 'PingFang Bold';
        font-size: 14px;
        font-weight: 600;
        line-height: 42px;
        color: var(--primary-text-color);
        text-align: center;
        cursor: pointer;
        border-bottom: 1px solid var(--header-border-color);

        &:hover {
          color: var(--theme-color);
        }

        &.active {
          color: var(--theme-color);
          border-right: 1px solid var(--header-border-color);
          border-bottom: none;
          border-left: 1px solid var(--header-border-color);

          &:first-child {
            border-left: none;
          }

          &:last-child {
            border-right: none;
          }

          &::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            content: '';
            background: var(--theme-color);
          }
        }
      }
    }

    .tab-content {
      width: 100%;
      max-height: calc(100% - 110px);
      // min-height: 230px;
      overflow-y: auto;

      .twin-prop-content {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 14px;
        overflow-y: auto;

        .twin-prop-form {
          width: 100%;
        }
      }

      .twin-custom-prop {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 24px 16px;
        overflow-y: auto;
      }
    }

    .tab-btn {
      display: flex;
      justify-content: center;
      margin: 14px 0;
      text-align: center;

      .save-btn-wrap {
        position: relative;
        margin-right: 10px;
      }

      .cancel-btn {
        margin-right: 10px;
      }

      .notice-icon {
        position: absolute;
        top: -7px;
        right: -5px;
        width: 13px;
        height: 13px;
        font-size: 26px;
        line-height: 27px;
        color: red;
        text-align: center;
        background-color: #fff;
        border-radius: 50%;
      }
    }
  }
}

.tt-sel {
  position: absolute;
  top: var(--top);
  right: 10px;
  z-index: 999;
  width: var(--width);
  min-width: var(--minWidth);
}
</style>
