package com.uino.x.pedestal.twin.jrm.core.enums;

import apijson.RequestMethod;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 请求类型,兼容apijson{@link RequestMethod}
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/23 10:06
 */
@AllArgsConstructor
@Getter
public enum RequestType {

    /**
     * jrm 请求
     */
    ALTER(null),
    SELECT(null),
    CREATE(null),
    DROP(null),
    TRUNCATE(null),

    /**
     * apijson请求
     */
    GET(RequestMethod.GET),
    HEAD(RequestMethod.HEAD),
    GETS(RequestMethod.GETS),
    HEADS(RequestMethod.HEADS),
    POST(RequestMethod.POST),
    PUT(RequestMethod.PUT),
    DELETE(RequestMethod.DELETE);

    private final RequestMethod apiJsonRequest;

    /**
     * 判断是否是apijson 请求
     *
     * @return 是否是apijson 请求
     * <AUTHOR>
     * @date 2021/4/23 10:06
     */
    public boolean isApiJson() {

        return Objects.nonNull(apiJsonRequest);
    }
}
