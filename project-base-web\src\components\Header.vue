<template>
  <div class="header">
    <div class="logo">
      <img :src="systemLogo" class="header-logo" alt="header-logo" @error="(event: any) => (event.target.src = tiImg)" /><span class="plat-name">{{ systemName }}</span>
    </div>
    <div class="content-wrapper">
      <div class="content">
        <div v-for="item in apps" :key="item.code" class="content-item" :class="item.code === activeApp?.code ? 'active' : ''" @click="handleClick(item)">
          <span class="name" :class="item.code === activeApp?.code ? 'active' : ''">{{ item.name }}</span>
        </div>
      </div>
    </div>
    <div class="user">
      <div class="project-name">{{ projectName }}</div>
      <notice-icon />
      <!-- <img ref="imgRef" src="@/assets/img/default/avatar.png" alt="头像" @click="handleAvatar" /> -->
      <div ref="imgRef" class="user-name" @click="handleAvatar">
        {{ userInfo?.name }}
      </div>
      <div v-show="toggleDown" ref="userRef" class="user-info keep-px">
        <div class="header-item" @click="goPerson">
          <user-outlined class="handel-icon" />
          <span class="name">个人中心</span>
        </div>
        <div class="header-item" @click="clickLogout">
          <logout-outlined class="handel-icon" />
          <span class="name">退出登录</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { LogoutOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import NoticeIcon from '@/components/NoticeIcon.vue';
import { logout } from '@/api/login/index';
import { usePermissionStore } from '@/store/permission';
import tiImg from '@/assets/img/header/ti.png';
import axios from 'axios';
import { generateRequestId } from '@/utils/util';
import { changeAppMenu, deleteLoginInfo } from '@/hooks/usePermission';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { useUserStore } from '@/store/user';
import { useSettingStore } from '@/store/setting';
const permissionStore = usePermissionStore();
const settingStore = useSettingStore();
const { userInfo } = useUserStore();
const tenant = axios.defaults.headers.common['Tenant'];
// 系统标题
const systemName = ref(sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) || '');
if (sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) === null) {
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=XI_TONG_BIAO_TI`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      const title = res.data?.data !== '无' ? res.data?.data : '';
      sessionStorage.setItem(`${tenant}_XI_TONG_BIAO_TI`, title);
      systemName.value = title;
      // 项目名称
      const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
      if (projectName.value) {
        document.title = `${systemName.value} - ${projectName.value}`;
      } else {
        document.title = `${systemName.value}`;
      }
    });
}
// 系统logo
const systemLogo = ref(sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`));
if (sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`) === null) {
  axios
    .get(`${window.baseConfig.appApi}/systemx/sys-config/open?code=DENG_LU_YE_LOGO`, {
      headers: {
        'X-Security-FreshToken': generateRequestId(), // 设置自定义请求头
        Tenant: tenant,
      },
    })
    .then((res) => {
      const logo = res.data?.data !== '无' ? res.data?.data : '';
      sessionStorage.setItem(`${tenant}_XI_TONG_LOGO`, logo);
      systemLogo.value = logo || tiImg;
      // 修改link-icon
      let favicon = document.querySelector('link[rel="icon"]');
      if (favicon) {
        // @ts-ignore
        favicon.href = systemLogo.value;
      }
    });
}
// 修改link-icon
let favicon = document.querySelector('link[rel="icon"]');
if (favicon) {
  // @ts-ignore
  favicon.href = systemLogo.value;
}
// 项目名称
const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
if (projectName.value) {
  document.title = `${systemName.value} - ${projectName.value}`;
} else {
  document.title = `${systemName.value}`;
}
const router = useRouter();
// 用户功能模块
const apps = computed(() => {
  return permissionStore.userApps.filter((app) => app.visible && app.code !== 'XXV');
});
// 当前选中的功能模块
const activeApp = computed(() => permissionStore.activeApp);
// 用户头像点击下拉
const toggleDown = ref(false);
// 点击菜单-切换功能菜单
const handleClick = (item: any) => {
  const tenant: any = axios.defaults.headers.common['Tenant'];
  // 以选中返回
  if (activeApp.value?.code === item.code) return;
  // 切换功能菜单
  changeAppMenu(item, tenant);
};

// 跳转个人中心
const goPerson = () => {
  const tenant = axios.defaults.headers.common['Tenant'];
  toggleDown.value = false;
  permissionStore.updateActiveApp({});
  permissionStore.updateActiveSecondMenu({});
  permissionStore.updateActiveThirdMenu({});
  permissionStore.thirdMenus = [];
  permissionStore.secondMenus = [];
  router.push(`/${tenant}/pedestal/personal`);
};
// 退出登录
const clickLogout = () => {
  toggleDown.value = false;
  const tenant: any = axios.defaults.headers.common['Tenant'];
  // 退出
  logout().then((res: any) => {
    if (res.code === 200) {
      const projectName = sessionStorage.getItem('PROJECT_NAME');
      if (projectName === 'master') {
        // 设置更新工作台更新主租户token
        localStorage.setItem('update_main', '1');
      }
      deleteLoginInfo(tenant).then(() => {
        router.push(`/${tenant}/login`);
      });
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
const handleAvatar = () => {
  toggleDown.value = !toggleDown.value;
};

// 关闭用户弹框
const userRef = ref();
const imgRef = ref();
const closeUser = () => {
  document.addEventListener('click', (e) => {
    if (imgRef.value && !imgRef.value.contains(e.target)) {
      if (userRef.value && !userRef.value.contains(e.target)) {
        toggleDown.value = false;
      }
    }
  });
};
onMounted(() => {
  closeUser();
});
watch(
  () => settingStore.updateSetting,
  (newVal) => {
    if (newVal === 1) {
      systemName.value = sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) || '';
      systemLogo.value = sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`) || tiImg;
      settingStore.setUpdateSetting(0);
    }
  }
);
</script>

<style scoped lang="scss">
.header {
  z-index: 2;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  min-height: 50px;
  font-size: 14px;
  background: var(--primary-bg-color);
  box-shadow:
    0 1px 0 0 var(--header-border-color),
    0 1px 5px 0 var(--header-border-color);

  .logo {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 145px;
    min-width: 145px;
    height: 100%;
    overflow: hidden;

    .header-logo {
      width: auto;
      height: 23px;
      min-height: 23px;
      margin-right: 8px;
    }

    .plat-name {
      overflow: hidden;
      font-family: DINAlternate-Bold;
      font-size: 20px;
      font-weight: 700;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .content-wrapper {
    flex: 1;
    height: 100%;
    padding: 0 66px 0 10px;
    color: var(--header-text-color);

    .content {
      display: flex;
      align-items: center;
      height: 100%;
      margin: 0 auto;

      .content-item {
        position: relative;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        height: 100%;
        margin-right: 36px;
        cursor: pointer;

        &:hover {
          .name {
            color: var(--header-text-active-color);
          }
        }

        &.active::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 3px;
          max-height: 3px;
          content: '';
          background: var(--theme-color);
        }

        .name {
          &.active {
            color: var(--header-text-active-color);
          }
        }
      }
    }
  }

  .user {
    position: relative;
    // width: 34px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    height: 34px;
    margin-right: 24px;
    margin-left: 108px;

    .company-name {
      display: flex;
      align-items: center;
      cursor: pointer;

      .check-company {
        margin-left: 2px;
        font-size: 16px;
      }
    }

    img {
      width: 34px;
      height: 34px;
      margin-left: 24px;
      cursor: pointer;
      border-radius: 50%;
    }

    .project-name {
      margin-right: 14px;
    }

    .user-name {
      margin-left: 10px;
      cursor: pointer;
    }

    .user-info.keep-px {
      position: absolute;
      top: 48px;
      right: 0;
      z-index: 999999;
      width: 109px;
      height: 70px;
      padding: 3px 0;
      color: var(--header-text-color);
      background: var(--primary-bg-color);
      border-radius: 6px;
      box-shadow: 0 0 8px 0 #121315;

      .user-name {
        display: flex;
        align-items: center;
        height: 32px;
        overflow: hidden;
        cursor: pointer;
        border-bottom: 1px solid var(--primary-bg-color);

        .img {
          width: 17px;
          height: 17px;
          margin-right: 16px;
          margin-left: 12px;
          border-radius: 50%;
        }
      }

      .header-item {
        display: flex;
        align-items: center;
        height: 32px;
        overflow: hidden;
        cursor: pointer;

        &:hover {
          color: var(--header-text-active-color);
          background: var(--table-active-bg-color);
          border-radius: 4px;
        }

        .handel-icon {
          width: 12px;
          height: 12px;
          margin-right: 6px;
          margin-left: 14px;
        }
      }
    }
  }
}
</style>
