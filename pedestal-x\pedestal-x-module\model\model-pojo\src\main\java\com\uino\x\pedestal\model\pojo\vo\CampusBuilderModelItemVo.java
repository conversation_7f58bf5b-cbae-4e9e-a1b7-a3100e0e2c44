package com.uino.x.pedestal.model.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 森园区模型项视图对象
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "森园区基础模型项信息")
public class CampusBuilderModelItemVo implements Serializable {

    @Schema(description = "模型唯一标识ID", example = "basic_model_001", required = true)
    private String id;

    @Schema(description = "模型显示名称", example = "标准办公桌", required = true)
    private String title;

    @Schema(description = "模型类型", example = "Furniture", required = true)
    private String type;

    @Schema(description = "模型版本号", example = "2.1.0")
    private String version;

    @Schema(description = "模型尺寸信息", example = "120x60x75")
    private List<Double> size;
}