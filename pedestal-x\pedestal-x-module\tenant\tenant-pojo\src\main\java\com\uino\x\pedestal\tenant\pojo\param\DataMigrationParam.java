package com.uino.x.pedestal.tenant.pojo.param;

import com.uino.x.common.pojo.param.BaseParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据迁移参数
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 9:52 2021/12/2
 */
@Data
@Schema(description = "数据迁移")
public class DataMigrationParam {

    /**
     * 租户code
     */
    @NotNull(message = "租户code不能为空", groups = {BaseParam.export.class, BaseParam.add.class})
    @Schema(description = "租户code")
    private String code;

    /**
     * 文件流和文件路径 不能同时为空
     */
    @Schema(description = "文件")
    private MultipartFile file;

    /**
     * 文件流和文件路径 不能同时为空
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 是否导出业务数据
     */
    @NotNull(message = "是否导出业务数据不能为空", groups = {BaseParam.export.class})
    @Schema(description = "是否导出业务数据")
    private Boolean businessData;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String tag;

    /**
     * 压缩包存放路径（工作台提供）
     */
    @Schema(description = "压缩包存放路径（工作台提供）")
    private String packagePath;


    /**
     * 操作说明桶与路径（工作台提供）
     */
    @Schema(description = "压缩包存放路径（工作台提供）")
    private Map<String, String> edtapMap;


    /**
     * 迁移服务列表
     */
    @Schema(description = "迁移服务列表")
    private List<String> migrationServerList;

    /**
     * 要迁移的文件列表
     */
    @Schema(description = "迁移文件列表")
    private Set<String> migrationFileList;

    /**
     * 下载完成后提示的消息
     */
    @Schema(description = "回调消息")
    private Map<String,Object> callBackMessage;
}
