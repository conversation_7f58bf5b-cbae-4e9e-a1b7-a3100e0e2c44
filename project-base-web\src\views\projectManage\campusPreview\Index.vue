<!--
 * @Description: 三维场景-场景点位
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-03 17:29:30
 * @LastEditors: lcm
 * @LastEditTime: 2025-08-26 17:24:30
-->
<template>
  <div class="campus-preview keep-px">
    <Header :name="sceneName" @current-func="currenFuncChange" />
    <div class="preview-content">
      <div class="preview-content-3d">
        <div id="div3d"></div>
      </div>
      <div id="campus-preview-content-panel" class="preview-content-panel">
        <div class="left-content">
          <div
            class="left-tree"
            :style="{
              width: leftContentShow ? '280px' : '0px',
              opacity: leftContentShow ? '1' : '0',
            }"
          >
            <a-spin :spinning="treeLoading">
              <div v-if="sceneData" class="tree-content">
                <a-tree
                  ref="sceneTree"
                  v-model:selectedKeys="sceneStore.currentLevelUuid"
                  v-model:expandedKeys="sceneStore.expandedKeys"
                  :block-node="true"
                  :tree-data="sceneData.children"
                  class="preview-tree"
                  :default-expand-all="false"
                  :field-names="replaceFields"
                  @select="handleClickTree"
                >
                  <template #title="item: any">
                    <div class="root-tree-item" :title="item.settingName || item.name + '（' + item.userid + '）'">
                      <span class="title">{{ item.settingName || item.name }}（{{ item.userid }}）</span>
                      <div class="btns">
                        <template v-if="item.dataType === 'buildings'">
                          <span class="btn" title="保存外立面视角" :class="{ active: item.facadesCamInfo }" @click.stop="saveCameraView(item, 1)">
                            <IconFont type="icon-facade" title="保存外立面视角" />
                          </span>
                          <span class="btn" title="进入建筑" @click.stop="clickIntoBuilding(item)"><IconFont type="icon-level-insert" title="进入建筑" /></span>
                        </template>
                        <span v-if="item.uuid === sceneStore.currentLevelUuid[0]" class="btn" title="属性设置" @click.stop="settingParam(item)">
                          <IconFont title="属性设置" type="icon-edit1" />
                        </span>
                      </div>
                    </div>
                  </template>
                </a-tree>
              </div>
            </a-spin>
          </div>
          <div class="left-content-expand" @click="changeLeftContentShow">
            <div class="left-content-hand" :class="{ hide: !leftContentShow }"></div>
          </div>
        </div>
        <div class="center-content">
          <div class="center-top">
            <div class="tools-wrap">
              <div class="tool-item">
                <a-popconfirm
                  :open="showRoomPop"
                  placement="left"
                  title="若房间过多,可能会引起场景卡顿,确认显示房间名称？"
                  ok-text="确认"
                  cancel-text="取消"
                  @open-change="handleVisibleChange"
                  @confirm="changeShowRoom"
                >
                  <a-checkbox v-model:checked="toolsFunc.showRoomName" class="tool-check">显示房间名称</a-checkbox>
                </a-popconfirm>
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="toolsFunc.hoverShowAttr" class="tool-check">悬浮展示孪生体属性</a-checkbox>
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="toolsFunc.onlyMyData" :disabled="currentCheckFunc?.code === 'cadPoint' || currentCheckFunc?.code === 'pointCal'" class="tool-check">
                  只展示我的数据</a-checkbox
                >
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="toolsFunc.hideThing" :disabled="currentCheckFunc?.code === 'cadPoint' || currentCheckFunc?.code === 'pointCal'" class="tool-check"> 隐藏物体</a-checkbox>
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="toolsFunc.hideMarker" :disabled="currentCheckFunc?.code === 'cadPoint' || currentCheckFunc?.code === 'pointCal'" class="tool-check"> 隐藏图标</a-checkbox>
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="toolsFunc.keepSize" :disabled="currentCheckFunc?.code === 'cadPoint' || currentCheckFunc?.code === 'pointCal'" class="tool-check">
                  图标保持大小</a-checkbox
                >
              </div>
              <div class="tool-item">
                <a-checkbox v-model:checked="closeEffect" class="tool-check" @change="toggleEffect">关闭后期</a-checkbox>
              </div>
            </div>
            <div class="func-btns">
              <!-- <a-button class="btn" @click="saveCameraView">
                                <template #icon><IconFont class="func-icon" type="icon-save"></IconFont></template>保存当前视角</a-button
                            > -->
              <a-button class="btn" type="primary" @click="changeEffectPanel">效果编辑</a-button>
            </div>
          </div>
          <div class="center-bottom">
            <div class="performance-wrap">
              <!-- 性能分析 -->
              <PerformanceAnalysis v-if="sceneStore.sceneLoaded" @refresh-tree="refreshTree" />
            </div>
            <!-- 2d/3d 转换 -->
            <div class="view-modes">
              <div v-for="item in viewModes" :key="item" class="view-mode" :class="{ active: currentViewMode === item }" @click="viewModeChange(item)">
                {{ item }}
              </div>
            </div>
            <!-- 子场景位置矫正 -->
            <div class="child-Scence" v-if="ifChildSence" title="子场景矫正位置" @click="clickPosCorrent">
              <IconFont class="icon" type="icon-location" :class="{ active: startChildPos }" />
              <!-- <div class="icon iconfont icon-icn-jiaozhengweizhi"></div> -->
            </div>
            <div class="prewbtns" id="prewbtns">
              <FullscreenOutlined class="icon" v-if="!fullscreen" @click="fullScreenFn(true)" title="全屏" />
              <FullscreenExitOutlined class="icon" v-else @click="fullScreenFn(false)" title="退出全屏" />
            </div>
            <div v-show="batchCopyFlag" class="handle-notice"><info-circle-outlined /> 按住 <span class="key-code">Shift</span> + 鼠标滚轮滚动可控制批量复制间距。</div>
            <div v-show="sceneStore.currentPointCalStep === 2" class="handle-notice">
              <info-circle-outlined /> 通过 <span class="key-code">W</span> <span class="key-code">S</span> <span class="key-code">A</span> <span class="key-code">D</span>
              <span class="key-code">Q</span> <span class="key-code">E</span> 按键，可对目标点位进行微调。
            </div>
            <div v-show="currentCheckFunc?.code === 'twinPut' && !batchCopyFlag" class="handle-notice">
              <info-circle-outlined /> 按住 <span class="key-code">Ctrl</span> / <span class="key-code">Alt</span> +鼠标左键 可加/减选孪生体, 按住
              <span class="key-code">Shift</span> 可框选孪生体批量操作。
            </div>
          </div>
        </div>
        <div class="right-content">
          <div
            id="campus-right-content-wrap"
            class="right-content-wrap"
            :style="{
              width: rightContentShow ? rightWidth + 'px' : '0px',
              opacity: rightContentShow ? '1' : '0',
            }"
          >
            <!-- 顶部header功能以及效果列表 -->
            <component :is="currentCheckFunc.component" v-show="!currentTwinCom && !showEffectPanel" v-if="currentCheckFunc" class="right-com" />
            <!-- 效果编辑 -->
            <SetEffect v-show="!currentTwinCom && showEffectPanel" v-if="sceneStore.sceneLoaded" :closeEffect="closeEffect" @close="showEffectPanel = false" />
            <childScencePosPanel v-if="!currentTwinCom && !showEffectPanel && sceneStore.sceneLoaded" ref="childScencePosPanelRef" @back="childScencePosPanelBack" />
            <!-- 摆点 -->
            <div v-if="currentTwinCom" id="place-twin-modal" class="place-twin-modal">
              <component :is="currentTwinCom.component" v-show="placeType" @default-twin-cb="defaultTwinCb" />
            </div>
          </div>
          <div class="right-content-expand" @click="changeRightContentShow">
            <div class="right-content-hand" :class="{ hide: !rightContentShow }"></div>
          </div>
          <div id="campus-drag-bar" class="drag-bar"></div>
        </div>
      </div>
    </div>
    <!-- <div v-if="currentTwinCom" id="place-twin-modal" class="place-twin-modal">
      <component :is="currentTwinCom.component" v-show="placeType" @default-twin-cb="defaultTwinCb" />
    </div> -->
    <Loading v-show="loading" :loading-percent="loadingPercent" :childLoading="childLoading" />
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { InfoCircleOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import { ref, reactive, onMounted, nextTick, computed, markRaw, watch, createVNode, render } from 'vue';
import BuildingMarker from '@/components/BuildingMarker.vue';
import { getSceneMsg, saveCameraConfig } from '@/api/business/scene';
import { initApp } from '@/utils/scene/index';
import { CampusManager } from '@/utils/scene/index';
import { loadJsFiles, setFlattenData } from '@/utils/util';
import { useSceneStore } from '@/store/scene';
import { CampusPlacement, CreatePointTwin, CreateLineTwin, CreateRegionTwin, CopyTwin, GroupTwin, LineSurfaceTransform, ScencePosCorrect } from '@/utils/campusPlacement/index';
import AddTwin from './twinProp/AddTwin.vue';
import EditTwin from './twinProp/EditTwin.vue';
import DefaultTwin from './twinProp/DefaultTwin.vue';
import PerformanceAnalysis from './components/PerformanceAnalysis.vue';
import GroupTwinProp from './twinProp/GroupTwin.vue';
import GroupAddTwin from './twinProp/GroupAddTwin.vue';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { showFloorName, hideFloorName } from '@/utils/scene/index';
import Header from './Header.vue';
import Loading from './Loading.vue';
import SetEffect from './components/SetEffect.vue'; // 设置效果包
import childScencePosPanel from './components/childScencePosPanel.vue'; // 子场景位置矫正
const sceneStore = useSceneStore();

const closeEffect = ref(false);
let oldEffect: any = null;
// 切换后期
const toggleEffect = () => {
  // thingjs1.0
  if (sceneStore.thingjsVersion === 1) {
    if (closeEffect.value) {
      // 关闭后期
      window.app.postEffect = { postEffect: { enable: false } };
    } else {
      // 打开后期
      window.app.postEffect = oldEffect;
    }
  } else {
    // thingjs2.0
    if (closeEffect.value) {
      // 关闭后期
      window.app.camera.postEffect.enable = false;
    } else {
      // 打开后期
      window.app.camera.postEffect.enable = true;
    }
  }
};
// 控制显示房间名称pop
const showRoomPop = ref(false);
const handleVisibleChange = (bool: boolean) => {
  if (!bool) {
    showRoomPop.value = false;
    return;
  }
  if (toolsFunc.value.showRoomName) {
    changeShowRoom(); // next step
  } else {
    showRoomPop.value = true;
  }
};
// 房间名称显示切换
const changeShowRoom = () => {
  toolsFunc.value.showRoomName = !toolsFunc.value.showRoomName;
  if (toolsFunc.value.showRoomName) {
    // 显示房间名称
    if (window.app.level.current.type === 'Floor') {
      showFloorName(window.campusManagerIns?.currentSceneUuid, window.app.level.current);
    }
  } else {
    // 隐藏房间名称
    hideFloorName();
  }
};
// 默认孪生体保存成功后返回
const defaultTwinCb = () => {
  // 刷新左侧树数据
  refreshTree();
};
// 点击保存视角
const saveCameraView = (item: any, type = 0) => {
  let currentLevelData = item;
  if (type === 0) {
    const levelDatas = setFlattenData(sceneData.value.children);
    const id = window.app.level.current.id;
    const currentType = window.app.level.current.type;
    if (currentType === 'Campus') {
      currentLevelData = levelDatas.find((item: any) => item.name === '室外');
    } else {
      currentLevelData = levelDatas.find((item: any) => {
        return `${item.userid}` === `${id}`;
      });
    }
  }
  const param = {
    camType: type,
    mainUuid: currentLevelData.parentSceneUUID,
    configCamInfo: JSON.stringify({
      eye: window.app.camera.position,
      target: window.app.camera.target,
      distance: window.app.camera.distance,
    }),
    childUuid: currentLevelData.uuid,
  };
  saveCameraConfig(param).then((res: any) => {
    if (res.code === 200) {
      currentLevelData.facadesCamInfo = param.configCamInfo;
      useGlobalMessage('success', '视角保存成功！');
      // 刷新左侧树数据
      refreshTree();
      if (type === 0) {
        // 更新场景数据
        window.campusManagerIns.updateCampusData();
      }
    }
  });
};
// 点击进入建筑
const clickIntoBuilding = (build: any) => {
  const { uuid } = build;
  const buildingInfo = sceneData.value.children.filter((item: any) => item.uuid === uuid && item.dataType === 'buildings')[0];
  if (!buildingInfo) {
    return;
  }
  const building = window.app.query(`#${buildingInfo.userid}`)[0];
  if (building) {
    window.app.level.change(building);
  }
};
// 孪生体摆点组件列表
const twinComs = [
  {
    code: 'new',
    name: '新增孪生体',
    component: markRaw(AddTwin),
  },
  {
    code: 'edit',
    name: '编辑孪生体',
    component: markRaw(EditTwin),
  },
  {
    code: 'group',
    name: '孪生体打组',
    component: markRaw(GroupTwinProp),
  },
  {
    code: 'groupAdd',
    name: '孪生体批量新增',
    component: markRaw(GroupAddTwin),
  },
  {
    code: 'default',
    name: '默认孪生体',
    component: markRaw(DefaultTwin),
  },
];
// 获取当前正在摆点的类型组件
const currentTwinCom = computed(() => {
  const twin = twinComs.find((item) => {
    if (item.code === sceneStore.placeType) {
      return item;
    }
    return null;
  });
  return twin;
});
// 当前选中的顶部功能模块
const currentCheckFunc = ref();
// 当前选中的顶部功能模块改变
const currenFuncChange = (val: any) => {
  // 还原相关数据
  reset();
  currentCheckFunc.value = val;
  sceneStore.currentCheckFunc = val;
};
// 摆点批量复制标识
const batchCopyFlag = computed(() => sceneStore.batchCopyFlag);
// 当前是否正在摆点
const placeType = computed(() => sceneStore.placeType);
// 场景加载进度
const loadingPercent = ref(0);
// 场景名称
const sceneName = ref();
// 场景uuid
const sceneUuid = ref();
// 场景小功能开关控制
const toolsFunc = computed(() => sceneStore.toolsFunc);
// 场景树数据
const sceneData = ref();
// 替换对应字段
const replaceFields = ref({
  children: 'children',
  title: 'name',
  key: 'uuid',
});
// 点击树节点
const handleClickTree = (key: any, value: any) => {
  const data = value.node.dataRef;
  if (sceneStore.currentLevelUuid[0] === data.uuid) {
    // 处理飞行事件
    disposeClickTreeFly(value.node.dataRef);
    return;
  }
  sceneStore.currentLevelUuid = [data.uuid];
  // 处理飞行事件
  disposeClickTreeFly(value.node.dataRef);
};
// 处理树节点点击飞行以及层级切换事件
const disposeClickTreeFly = (data: any) => {
  const currentLevel = window.app.level.current;
  if (data.dataType === 'outdoors') {
    if (currentLevel.type === 'Campus') {
      const toCamInfo = data.configCamInfo;
      if (toCamInfo) {
        const to = JSON.parse(toCamInfo);
        eyeFly(to);
      } else {
        if (sceneStore.thingjsVersion === 1) {
          window.app.camera.flyTo({
            object: window.app.level.current,
            time: 1000,
          });
        } else {
          window.app.camera.flyTo({
            target: window.app.level.current,
            duration: 1000,
          });
        }
      }
    } else {
      const campus = window.app.query('.Campus')[0];
      if (campus) {
        window.app.level.change(campus);
      }
    }
  } else if (data.dataType === 'buildings') {
    if (currentLevel.type === 'Campus') {
      const buildingByUserId = window.app.level.current.query(`#${data.userid}`)[0];

      if (buildingByUserId) {
        if (data.facadesCamInfo) {
          eyeFly(JSON.parse(data.facadesCamInfo));
        } else {
          if (sceneStore.thingjsVersion === 1) {
            window.app.camera.flyTo({
              object: buildingByUserId,
              time: 1000,
            });
          } else {
            window.app.camera.flyTo({
              target: buildingByUserId,
              duration: 1000,
            });
          }
        }
      }
    } else {
      const campus = window.app.query('.Campus')[0];
      if (campus) {
        window.app.level.change(campus, {
          complete: () => {
            setTimeout(() => {
              const buildingByUserId = window.app.level.current.query(`#${data.userid}`)[0];
              sceneStore.currentLevelUuid = [data.uuid];
              if (buildingByUserId) {
                if (sceneStore.thingjsVersion === 1) {
                  window.app.camera.flyTo({
                    object: buildingByUserId,
                    time: 1000,
                  });
                } else {
                  window.app.camera.flyTo({
                    target: buildingByUserId,
                    duration: 1000,
                  });
                }
              }
            });
          },
        });
      }
    }
  } else if (data.dataType === 'plans') {
    // 当前层
    if (`${currentLevel.id}` === `${data.userid}` && `${currentLevel.parent.id}` === `${data.twinData.parent_user_id}`) {
      const toCamInfo = data.configCamInfo;
      if (toCamInfo) {
        const to = JSON.parse(toCamInfo);
        eyeFly(to);
      } else {
        if (sceneStore.thingjsVersion === 1) {
          window.app.camera.flyTo({
            object: window.app.level.current,
            time: 1000,
          });
        } else {
          window.app.camera.flyTo({
            target: window.app.level.current,
            duration: 1000,
          });
        }
      }
    } else if (currentLevel.type === 'Floor' && `${currentLevel.parent.id}` === `${data.twinData.parent_user_id}`) {
      // 同一栋建筑不同层
      const changeLevelId = currentLevel.parent.query(`#${data.userid}`)[0];
      if (changeLevelId) {
        window.app.level.change(changeLevelId);
      }
    } else {
      const building = window.app.query(`#${data.twinData.parent_user_id}`)[0];
      if (building) {
        // 正在当前建筑
        if (`${currentLevel.id}` === `${building.id}`) {
          const changeLevelId = building.query(`#${data.userid}`)[0];
          if (changeLevelId) {
            window.app.level.change(changeLevelId);
          }
        } else {
          // 不同建筑也不在当前层
          // 监听进入建筑/动态加载建筑完毕
          window.app.on(
            THING.EventType.EnterLevel,
            '.Building',
            () => {
              setTimeout(() => {
                const changeLevelId = building.query(`#${data.userid}`)[0];
                if (changeLevelId) {
                  window.app.level.change(changeLevelId);
                  // 移除监听
                  window.app.off(THING.EventType.EnterLevel, '.Building', 'buildinglevelChange');
                }
              }, 200);
            },
            'buildinglevelChange'
          );
          // 切换建筑层级
          window.app.level.change(building);
        }
      }
    }
  }
};
// 飞到设置视角
const eyeFly = (e: any) => {
  if (e && e.eye && e.target) {
    const positionArr = typeof e.eye === 'string' ? e.eye.replace(' ', ',').split(',') : e.eye;
    const targetArr = typeof e.target === 'string' ? e.target.replace(' ', ',').split(',') : e.target;
    const position: number[] = [];
    const target: number[] = [];
    positionArr.forEach((item: string) => position.push(Number(item)));
    targetArr.forEach((item: string) => target.push(Number(item)));
    if (sceneStore.thingjsVersion === 1) {
      window.app.camera.flyTo({
        position,
        target,
        radius: e.distance,
        time: 1000,
      });
    } else {
      window.app.camera.flyTo({
        position,
        target,
        distance: e.distance,
        duration: 1000,
      });
    }
  }
};
// 点击属性设置
const settingParam = (item: any) => {
  // 初始化
  reset();
  if (item.dataType === 'outdoors') {
    sceneStore.activeDefaultTwin = sceneData.value;
  } else {
    sceneStore.activeDefaultTwin = item.data;
  }
  sceneStore.placeType = 'default';
};
// 统一初始化相关互相影响数据
const reset = () => {
  // 初始化摆点
  window.campusPlacementIns && window.campusPlacementIns.reset(true, true);
  // 删除打组对象
  window.groupTwinIns && window.groupTwinIns.reset();
  sceneStore.placeType = '';
  sceneStore.activeDefaultTwin = null;
  sceneStore.activeTwin = null;
  sceneStore.activeEditTwin = null;
  showEffectPanel.value = false;
};
// 监听左侧树当前选择行改变
watch(
  () => sceneStore.currentLevelUuid,
  (val: string[]) => {
    if (val.length) {
      reset();
      if (sceneStore.sceneLoaded) {
        // 层级切换-2d状态，运用一次2d事件
        useDefaultViewModel();
      }
    }
  }
);
// 正在摆点 别的取消选中效果
watch(
  () => sceneStore.activeTwin,
  (val: any) => {
    if (val) {
      changeRoomAndObjectOutline();
    } else {
      resumeRoomAndObjectOutline();
    }
  }
);
// 设置房间和物体选中没有outline
const changeRoomAndObjectOutline = () => {
  console.log('设置房间和物体选中没有outline');
  if (sceneStore.thingjsVersion === 1) {
    window.app.level.options = { outlineColor: null };
    window.app.pauseEvent(THING.EventType.DBLClick, '*', THING.EventTag.LevelEnterOperation);
  } else {
    const baseObject = window.app.query('.Object3D');
    baseObject.forEach((item: any) => {
      item.level.config.ignoreStyle = true;
    });
  }
};
// 设置房间和物体选中没有outline
const resumeRoomAndObjectOutline = () => {
  console.log('恢复房间和物体选中有outline');
  if (sceneStore.thingjsVersion === 1) {
    window.app.level.options = { outlineColor: '#FFB810' };
    window.app.resumeEvent(THING.EventType.DBLClick, '*', THING.EventTag.LevelEnterOperation);
  } else {
    const baseObject = window.app.query('.Object3D');
    baseObject.forEach((item: any) => {
      if (item.type === 'Marker') return;
      item.level.config.ignoreStyle = false;
    });
  }
};
// 树加载中
const treeLoading = ref(false);
// 场景加载中
const loading = ref(true);
// 左侧content展示控制
const leftContentShow = ref(true);
// 切换左侧内容展示
const changeLeftContentShow = () => {
  leftContentShow.value = !leftContentShow.value;
};
// 右侧content展示控制
const rightContentShow = ref(true);
// 切换右侧内容展示
const changeRightContentShow = () => {
  rightContentShow.value = !rightContentShow.value;
};
// 初始化左侧树、三维方法
const init = async () => {
  const { query } = useRoute();
  const { uuid, name } = query;
  // 没有值返回
  if (!uuid || !name) {
    return;
  }
  // 保存场景名称
  sceneName.value = name;
  sceneUuid.value = uuid;
  // 查询场景树
  await getSceneTree();
  nextTick(() => {
    // 创建场景
    loadDepend(() => {
      createCampus();
    });
  });
};
const childLoading = ref(false);
// 加载依赖
const loadDepend = (cb: Function) => {
  loadJsFiles(() => {
    cb && cb();
  }, ['thingjs', 'EffectThemeControl', 'thing.campus']);
};
const ifChildSence = ref(false);
// 创建园区
const createCampus = async () => {
  //  初始化app实例
  const app = await initApp('div3d');
  window.app = app;
  childLoading.value = false;
  // 加载进度
  const loadOption: any = {};
  if (sceneStore.thingjsVersion === 1) {
    app.on(THING.EventType.LoadCampusProgress, (ev: any) => {
      loadingPercent.value = Number((ev.progress * 100).toFixed(2));
    });
  } else if (sceneStore.thingjsVersion === 2) {
    loadOption.loadProgress = (num: number) => {
      loadingPercent.value = num;
    };
  }
  // 创建园区
  window.campusManagerIns = CampusManager({
    app, // THINGJS实例
    sceneCode: sceneData.value.sceneCode,
    flyToSetting: true, // 园区，建筑，楼层默认飞到设置视角 - 默认true
    initChildScene: false, // 加载主场景时是否加载子场景 默认false
    outlineColor: '#FFB810',
    flyTime: 1000,
    ...loadOption,
    childSceneBeginLoad: () => {
      loading.value = true;
      childLoading.value = true;
      loadingPercent.value = 0;
    },
    childSceneLoaded: () => {
      setTimeout(() => {
        loading.value = false;
        childLoading.value = false;
        loadingPercent.value = 0;
      }, 1000);
      // 动态加载子场景时，子场景加载完毕需要重新应用一下效果模板
      window.effectManagerIns && window.effectManagerIns.applyTheme();
    },
    sceneLoaded: async (campus: any) => {
      // 层级切换
      app.level.change(campus);
      setTimeout(() => {
        // 保存原始后期
        if (sceneStore.thingjsVersion === 1) {
          oldEffect = JSON.parse(JSON.stringify(window.app.postEffect));
        }
        loading.value = false;
        // 场景加载完毕
        sceneStore.sceneLoaded = true;
        senceLoaded();
      }, 1000);
    },
  });
};
// 创建建筑顶牌
const createBuildingMarker = () => {
  const buildings = sceneData.value.children?.filter((building: any) => building.name !== '室外');
  if (buildings && buildings.length) {
    buildings.forEach((item: any) => {
      const building = window.app.query(`#${item.userid}`)[0];
      if (sceneStore.thingjsVersion === 1) {
        let marker = window.app.query(`##Marker${item.uuid}`)[0];
        if (marker) {
          marker.element.firstChild.innerHTML = item.settingName || item.name;
        } else {
          const props = {
            name: item.settingName || item.name,
          };
          const div = document.createElement('div');
          div.className = 'building-panel';
          const instance = createVNode(BuildingMarker, props);
          render(instance, div);
          marker = window.app.create({
            type: 'Marker',
            // @ts-ignore
            uuid: `Marker${item.uuid}`,
            pivot: [0.5, 1],
            element: instance.el,
            name: 'BuildingMarker',
            parent: window.app.query('.Campus')[0],
            position: [building.center[0], building.center[1] + building.size[1] / 2 + 8, building.center[2]],
            size: 8,
          });
          const truck = window.app.create({
            type: 'Thing',
            name: 'markerTruck',
            parent: marker,
            localPosition: [0, 1, 0],
            inheritScale: false,
            inheritStyle: false,
            inheritTheme: false,
            scale: [4, 4, 4],
            keepSize: false,
            url: '/base/models/717bfd9a01b845d1bfc397998ad3d8f1/gltf/',
          });
          // window.app.on(
          //   THING.EventType.EnterLevel,
          //   '.Campus',
          //   () => {
          //     marker.visible = true;
          //   },
          //   'buildingMarkerTag'
          // );
          // window.app.on(
          //   THING.EventType.LeaveLevel,
          //   '.Campus',
          //   () => {
          //     marker.visible = false;
          //   },
          //   'buildingMarkerTag'
          // );
        }
      } else {
        // // @ts-ignore
        // building.inherit.visible = THING.InheritType.Break;
        let css = building.css;
        if (css) {
          css.domElement.firstChild.innerHTML = item.settingName || item.name;
        } else {
          const props = {
            name: item.settingName || item.name,
          };
          const div = document.createElement('div');
          div.className = 'building-panel';
          const instance = createVNode(BuildingMarker, props);
          render(instance, div);
          // @ts-ignore
          building.addComponent(THING.DOM.CSS3DComponent, 'css');
          const css = building.css;
          css.domElement = instance.el;
          // 获取/设置基于下中，默认为：[0.5, 0.5]（中心位置）
          css.pivot = [0.5, 0];
          css.offset = [0, building.boundingBox.size[1] + 10, 0];
          // 获取/设置 DOM 元素缩放比例,默认为：1 / 30
          css.factor = 0.2;
          // @ts-ignore
          const truck = new THING.Entity({
            name: 'markerTruck',
            parent: building,
            localPosition: [0, building.boundingBox.size[1] + 8, 0],
            visible: true,
            scale: [4, 4, 4],
            keepSize: false,
            url: '/base/models/717bfd9a01b845d1bfc397998ad3d8f1/gltf/',
            complete: (e: any) => {
              // @ts-ignore
              e.object.inherit.visible = THING.InheritType.Stop;
            },
          });
          window.app.on(
            THING.EventType.AfterEnterLevel,
            '.Campus',
            () => {
              const Buildings = window.app.query('.Building');
              Buildings.forEach((building: any) => {
                if (building.css) {
                  building.css.visible = true;
                }
              });
              window.app.query('markerTruck').visible = true;
            },
            'buildingMarkerTag'
          );
          window.app.on(
            THING.EventType.AfterEnterLevel,
            '.Building || .Floor',
            (e: any) => {
              const Buildings = window.app.query('.Building');
              Buildings.forEach((building: any) => {
                if (building.css) {
                  building.css.visible = false;
                }
              });
              window.app.query('markerTruck').visible = false;
            },
            'buildingMarkerTag'
          );
        }
      }
    });
  }
};

// 场景加载完毕处理方法
const senceLoaded = () => {
  console.log('场景加载完毕');
  // 初始化摆点控制实例
  window.campusPlacementIns = CampusPlacement(window.app);
  // 初始化摆点新建点实例
  window.createPointTwinIns = CreatePointTwin(window.app);
  // 初始化摆点新建线实例
  window.createLineTwinIns = CreateLineTwin(window.app);
  // 初始化摆点新建面实例
  window.createRegionTwinIns = CreateRegionTwin(window.app);
  // 初始化线面编辑实例
  window.lineSurfaceTransformIns = LineSurfaceTransform(window.app);
  // 初始化点位复制实例
  window.copyTwinIns = CopyTwin(window.app);
  // 初始化打组类
  window.groupTwinIns = GroupTwin(window.app);
  //初始化子场景位置矫正
  window.scencePosCorrect = ScencePosCorrect(window.app);
  // 创建建筑顶牌
  createBuildingMarker();
  // 监听进入楼层层级-处理加载楼层名称
  window.app.on(
    THING.EventType.EnterLevel,
    '*',
    (ev: any) => {
      if (ev.current.type === 'Floor') {
        if (toolsFunc.value.showRoomName) {
          showFloorName(window.campusManagerIns?.currentSceneUuid, ev.current);
        }
      } else if (toolsFunc.value.showRoomName) {
        hideFloorName();
      }
      ifChildSence.value = false;
      const userData = ev.current?.userData;
      if (ev.current.type === 'Building' && userData?.childrenSceneUUID && userData?.facades) {
        ifChildSence.value = true;
      }
      if (ev.current.type === 'Campus') {
        window.app.query('.Campus')[0].visible = true;
      }
    },
    'floorName'
  );
  // 监听进入楼层层级-处理加载楼层名称
  window.app.on(
    THING.EventType.BeforeLeaveLevel,
    '*',
    (ev: any) => {
      if (ev.current.type === 'Building' && ifChildSence.value && childScencePosPanelRef.value && typeof childScencePosPanelRef.value.cancel === 'function') {
        try {
          childScencePosPanelRef.value.cancel();
        } catch (error) {
          console.error('Error during child scene position cancel:', error);
        }
      }
    },
    'buidlLevel'
  );
};

// 右侧面板宽度
const rightWidth = ref(330);
// 初始化右侧拖拽条事件
const initRightDrag = () => {
  const dragBar = document.getElementById('campus-drag-bar');
  const rightContent = document.getElementById('campus-right-content-wrap');
  const previewPanel = document.getElementById('campus-preview-content-panel');
  if (!dragBar) {
    return;
  }
  dragBar.onmousedown = (e) => {
    e.stopPropagation();
    const startX = e.clientX;
    const startWidth = rightWidth.value;
    let width = 0;
    rightContent!.style.transitionDuration = '0s';
    previewPanel!.style.pointerEvents = 'all';
    document.onmousemove = (e1) => {
      const endX = e1.clientX;
      width = startX - endX + startWidth;
      if (width < 330) {
        width = 330;
      }
      if (width > 690) {
        // 超过最大高度不能往下再拖
        width = 690;
      }
      rightWidth.value = width;
      // control.css({ right: `${width - 1}px` })
      // content.css({ width: `${width}px` })
    };
    document.onmouseup = () => {
      e.stopPropagation();
      rightContent!.style.transitionDuration = '0.3s';
      previewPanel!.style.pointerEvents = 'none';
      if (width !== 0) {
        document.onmousemove = null;
        document.onmouseup = null;
        rightWidth.value = width;
      }
    };
  };
};
// 刷新场景树数据 twinFlag: true
const refreshTree = () => {
  getSceneMsg({ uuid: sceneUuid.value, flag: true, twinFlag: true }).then((res) => {
    if (res.success) {
      sceneData.value = res.data;
      // 刷新building marker
      createBuildingMarker();
    }
  });
};
// 查询场景树 twinFlag: true
const getSceneTree = () => {
  return new Promise((resolve, reject) => {
    treeLoading.value = true;
    getSceneMsg({ uuid: sceneUuid.value, flag: true, twinFlag: true })
      .then((res) => {
        treeLoading.value = false;
        if (res.success) {
          sceneData.value = res.data;
          // 处理左侧树默认选中
          const outDoor = res.data.children.find((child: any) => child.dataType === 'outdoors');
          if (outDoor) {
            sceneStore.currentLevelUuid = [outDoor.uuid];
          }
          // 保存sceneCode
          sceneStore.sceneCode = res.data.sceneCode;
          resolve(res.data);
        }
      })
      .catch(() => {
        treeLoading.value = false;
        reject();
      });
  });
};
// #region 3D2D转换
const viewModes = ['3D', '2D'];
const currentViewMode = ref('3D');
// 3d2d改变
const viewModeChange = (viewMode: string) => {
  currentViewMode.value = viewMode;
  if (sceneStore.thingjsVersion === 1) {
    if (viewMode === '3D') {
      window.app.camera.viewMode = THING.CameraView.Normal;
    } else {
      window.app.camera.viewMode = THING.CameraView.TopView;
    }
  } else {
    if (viewMode === '3D') {
      window.app.camera.setViewMode();
      window.app.camera.enableRotate = true;
    } else {
      window.app.camera.setViewMode('Top');
      window.app.camera.enableRotate = false;
    }
  }
};
// 2d视角，层级切换后应用一次
const useDefaultViewModel = () => {
  if (currentViewMode.value !== '2D') return;
  if (sceneStore.thingjsVersion === 1) {
    window.app.camera.viewMode = THING.CameraView.TopView;
  } else {
    window.app.camera.setViewMode('Top');
    window.app.camera.enableRotate = false;
  }
};
// #endregion 3D2D转换结束
// #region 效果包设置
const showEffectPanel = ref(false); // 是否显示效果包设置面板
const changeEffectPanel = () => {
  showEffectPanel.value = !showEffectPanel.value;
};
// #endregion 效果包设置结束
onMounted(() => {
  initRightDrag();
  // 默认3D
  currentViewMode.value = '3D';
});
// 初始化
init();
//#region 场景位置矫正
const childScencePosPanelRef = ref();
const startChildPos = ref(false);
const clickPosCorrent = () => {
  // 增加更多检查
  if (window.app && window.app.level && window.app.level.current && window.app.level.current.type === 'Building') {
    // 确保组件已加载并可用
    nextTick(() => {
      if (childScencePosPanelRef.value) {
        showEffectPanel.value = false;
        startChildPos.value = true;
        try {
          childScencePosPanelRef.value.init();
        } catch (error) {
          console.error('Error initializing child scene position panel:', error);
        }
      }
    });
  }
};
const childScencePosPanelBack = () => {
  startChildPos.value = false;
  refreshTree();
  window.campusManagerIns.updateCampusData();
};
//#endregion
const fullscreen = ref(false);
const fullScreenFn = (flag: boolean) => {
  fullscreen.value = flag;
  const dom3d = document.getElementById('div3d');
  const campusPrewDow = document.getElementById('campus-preview-content-panel');
  const prewbtnsDom = document.getElementById('prewbtns');

  if (fullscreen.value) {
    dom3d.style.position = 'fixed';
    campusPrewDow.style.visibility = 'hidden';
    prewbtnsDom.style.visibility = 'visible';
    prewbtnsDom.style.position = 'fixed';
  } else {
    dom3d.style.position = 'relative';
    campusPrewDow.style.visibility = 'visible';
    prewbtnsDom.style.visibility = 'auto';
    prewbtnsDom.style.position = 'absolute';
  }
};
</script>
<style scoped lang="scss">
.campus-preview.keep-px {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--primary-bg-color);

  :deep(.ant-tree-title) {
    width: 100%;
  }

  :deep(.ant-tree-switcher) {
    line-height: 38px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 18px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .place-twin-modal {
    // position: absolute;
    // top: 200px;
    // right: 400px;
    // z-index: 999;
    // width: 320px;
    // max-height: 520px;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    // border: 1px solid var(--primary-border-color);
    // border-radius: 6px;
    // box-shadow:
    //   inset 0 0 10px 0 rgb(0 0 0 / 10%),
    //   0 0 10px 0 rgb(0 0 0 / 10%);
  }

  .preview-content {
    position: relative;
    display: flex;
    flex: 1;
    width: 100%;
    overflow: hidden;

    .preview-content-3d {
      position: absolute;
      top: 0;
      left: 0;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding-top: 55px;
      user-select: none;

      #div3d {
        width: 100%;
        height: 100%;
      }
    }

    .preview-content-panel {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 100;
      display: flex;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .left-content {
        position: relative;
        height: 100%;
        pointer-events: all;

        .left-tree {
          width: 280px;
          height: 100%;
          background: var(--primary-bg-color);
          border-right: 1px solid var(--header-border-color);
          transition: all 0.3s;

          .tree-content {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            padding: 12px 16px;
            overflow-y: auto;

            .preview-tree {
              width: 100%;

              .root-tree-item {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: middle;

                .title {
                  display: block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .btns {
                  display: flex;

                  .btn {
                    display: flex;
                    width: 22px;
                    max-width: 22px;
                    height: 22px;
                    max-height: 22px;
                    margin: 0 2px;
                    text-align: center;

                    &:hover {
                      background: var(--btn-hover-bg);
                      border-radius: 4px;
                    }

                    &.active {
                      .anticon {
                        color: var(--theme-color);
                      }
                    }

                    .anticon {
                      font-size: 20px;
                      color: var(--primary-text-color);
                    }
                  }
                }
              }
            }
          }
        }

        .left-content-expand {
          position: absolute;
          top: 50%;
          left: 100%;
          z-index: 999;
          display: flex;
          place-items: center center;
          width: 16px;
          height: 128px;
          cursor: pointer;
          background: var(--theme-color);
          border-radius: 0 8px 8px 0;
          transform: translateY(-50%);

          .left-content-hand {
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-right: 5px solid #fefefe;
            border-bottom: 5px solid transparent;
            border-left: 5px solid transparent;

            &.hide {
              margin-left: 5px;
              border-right: 5px solid transparent;
              border-left: 5px solid #fefefe;
            }
          }
        }
      }

      .center-content {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
        user-select: none;

        .center-top {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 55px;
          padding: 0 22px;
          pointer-events: all;
          background: var(--primary-bg-color);
          border-bottom: 1px solid var(--header-border-color);

          .tools-wrap {
            display: flex;
            flex-wrap: wrap;
            max-height: 44px;
            overflow: hidden;

            .tool-item {
              margin-right: 20px;

              .tool-check {
                :deep(span) {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }

          .func-btns {
            display: flex;
            align-items: center;

            .btn {
              &:first-child {
                margin-right: 16px;
              }
            }

            .func-icon {
              color: inherit;
            }
          }
        }

        .center-bottom {
          position: relative;
          flex: 1;
          width: 100%;
          overflow: hidden;
          pointer-events: none;

          .handle-notice {
            position: absolute;
            bottom: 2px;
            left: 50%;
            min-width: 20px;
            padding: 0 12px;
            overflow: hidden;
            line-height: 28px;
            text-overflow: ellipsis;
            letter-spacing: 1px;
            white-space: nowrap;
            background-color: var(--router-bg-color);
            border-radius: 3px;
            transform: translateX(-50%);

            .key-code {
              padding: 2px 4px;
              background-color: var(--btn-hover-bg);
              border-radius: 1px;
            }
          }

          .performance-wrap {
            position: absolute;
            top: 24px;
            left: 24px;
          }

          .view-modes {
            position: absolute;
            top: 24px;
            right: 23px;
            display: flex;
            pointer-events: all;
            cursor: pointer;

            .view-mode {
              box-sizing: border-box;
              width: 56px;
              height: 36px;
              font-size: 14px;
              line-height: 36px;
              text-align: center;
              background: rgb(255 255 255 / 90%);
              border: 1px solid #c3c4c7;
              transition: all 0.5s;

              &:first-child {
                border-radius: 6px 0 0 6px;
              }

              &:last-child {
                border-radius: 0 6px 6px 0;
              }

              &.active {
                font-weight: 500;
                color: #fff;
                background: var(--theme-color);
                border: 1px solid var(--theme-color);
              }
            }
          }

          .child-Scence {
            position: absolute;
            top: 100px;
            right: 23px;
            pointer-events: all;
            cursor: pointer;

            .icon {
              width: 30px;
              height: 30px;
              margin: 4px;
              font-size: 25px;
              line-height: 30px;
              color: #fff;
              text-align: center;
              cursor: pointer;

              &.active,
              &:hover {
                color: var(--theme-color);
              }
            }
          }

          .prewbtns {
            position: absolute;
            right: 20px;
            bottom: 20px;
            pointer-events: all;
            cursor: pointer;

            .icon {
              font-size: 20px;
              color: #fff;
              cursor: pointer;

              &:hover {
                color: var(--theme-color);
              }
            }
          }
        }
      }

      .right-content {
        position: relative;
        height: 100%;
        pointer-events: all;
        user-select: none;

        .right-content-wrap {
          width: 330px;
          height: 100%;
          background: var(--primary-bg-color);
          border-left: 1px solid var(--header-border-color);
          transition: all 0.3s;
        }

        .right-content-expand {
          position: absolute;
          top: 50%;
          right: 100%;
          z-index: 999;
          display: flex;
          place-items: center center;
          width: 16px;
          height: 128px;
          cursor: pointer;
          background: var(--theme-color);
          border-radius: 8px 0 0 8px;
          transform: translateY(-50%);

          .right-content-hand {
            width: 0;
            height: 0;
            margin-left: 5px;
            border-top: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 5px solid #fefefe;

            &.hide {
              margin-left: 0;
              border-right: 5px solid #fefefe;
              border-left: 5px solid transparent;
            }
          }
        }

        .drag-bar {
          position: absolute;
          top: 0;
          left: 0;
          width: 10px;
          height: 100%;
          cursor: col-resize;
        }
      }
    }
  }
}
</style>
