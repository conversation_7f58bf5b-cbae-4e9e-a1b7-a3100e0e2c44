package com.uino.x.pedestal.tenant.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.uino.x.common.cache.CacheRefresh;
import com.uino.x.common.core.factory.ExecutorServiceFactory;
import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.common.core.util.FeignUtils;
import com.uino.x.common.core.util.TenantUtils;
import com.uino.x.common.core.util.ZipUtils;
import com.uino.x.common.datasource.properties.ModuleConfigProperties;
import com.uino.x.common.datasource.util.DataSourceConfigurationUtils;
import com.uino.x.common.datasource.util.IgnoreInfo;
import com.uino.x.common.datasource.util.SqlUtils;
import com.uino.x.common.datasource.util.TenantDataSource;
import com.uino.x.common.label.annotation.aop.RedissonLock;
import com.uino.x.common.label.constant.TenantConstant;
import com.uino.x.common.label.enums.StatusEnum;
import com.uino.x.common.label.exception.GenericException;
import com.uino.x.common.objectstorage.response.UploadObjectResponse;
import com.uino.x.common.objectstorage.template.ObjectStorageTemplate;
import com.uino.x.common.pojo.entity.BaseEntity;
import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.sql.maker.SchemaSql;
import com.uino.x.common.sql.maker.SchemaSqlFactory;
import com.uino.x.common.sql.maker.TableSql;
import com.uino.x.common.tool.base.AssertUtils;
import com.uino.x.common.tool.base.CollectionUtils;
import com.uino.x.common.tool.base.DateUtils;
import com.uino.x.common.tool.base.ThrowUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.common.constant.DataSourceConstant;
import com.uino.x.pedestal.effectpackage.common.utils.PlacementDownloadUtils;
import com.uino.x.pedestal.file.common.domain.FileAsyInfo;
import com.uino.x.pedestal.file.common.properties.DownloadProperties;
import com.uino.x.pedestal.file.common.util.AsyncFileTask;
import com.uino.x.pedestal.file.common.util.FileViewer;
import com.uino.x.pedestal.file.entity.SysFileInfo;
import com.uino.x.pedestal.identity.api.IdentityContext;
import com.uino.x.pedestal.identity.api.SysUserApi;
import com.uino.x.pedestal.identity.dao.mapper.SysUserMapper;
import com.uino.x.pedestal.identity.pojo.domain.SysLoginUser;
import com.uino.x.pedestal.identity.pojo.entity.SysUser;
import com.uino.x.pedestal.message.api.service.RealTimeMessageService;
import com.uino.x.pedestal.message.pojo.entity.SysRealTimeMessage;
import com.uino.x.pedestal.model.common.properties.ModelConfigProperties;
import com.uino.x.pedestal.projectpack.api.service.ProjectPackService;
import com.uino.x.pedestal.tenant.api.DataMigrationApi;
import com.uino.x.pedestal.tenant.api.EdtapApi;
import com.uino.x.pedestal.tenant.api.TiApi;
import com.uino.x.pedestal.tenant.api.service.DataMigrationService;
import com.uino.x.pedestal.tenant.api.service.TenantInfoService;
import com.uino.x.pedestal.tenant.common.enums.DataMigrationDifferenceOperation;
import com.uino.x.pedestal.tenant.common.enums.DynamicBusinessTable;
import com.uino.x.pedestal.tenant.common.exception.DataMigrationException;
import com.uino.x.pedestal.tenant.dao.mapper.DataMigrationMapper;
import com.uino.x.pedestal.tenant.migrate.AbstractDataMigrationService;
import com.uino.x.pedestal.tenant.migrate.properties.DataMigrateProperties;
import com.uino.x.pedestal.tenant.pojo.domain.DataMigrationMetaInfo;
import com.uino.x.pedestal.tenant.pojo.domain.DynamicForm;
import com.uino.x.pedestal.tenant.pojo.entity.TenantInfo;
import com.uino.x.pedestal.tenant.pojo.param.DataMigrationParam;
import com.uino.x.pedestal.tenant.pojo.vo.DataMigrationDifferenceDetailVo;
import com.uino.x.pedestal.tenant.pojo.vo.DataMigrationDifferenceVo;
import com.uino.x.pedestal.twin.common.utils.FormUtils;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.flyway.FlywayProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.uino.x.pedestal.effectpackage.common.utils.PlacementDownloadUtils.SEP;


/**
 * 数据迁移service实现
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2020/4/14 11:16
 */
@Service
@Slf4j
@EnableConfigurationProperties(DataMigrateProperties.class)
public class DataMigrationServiceImpl extends AbstractDataMigrationService implements DataMigrationService {

    public static final ExecutorService EXECUTOR_SERVICE = ExecutorServiceFactory.createThreadPool(DataMigrationServiceImpl.class);


    private static final String MIGRATION_META_INFO_FILE_NAME = ".meta";
    private static final String SYSTEM_SERVER = "system-x";
    private static final String MODEL_SERVER = "model-x";
    private static final String SCENE_SERVER = "scene-x";
    private static final String TWIN_SERVER = "twin-x";
    private static final String MASTER = TenantConstant.MASTER;
    private static final String SYSTEM_X = DataSourceConstant.SYSTEM_X;
    private static final int MIGRATION_FILE_SQL_COUNT = 5000;
    public final DataMigrateProperties dataMigrateProperties;
    private final SysUserMapper sysUserMapper;
    private final SysUserApi sysUserApi;
    private final ObjectStorageTemplate ost;
    private final DataMigrationMapper dataMigrationMapper;
    private final RealTimeMessageService realTimeMessageService;
    private final CacheRefresh cacheRefresh;
    private final TenantInfoService tenantInfoService;
//    private final PluginInfoMapper pluginInfoMapper;
    private final FlywayProperties flywayProperties;
    private final EdtapApi edtapApi;
    @Value("${file.sample-osr-url}")
    private String sampleOsrUrl;
    @Value("${file.hundun-url}")
    private String hundunUrl;
    private final DownloadUtil downloadUtils;
    @Autowired
    private TiApi tiApi;
    private final ModuleConfigProperties moduleDataSourceProps;
    private final static String BIG_MARGIN_FILE_PATH = "/uino/sync";

    public DataMigrationServiceImpl(DataMigrateProperties dataMigrateProperties, SysUserMapper sysUserMapper, SysUserApi sysUserApi, ObjectStorageTemplate ost, DataMigrationMapper dataMigrationMapper, RealTimeMessageService realTimeMessageService, CacheRefresh cacheRefresh, TenantInfoService tenantInfoService, /*PluginInfoMapper pluginInfoMapper,*/ FlywayProperties flywayProperties, EdtapApi edtapApi, DownloadUtil downloadUtils, ModuleConfigProperties moduleDataSourceProps) {
        this.edtapApi = edtapApi;
        this.downloadUtils = downloadUtils;
        this.moduleDataSourceProps = moduleDataSourceProps;
        super.dataMigrateProperties = dataMigrateProperties;
        this.dataMigrateProperties = dataMigrateProperties;
        this.sysUserMapper = sysUserMapper;
        this.sysUserApi = sysUserApi;
        this.ost = ost;
        this.dataMigrationMapper = dataMigrationMapper;
        this.realTimeMessageService = realTimeMessageService;
        this.cacheRefresh = cacheRefresh;
        this.tenantInfoService = tenantInfoService;
//        this.pluginInfoMapper = pluginInfoMapper;
        this.flywayProperties = flywayProperties;

        tiApi = null;
    }

    @Override
    public Set<String> availableDownloadFile(String code){
        List<String> names = ost.listObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest() + "/", false).getNames();
        if (CollectionUtils.isEmpty(names)){
            return Collections.emptySet();
        }
        return names.stream().map(e->e.replace(code,"").replace("/","")).collect(Collectors.toSet());
    }

    @Override
    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户文件数据正在导出中，请稍后再试")
    public void downloadFile(DataMigrationParam param) {
        final SysLoginUser sysLoginUser = IdentityContext.getSysLoginUser();
        final String tenantName = getTenantName(param.getCode());
        String zipFileName = "租户【" + tenantName + "】文件资源" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);

        try {
            if (CollectionUtils.isNotEmpty(param.getMigrationFileList())){
                ost.downloadObjects(ModelConfigProperties.getRootBucket(), TenantUtils.getTenantByRequest(), fileAsyInfo.getTempFile() + File.separator);
            }else{
                for (String bucket:param.getMigrationFileList()){
                    ost.downloadObjects(ModelConfigProperties.getRootBucket(), String.format("%s/%s", TenantUtils.getTenantByRequest(),bucket), fileAsyInfo.getTempFile() + File.separator);
                }
            }
            File[] files = fileAsyInfo.getTempFile().listFiles();
            if (Objects.isNull(files) && files.length <= 0) {
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), String.format("下载租户【%s】文件资源失败，该租户下文件为空！", tenantName), null));
                return;
            }
            for (File file : files) {
                String zipPath = String.format("%s%s%s.zip", fileAsyInfo.getZipFile().getAbsoluteFile(), File.separator, file.getName());
                ZipUtils.zip(file, new File(zipPath));
            }

            String buildFileName = fileAsyInfo.getAbsoluteFile() + SEP + zipFileName;
            ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
            // 文件路径  桶名称  存放路径
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + "/" + zipFileName, buildFileName);
            // 6.消息通知下载
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), String.format("打包租户【%s】文件资源成功！请在%s日之前下载，过期后，文件将被删除", tenantName, DateTimeFormatter.ofPattern("yyyy-MM-dd").format(fileAsyInfo.getExpiredDate())), fileAsyInfo.getDownLoadPath()));
            log.info("打包租户数据资源完成");
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(sysLoginUser.getId(), String.format("下载租户【%s】文件资源失败，请稍后再试！", tenantName), null));
            log.error("下载租户{}文件资源失败:", tenantName, e);
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
    }

    @Override
//    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户文件数据正在导出中，请稍后再试")
    public String feignDownloadFile(DataMigrationParam param) {
        // 下载操作文件
        Map<String, String> edtapMap = param.getEdtapMap();
        if (edtapMap != null && edtapMap.get("bucket") != null) {
            ost.downloadObject(edtapMap.get("bucket"), edtapMap.get("bucketValue"), param.getPackagePath() + "/操作系统说明.txt");
        }
        downloadTexture(param);
        packFile(param);
        String attachmentAddress = packDataSource(param);
        Map<String, Object> callBackMessage = param.getCallBackMessage();
        callBackMessage.put("attachmentAddress",attachmentAddress);
        edtapApi.sendSystemMessage(callBackMessage);
        return attachmentAddress;
    }


    /**
     * 下载大屏数据
     *
     * @return
     */
    public void downloadTexture(DataMigrationParam param) {
        for (int i = 0; i <= 1; i++) {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            String zipFileName = "";
            String downloadType = "";
            if (i == 0) {
                downloadType = "projectGroup/downloadScene/" + param.getCode();
                zipFileName = "租户【" + param.getCode() + "】大屏资源" + ".zip";
            } else {
                downloadType = "projectGroup/downloadComponent/" + param.getCode();
                zipFileName = "租户【" + param.getCode() + "】图表资源" + ".zip";
            }
            FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
            try {
                HttpGet httpGet = new HttpGet(hundunUrl + downloadType);
//                httpGet.addHeader("Authorization", request.getHeader("Authorization"));
                HttpResponse httpResponse = httpClient.execute(httpGet);
                JSONObject pa = JSONObject.parseObject(EntityUtils.toString(httpResponse.getEntity()));
                if (pa.get("code").equals(200)) {
                    log.info("资源【{}】获取成功", zipFileName);
                    if (pa.get("data") != null) {
                        String data = pa.getString("data");
                        org.json.JSONObject jsonObject = new org.json.JSONObject(data);
                        Iterator<String> keys = jsonObject.keys();
                        String key = null;
                        while (keys.hasNext()) {
                            key = keys.next();
                            log.info("资源key获取成功【{}】", key);
                        }
                        if (key != null) {
                            String value = jsonObject.get(key).toString().replace("\"", "").replace("[", "").replace("]", "");
                            String[] strArr = value.split(",");
                            for (String val : strArr) {
                                ost.downloadObject(key, val, fileAsyInfo.getTempFile() + File.separator + val);
                            }
                            String buildFileName = param.getPackagePath() + SEP + zipFileName;
                            Path paths = Paths.get(param.getPackagePath());
                            Files.createDirectories(paths);
                            ZipUtils.zip(fileAsyInfo.getTempFile(), new File(buildFileName));
                        }
                    } else {
                        log.info(zipFileName + "没有大屏/图表资源");
                    }
                } else {
                    log.info(zipFileName + "生成数据失败");
                }
                httpClient.close();
            } catch (Exception e) {
                throw new DataMigrationException(String.format("租户【%s】资源迁移失败", zipFileName), e);

            }
        }
    }

    @Override
//    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户文件数据正在导出中，请稍后再试")
    public String feignDownloadDatabase(DataMigrationParam param) {

        Map<String, Object> callBackMessage = param.getCallBackMessage();
        String attachmentAddress = packDataSource(param);
        callBackMessage.put("attachmentAddress",attachmentAddress);
        edtapApi.sendSystemMessage(callBackMessage);
        return attachmentAddress;
    }

    /**
     * 打包文件
     */
    public void packFile(DataMigrationParam param) {
        final String tenantName = getTenantName(param.getCode());
        String zipFileName = "租户【" + tenantName + "】文件资源" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);

        try {
            ost.downloadObjects(ModelConfigProperties.getRootBucket(), String.format("%s", param.getCode()), fileAsyInfo.getTempFile() + File.separator);

            File[] files = fileAsyInfo.getTempFile().listFiles();
            if (Objects.isNull(files) && files.length <= 0) {
                return;
            }
            for (File file : files) {
                String zipPath = String.format("%s%s%s.zip", fileAsyInfo.getZipFile().getAbsoluteFile(), File.separator, file.getName());
                ZipUtils.zip(file, new File(zipPath));
            }

            String buildFileName = fileAsyInfo.getAbsoluteFile() + SEP + zipFileName;
            ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
            // 文件路径  桶名称  存放路径
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + "/" + zipFileName, buildFileName);
            // 6.消息通知下载
            log.info("打包租户数据资源完成");
        } catch (Exception e) {
            log.error("下载租户{}文件资源失败:", tenantName, e);
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
    }

    public String packDataSource(DataMigrationParam param) {
        final String tag = param.getTag();
        final String code = param.getCode();
        final Boolean businessData = param.getBusinessData();
        final String businessDataMessage = businessData ? "" : "（不包含业务）";
        String zipFileName = "租户【" + code + "】系统数据" + businessDataMessage + "_" + tag + "_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
        try {
            // 1.异步通知各个服务租户数据导出
            List<String> migrationServerList = dataMigrateProperties.getMigrationServerList();
            if (CollectionUtils.isNotEmpty(migrationServerList)) {
                AsyncUtils.asyncExecutorBean(migrationServerList, serverName -> asyncExportData(serverName, code, tag, businessData, fileAsyInfo.getZipPath(), false), EXECUTOR_SERVICE);
            }
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
        return null;
    }

    @Override
    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户文件数据正在导入中，请稍后再试")
    public void uploadFile(DataMigrationParam param) {
        final String tenantName = getTenantName(param.getCode());
        final Long loginUserId = IdentityContext.getSysLoginUserId();
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH, IdUtils.fastUuid());
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        MultipartFile multipartFile = param.getFile();
        // 校验文件格式
        String multipartFileName = Objects.nonNull(multipartFile) ? multipartFile.getOriginalFilename() : param.getFilePath();
        AssertUtils.isTrue(StringUtils.isNotBlank(multipartFileName), HttpStatus.BAD_REQUEST, "文件名不能为空！");
        AssertUtils.isTrue(multipartFileName.endsWith(".zip"), HttpStatus.FORBIDDEN, "请上传zip文件");
        String filePath = tempPath.getAbsolutePath() + SEP;
        String zipPath = filePath + multipartFileName;
        // 期待要上传的目标文件
        File file = new File(zipPath);
        try {
            if (!file.exists()) {
                AssertUtils.isTrue(file.createNewFile(), "文件创建失败");
            }
            if (Objects.isNull(multipartFile)) {
                // 取文件路径
                File _syncFile = new File(BIG_MARGIN_FILE_PATH, param.getFilePath());
                AssertUtils.isTrue(_syncFile.exists(), HttpStatus.BAD_REQUEST, "未检测到已上传的文件！");
                // 将手动上传的迁移文件，移动到要处理的文件目录
                FileUtils.moveFile(_syncFile, file);
            } else {
                multipartFile.transferTo(file);
            }
            String fileDir = filePath + multipartFileName.substring(0, multipartFileName.lastIndexOf("."));
            File unzipFile = new File(fileDir);
            FileUtils.forceMkdir(unzipFile);
            ZipUtils.unzip(zipPath, fileDir, dataMigrateProperties.getPassword());

            File[] zips = unzipFile.listFiles();
            Optional.ofNullable(zips).ifPresent(zipArray -> {
                // 确定需要用多少的线程
                CountDownLatch latch = new CountDownLatch(zips.length);
                for (File zip : zips) {
                    try {
                        asyncImportFile(param.getCode(), zip, latch);
                    } catch (Exception e) {
                        log.error("{}租户{}文件资源导入失败：", tenantName, zip.getName(), e);
                    }
                }
                try {
                    if (latch.await(30, TimeUnit.MINUTES)) {
                        realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("租户【%s】文件资源迁移成功！", tenantName), null));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("租户【%s】文件资源迁移失败，请稍后再试！", tenantName), null));
            throw new DataMigrationException(String.format("租户【%s】文件资源迁移失败", tenantName), e);
        } finally {
            try {
                if (tempPath.exists()){
                    FileUtils.deleteDirectory(tempPath);
                }
            } catch (IOException io) {
                io.printStackTrace();
            }
        }
    }


    @Override
//    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户数据库数据正在导出中，请稍后再试")
    public void downloadDatabaseInfo(DataMigrationParam param) {
        final String tag = param.getTag();
        final String code = param.getCode();
        final String tenantName = getTenantName(code);
        final List<String> configMigrationServerList = dataMigrateProperties.getMigrationServerList();
        List<String> migrationServerList = CollectionUtils.isEmpty(param.getMigrationServerList()) ? configMigrationServerList : param.getMigrationServerList();
        final Long loginUserId = IdentityContext.getSysLoginUserId();
        final Boolean businessData = param.getBusinessData();
        final String businessDataMessage = businessData ? "" : "（不包含业务）";
        String zipFileName = "租户【" + tenantName + "】系统数据" + businessDataMessage + "_" + tag + "_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
        try {
            // 1.导出tidix当前租户的数据
            ObjectMapper objectMapper = new ObjectMapper();
            // 写入json文件
            try {
                String tiApiFilejson = tiApi.exportTiApiFile(code);
                objectMapper.writeValue(new File(fileAsyInfo.getZipPath(), "tiApiFilejson.json"), tiApiFilejson);
            } catch (Exception e) {
                log.error("tidix 导出租户数据失败: {}", code, e);
            }

            // 2.异步通知各个服务租户数据导出
            if (CollectionUtils.isNotEmpty(migrationServerList)) {
                AsyncUtils.asyncExecutorBean(migrationServerList, serverName -> asyncExportData(serverName, code, tag, businessData, fileAsyInfo.getZipPath(), false), EXECUTOR_SERVICE);
                log.info("------------------------重新压缩并上传压缩包---------------------------");
                downDataZipSaveFile(fileAsyInfo, zipFileName, loginUserId, tenantName, tag, businessDataMessage);
            }
        } catch (Exception e) {
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("打包租户【%s】系统数据%s%s版本失败，请稍后再试！", tenantName, businessDataMessage, tag), null));
            throw ThrowUtils.getThrow().internalServerError(String.format("打包租户【%s】系统数据%s失败", tenantName, businessDataMessage), e);
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
    }

    @Override
    @RedissonLock(enablePrefix = false, value = "'dataMigration:' + #param.code", message = "当前租户数据库数据正在导入中，请稍后再试")
//    public List<DataMigrationDifferenceVo> uploadDatabaseInfo(DataMigrationParam param) {
//        final Long loginUserId = IdentityContext.getSysLoginUserId();
//        MultipartFile multipartFile = param.getFile();
//        final String tenantName = getTenantName(param.getCode());
//        String multipartFileName = multipartFile.getOriginalFilename();
//        AssertUtils.isTrue(StringUtils.isNotBlank(multipartFileName), HttpStatus.BAD_REQUEST, "文件名不能为空！");
//        AssertUtils.isTrue(multipartFileName.endsWith(".zip"), HttpStatus.FORBIDDEN, "请上传zip文件");
//
//        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH + IdUtils.fastUuid());
//        if (!tempPath.exists()) {
//            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
//        }
//        try {
//            String filePath = tempPath.getAbsolutePath() + SEP;
//            String zipPath = filePath + multipartFileName;
//            File file = new File(zipPath);
//            multipartFile.transferTo(file);
//            String fileDir = filePath + multipartFileName.substring(0, multipartFileName.lastIndexOf("."));
//            File unzipFile = new File(fileDir);
//            FileUtils.forceMkdir(unzipFile);
//            ZipUtils.unzip(zipPath, fileDir, dataMigrateProperties.getPassword());
//
//            File[] zips = unzipFile.listFiles();
//
//            File tiApiFile = new File(fileDir, "tiApiFilejson.json");
//            if (tiApiFile.exists()) {
//                ObjectMapper objectMapper = new ObjectMapper();
//                try {
//                    // 将tiApiFile JSON 文件转换为字符串
//                    String fileJsonSting = objectMapper.writeValueAsString(objectMapper.readValue(tiApiFile, Object.class));
//                    if (StringUtils.isNotBlank(fileJsonSting) && !fileJsonSting.equals("null")) {
//                        tiApi.parseJson(param.getCode(), fileJsonSting);
//                    }
//                } catch (Exception e) {
//                    log.error("导出ti-api数据失败",e);
//                }
//            }
//
//            Optional.ofNullable(zips).ifPresent(zipArray -> {
//                try {
//                    File tagFile = Stream.of(zips).filter(s -> "tag.txt".equals(s.getName())).findFirst().get();
//                    String importTag = FileUtils.readFileToString(tagFile, StandardCharsets.UTF_8);
//                    AssertUtils.isTrue(dataMigrateProperties.getTag().equals(importTag), ThrowUtils.getThrow().badRequest(String.format("导入的数据版本与当前版本不一致, %s -> %s", importTag, dataMigrateProperties.getTag())));
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//                List<String> migrationServerList = dataMigrateProperties.getMigrationServerList();
//                CyclicBarrier cyclicBarrier = new CyclicBarrier(migrationServerList.size() + 1);
//                for (File zip : zips) {
//                    try {
//                        final String fileName = zip.getName();
//                        final byte[] bytes = FileUtils.readFileToByteArray(zip);
//                        if (zip.getName().contains(DataSourceConstant.SYSTEM_X)) {
//                            asyncImportData(null, param.getCode(), bytes, fileName, cyclicBarrier);
//                        }
//                        if (zip.getName().contains(DataSourceConstant.MODEL_X)) {
//                            asyncImportData(MODEL_SERVER, param.getCode(), bytes, fileName, cyclicBarrier);
//                        }
//                        if (zip.getName().contains(DataSourceConstant.SCENE_X)) {
//                            asyncImportData(SCENE_SERVER, param.getCode(), bytes, fileName, cyclicBarrier);
//                        }
//                        if (zip.getName().contains(DataSourceConstant.TWIN_X)) {
//                            asyncImportData(TWIN_SERVER, param.getCode(), bytes, fileName, cyclicBarrier);
//                        }
//                    } catch (IOException io) {
//                        io.printStackTrace();
//                    }
//                }
//                try {
//                    cyclicBarrier.await();
//                } catch (InterruptedException | BrokenBarrierException e) {
//                    throw new RuntimeException(e);
//                }
//                if (Objects.equals(cyclicBarrier.getNumberWaiting(), 0)) {
//                    sendMsg(loginUserId, tenantName);
//                }
//            });
//
//        } catch (Exception e) {
//            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("迁移租户【%s】系统数据失败，请稍后再试！", tenantName), null));
//            throw ThrowUtils.getThrow().internalServerError("文件创建失败", e);
//        } finally {
//            try {
//                FileUtils.deleteDirectory(tempPath);
//            } catch (IOException io) {
//                io.printStackTrace();
//            }
//        }
//        return null;
//    }
//    @Async
    public CompletableFuture<List<DataMigrationDifferenceVo>> uploadDatabaseInfo(DataMigrationParam param) {
        final Long loginUserId = 1L;
        MultipartFile multipartFile = param.getFile();
        // 校验文件格式
        String multipartFileName = Objects.nonNull(multipartFile) ? multipartFile.getOriginalFilename() : param.getFilePath();
        AssertUtils.isTrue(StringUtils.isNotBlank(multipartFileName), HttpStatus.BAD_REQUEST, "文件名不能为空！");
        AssertUtils.isTrue(multipartFileName.endsWith(".zip"), HttpStatus.FORBIDDEN, "请上传zip文件");
//        String filePath = tempPath.getAbsolutePath() + SEP;
//        String zipPath = filePath + multipartFileName;
        // 期待要上传的目标文件
//        File file = new File(zipPath);

        final String tenantName = getTenantName(param.getCode());
        // 创建临时目录
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH + IdUtils.fastUuid());
        AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");

        try {
            if (Objects.isNull(multipartFile)) {
                // 取文件路径
                File _syncFile = new File(BIG_MARGIN_FILE_PATH, param.getFilePath());
                AssertUtils.isTrue(_syncFile.exists(), HttpStatus.BAD_REQUEST, "未检测到已上传的文件！");
                // 将 MultipartFile 转换为字节数组
                byte[] fileBytes = FileUtils.readFileToByteArray(_syncFile);
                // 用完及时清理
                _syncFile.delete();
                // 将字节数组传递给异步方法
                return CompletableFuture.completedFuture(processFile(fileBytes, param.getCode(), loginUserId, tenantName, tempPath));
            } else {
                // 将 MultipartFile 转换为字节数组
                byte[] fileBytes = multipartFile.getBytes();
                // 将字节数组传递给异步方法
                return CompletableFuture.completedFuture(processFile(fileBytes, param.getCode(), loginUserId, tenantName, tempPath));
            }
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(loginUserId, String.format("迁移租户【%s】系统数据失败，请稍后再试！", tenantName), null));
            throw ThrowUtils.getThrow().internalServerError("数据迁移失败", e);
        } finally {
            // 清理临时目录
            cleanupTempDirectory(tempPath);
        }
    }


    private List<DataMigrationDifferenceVo> processFile(byte[] fileBytes, String code, Long loginUserId, String tenantName, File tempPath) {
        try {
            System.out.println();
            // 将字节数组写入临时文件
            String zipPath = tempPath.getAbsolutePath() + SEP + "uploadedFile.zip";
            File file = new File(zipPath);
            try (FileOutputStream outputStream = new FileOutputStream(file)) {
                outputStream.write(fileBytes);
            }

            // 解压文件
            File unzipFile = unzipFile(file, tempPath);

            // 解析 JSON 文件
            parseJsonFile(unzipFile, code);

            // 导入数据
            importData(unzipFile, code, loginUserId, tenantName);

        } catch (Exception e) {
            log.error("数据迁移失败", e);
            throw ThrowUtils.getThrow().internalServerError("数据迁移失败", e);
        }

        return null; // 返回实际的数据迁移结果
    }

    private File unzipFile(File file, File tempPath) throws Exception {
        String fileDir = tempPath.getAbsolutePath() + SEP + "unzipped";
        File unzipFile = new File(fileDir);
        FileUtils.forceMkdir(unzipFile);
        ZipUtils.unzip(file.getAbsolutePath(), fileDir, dataMigrateProperties.getPassword());
        return unzipFile;
    }

    private void parseJsonFile(File unzipFile, String code) {
        File tiApiFile = new File(unzipFile, "tiApiFilejson.json");
        if (tiApiFile.exists()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String fileJsonString = objectMapper.writeValueAsString(objectMapper.readValue(tiApiFile, Object.class));
                if (StringUtils.isNotBlank(fileJsonString) && !fileJsonString.equals("null")) {
                    tiApi.parseJson(code, fileJsonString);
                }
            } catch (Exception e) {
                log.error("解析ti-api数据失败", e);
            }
        }
    }

    private void importData(File unzipFile, String code, Long loginUserId, String tenantName) throws IOException {
        File[] zips = unzipFile.listFiles();
        if (zips == null) {
            return;
        }

        File tagFile = Stream.of(zips).filter(s -> "tag.txt".equals(s.getName())).findFirst().orElseThrow(() -> new RuntimeException("未找到tag.txt文件"));
        String importTag = FileUtils.readFileToString(tagFile, StandardCharsets.UTF_8);
        AssertUtils.isTrue(dataMigrateProperties.getTag().equals(importTag), ThrowUtils.getThrow().badRequest(String.format("导入的数据版本与当前版本不一致, %s -> %s", importTag, dataMigrateProperties.getTag())));

//        List<String> migrationServerList = dataMigrateProperties.getMigrationServerList();
        List<CompletableFuture<Object>> futures = new ArrayList<>();
        for (File zip : zips) {
            final String fileName = zip.getName();
            byte[] bytes = FileUtils.readFileToByteArray(zip);
            // TODO: 先进行组装兼容，后续按模块
            if (!fileName.contains("_")) {
                continue;
            }
            final String tmpName = fileName.substring(0, fileName.indexOf("_"));
            final String serverName = tmpName.endsWith("x") ? tmpName.substring(0, tmpName.length() - 2) + "-x" : tmpName + "-x";
            // 使用CompletableFuture包装异步任务
            final CompletableFuture<Object> future = asyncImportData(serverName, code, bytes, fileName)
                    .exceptionally(ex -> {
                        throw new RuntimeException(String.format("数据导入过程中发生错误: %s", serverName), ex);
                    });

            futures.add(future);
        }

        try {
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            throw new RuntimeException("数据导入过程中发生错误", e);
        }
            sendMsg(loginUserId, tenantName);
    }

    private void cleanupTempDirectory(File tempPath) {
        try {
            FileUtils.deleteDirectory(tempPath);
        } catch (IOException e) {
            log.error("清理临时目录失败", e);
        }
    }

    /**
     * 根据租户code获取租户名称
     *
     * @param code 租户code
     * @return 租户名称
     * <AUTHOR>
     * @date 2022/01/18 16:21:59
     */
    @Override
    public String getTenantName(String code) {
        AssertUtils.isTrue(StringUtils.isNotEmpty(code), HttpStatus.BAD_REQUEST, "租户code不能为空");
        if (MASTER.equals(code)) {
            return "主";
        }
        TenantInfo tenantInfo = tenantInfoService.getOne(new LambdaQueryWrapper<TenantInfo>().ne(BaseEntity::getStatus, StatusEnum.DELETED.getCode()).eq(TenantInfo::getCode, code));
        AssertUtils.isTrue(Objects.nonNull(tenantInfo), HttpStatus.BAD_REQUEST, "当前租户不存在");
        return tenantInfo.getName();
    }

    @Override
    public List<String> getTransferableVersion(Boolean businessData) {
        return super.getTransferableVersion(businessData);
    }

    @Override
    public List<String> getVersions(Boolean businessData) {
        return super.getVersions(businessData);
    }


    @Override
    public List<Object> doImportData(String tenantCode, byte[] bytes, String fileName) {
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        String filePath = tempPath.getAbsolutePath() + SEP;
        String fileUrl = filePath + fileName;
        File file = new File(fileUrl);
        try {
            FileUtils.writeByteArrayToFile(file, bytes);
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("文件读取失败", e);
        }

        String fileDir = filePath + fileName.substring(0, fileName.lastIndexOf("."));
        File unzipFile = new File(fileDir);
        try {
            FileUtils.forceMkdir(unzipFile);
        } catch (IOException e) {
            throw ThrowUtils.getThrow().internalServerError("文件创建失败", e);
        }
        String bakPath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        final String currentTag = dataMigrateProperties.getTag();

        final Map<String, List<IgnoreInfo>> ignoreInfoMap = dataMigrateProperties.getIgnoreInfoMap();
        final List<IgnoreInfo> systemXIgnoreInfoList = ignoreInfoMap.get(DataSourceConstant.SYSTEM_X);
        try {
            ZipUtils.unzip(fileUrl, fileDir, dataMigrateProperties.getPassword());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("数据文件解压失败", e);
        }
        File metaInfoFile = new File(unzipFile.getPath() + File.separator + MIGRATION_META_INFO_FILE_NAME);
        AssertUtils.isTrue(metaInfoFile.exists(), HttpStatus.BAD_REQUEST, "请上传有效的租户数据迁移文件");
        DataMigrationMetaInfo dataMigrationMetaInfo;
        final TenantDataSource systemXDataSource = DataSourceConfigurationUtils.newTenantDataSource(tenantCode.toLowerCase(), SYSTEM_X);
        final String systemXSchema = systemXDataSource.getSchema();
        try {
            final String metaInfoStr = FileUtils.readFileToString(metaInfoFile, StandardCharsets.UTF_8);
            final String metaInfoJson = SqlUtils.decode(true, metaInfoStr);
            dataMigrationMetaInfo = JSON.parseObject(metaInfoJson, DataMigrationMetaInfo.class);
            final List<DynamicForm> newDynamicFormList = dataMigrationMetaInfo.getDynamicFormList();
            final String importTag = dataMigrationMetaInfo.getTag();
            AssertUtils.isTrue(currentTag.equals(importTag), ThrowUtils.getThrow().badRequest(String.format("导入的数据版本与当前版本不一致, %s -> %s", importTag, currentTag)));
            if (CollectionUtils.isNotEmpty(newDynamicFormList)) {
                // 获取当前auto_api的SchemaSql
                final List<TableSql> tableSqlList;
                try (SchemaSql schemaSql = SchemaSqlFactory.get(systemXDataSource, systemXSchema)) {
                    // 获取实际忽略的TableSql
                    tableSqlList = ignoreTableSqlList(systemXIgnoreInfoList, schemaSql);
                }
                final List<DynamicForm> originDynamicFormList = getTableFormList(tenantCode.toLowerCase(), tableSqlList);
                // 对比动态表单信息
                final List<DataMigrationDifferenceVo> dataMigrationDifferenceVoList = getDataMigrationDifference(originDynamicFormList, newDynamicFormList);
                // 校验不为空返回
                if (CollectionUtils.isNotEmpty(dataMigrationDifferenceVoList)) {

                    return JSONArray.parseArray(JSON.toJSONString(dataMigrationDifferenceVoList));
                }
            }
        } catch (GenericException e) {
            throw e;
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("数据合并元信息获取失败", e);
        }
        File bakFile = new File(bakPath);
        try {
            //备份租户数据
            final boolean bakFlag = bakData(systemXDataSource, bakPath);
            AssertUtils.isTrue(bakFlag, ThrowUtils.getThrow().serviceUnavailable(String.format("租户【%s】系统数据备份失败, 版本: %s", tenantCode, currentTag)));
            List<File> sqlFileList = sortAppendFile(unzipFile);
            if (CollectionUtils.isEmpty(sqlFileList)) {
                throw new DataMigrationException(String.format("租户【%s】系统数据文件不存在, 版本: %s", tenantCode, currentTag));
            }
            log.info("开始数据迁移");
            SqlUtils.importSchema(systemXDataSource, sqlFileList, true, sql -> transToSchemaSql(sql, systemXSchema, SYSTEM_X));
            log.info("开始刷新缓存");
            cacheRefresh.refresh();
        } catch (Exception e) {
            try {
                log.warn("开始数据回滚");
                List<File> sqlFileList = sortAppendFile(bakFile);
                SqlUtils.importSchema(systemXDataSource, sqlFileList, true, sql -> transToSchemaSql(sql, systemXSchema, SYSTEM_X));
                cacheRefresh.refresh();
            } catch (Exception e1) {
                log.error("数据回滚失败", e1);
            }
            throw new DataMigrationException(String.format("租户【%s】系统数据迁移失败, 版本: %s", tenantCode, currentTag), e);
        } finally {
            try {
                FileUtils.deleteQuietly(file);
                FileUtils.deleteDirectory(unzipFile);
                FileUtils.deleteQuietly(bakFile);
            } catch (IOException e) {
                log.error("Delete temp file fail", e);
            }
            DataSourceConfigurationUtils.closeTenantDataSources(systemXDataSource);
        }
        return Collections.emptyList();
    }

    @Override
    public String doExportData(String tenantCode, String tag, Boolean businessData, Boolean flag) {
        log.info("----------------start doExportData system------------------------");
        // 1.设置下载目录，压缩包名称，初始化数据源
        AssertUtils.isTrue(StringUtils.isNotBlank(tag), "系统版本号不可为空");
        final String businessDataMessage = businessData ? "" : "（不包含业务）";
        final String exportFilePath = PlacementDownloadUtils.TEMP_EXPORT + IdUtils.fastUuid();
        String zipFileName = DataSourceConstant.SYSTEM_X + "_" + tenantCode + "_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
        String tempPath = fileAsyInfo.getTempPath();
        final TenantDataSource systemX = DataSourceConfigurationUtils.newTenantDataSource(tenantCode.toLowerCase(), SYSTEM_X);
        final DataMigrationMetaInfo dataMigrationMetaInfo = new DataMigrationMetaInfo();
        dataMigrationMetaInfo.setTag(tag);
        try (systemX) {
            log.info("---------------- success get tenant datasource  ------------------------");
            // 2.封装忽略的表数据
            final Map<String, List<IgnoreInfo>> ignoreInfoMap = dataMigrateProperties.getIgnoreInfoMap();
            // a.构建basex忽略信息, 默认忽略tenant_info所有信息, 否则忽略定制的相关内容
            final List<IgnoreInfo> systemXIgnoreInfoList = businessData ? Lists.newArrayList(IgnoreInfo.of("tenant_info"), IgnoreInfo.of("plugin_info")) : ignoreInfoMap.get(DataSourceConstant.SYSTEM_X);
            // b.忽略basex的插件信息
            systemXIgnoreInfoList.addAll(Arrays.asList(IgnoreInfo.of("plan_industry_group"),
                    IgnoreInfo.of("plan_event_type"),
                    IgnoreInfo.of("plan_info_point_group"),
                    IgnoreInfo.of("plan_info_point"),
                    IgnoreInfo.of("plan_params"),
                    IgnoreInfo.of("plan_template_step"),
                    IgnoreInfo.of("plan_step"),
                    IgnoreInfo.of("plan_*")));
            systemXIgnoreInfoList.addAll(Arrays.asList(IgnoreInfo.of("undo_log"), IgnoreInfo.of("flyway_schema_history")));
            // c.获取插件相关的sql文件
            final List<File> pluginAppendFiles = getPluginAppendFile(exportFilePath);
            // d.忽略菜单用户类数据
            log.info("---------------- pluginAppendFiles ------------------------");
            // 3.导出数据库sql
            final String currentTag = dataMigrateProperties.getTag();
            final SchemaSql systemXSchemaSql;
            if (currentTag.equals(tag)) {
                // a.版本号为当前最新版本直接导出
                final IgnoreInfo[] systemXIgnoreInfos = systemXIgnoreInfoList.toArray(IgnoreInfo[]::new);
                systemXSchemaSql = SqlUtils.exportSchema(systemX, systemX.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, true, pluginAppendFiles, systemXIgnoreInfos);
            } else {
                // b.版本号非最新版本,从临时库导出
                systemXSchemaSql = operateTempDatabase(tenantCode, tag, businessData, exportFilePath, tempPath, systemX, businessDataMessage, currentTag, systemXIgnoreInfoList, pluginAppendFiles);
            }

            // 4.封装动态表单信息，只有在不需要业务数据的情况下才构建 表单 元信息
            if (!businessData) {
                // a.获取实际忽略的TableSql
                final List<TableSql> tableSqlList = ignoreTableSqlList(systemXIgnoreInfoList, systemXSchemaSql);
                // b.查询对应的form并构建 表单 元信息
                final List<DynamicForm> dynamicFormList = getTableFormList(tenantCode.toLowerCase(), tableSqlList);
                dataMigrationMetaInfo.setDynamicFormList(dynamicFormList);
            }

            log.info("---------------- system dynamicFormList------------------------");
            // 5.压缩，兼容没有可选业务数据的低版本 tag.txt文件
            if (tag.compareTo(dataMigrateProperties.getVersionBaseline().getBusinessData()) < 0) {
                FileUtils.writeStringToFile(new File(tempPath + SEP + "tag.txt"), tag, StandardCharsets.UTF_8, true);
            }
            FileUtils.writeStringToFile(new File(tempPath + SEP + MIGRATION_META_INFO_FILE_NAME), SqlUtils.encode(true, JSON.toJSONString(dataMigrationMetaInfo)), StandardCharsets.UTF_8, true);
            String buildFileName = fileAsyInfo.getZipPath() + SEP + zipFileName;
            ZipUtils.zipUsePass(tempPath, buildFileName, dataMigrateProperties.getPassword());
            // 6.上传压缩包
            String filmId = DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate());
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            final UploadObjectResponse uploadObjectResponse = ost.uploadObject(DownloadProperties.getTempPath(), filmId + "/" + zipFileName, buildFileName);
            log.info("租户打包租户数据资源完成");
            return uploadObjectResponse.getObject();

        } catch (Exception e) {
            e.printStackTrace();
            throw ThrowUtils.getThrow().internalServerError(String.format("打包租户【%s】系统数据%s%s版本失败", tenantCode, businessDataMessage, tag), e);
        } finally {
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
                FileUtils.deleteDirectory(new File(exportFilePath));
            } catch (IOException e) {
                log.warn("删除缓存文件夹失败");
            }
        }
    }


    @Deprecated
    @Override
    public void doImportFile(String code, byte[] bytes, String fileName) {
        String tenantName = getTenantName(code);
        //上传文件到服务器
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        String fileUrl = tempPath.getAbsolutePath() + SEP + IdUtils.fastUuid() + '-' + fileName;
        File file = new File(fileUrl);
        try {
            FileUtils.writeByteArrayToFile(file, bytes);
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("文件读取失败", e);
        }
        try {
            ZipUtils.unzip(fileUrl, fileUrl.substring(0, fileUrl.lastIndexOf(".")), dataMigrateProperties.getPassword());
//            ZipUtils.unzip(new File(fileUrl), new File(fileUrl.substring(0, fileUrl.lastIndexOf("."))));
            File unzipFile = new File(fileUrl.substring(0, fileUrl.lastIndexOf(".")));
//            File unzipFile = ZipUtils.unzip(file);
            Map<String, String> dirMap = FileViewer.getCurrentPathNames(unzipFile.getAbsolutePath());
//            Map<String, String> dirMap = new HashMap<>();
//            dirMap.put("bubble", fileUrl.substring(0, fileUrl.lastIndexOf(".")));
            Objects.requireNonNull(dirMap).forEach((k, v) -> {
                List<String> listFiles = FileViewer.getListFiles(v, "", true);
//                Map<String, String> fileMap = listFiles.stream().
//                        collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, k, unzipFile.getAbsolutePath()),
//                        (k1, k2) -> k1).toString().replace(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, k, unzipFile.getAbsolutePath()),"")
//                        );
                Map<String, String> fileMap = new HashMap<>();
                listFiles.forEach(e -> {
                    File file1 = new File(e);
                    String name = file1.getName();
                    String absolutePath = file1.getAbsolutePath();
                    fileMap.put(name, absolutePath);
                });
//                listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, k, unzipFile.getAbsolutePath()), (k1, k2) -> k1));

                ost.makeSetPublicReadableBucketPolicy(k);
                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
                        entry -> {
                            final String key = entry.getKey();
                            final String value = entry.getValue();
//                            System.out.println(k);
//                            System.out.println(value + "/" + key.substring(key.lastIndexOf(File.separator) + 1));
//                            System.out.println(key);
                            ost.uploadObject(k, key, value);
                        }, EXECUTOR_SERVICE);
            });
//            FileUtils.deleteDirectory(unzipFile);
        } catch (Exception e) {
            throw new DataMigrationException(String.format("租户【%s】文件资源迁移失败", tenantName), e);
        } finally {
            FileUtils.deleteQuietly(file);
        }
        log.info("迁移文件资源完成");
    }

    /**
     * 自调用导入系统文件
     *
     * @param code
     * @param file
     */
    private void doImportSystemFile(String code, File file) {
        String tenantName = getTenantName(code);
        //上传文件到服务器
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        File unzipFile = null;
        try {
            ZipUtils.unzip(file.getAbsolutePath(), file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")), dataMigrateProperties.getPassword());
//            ZipUtils.unzip(new File(fileUrl), new File(fileUrl.substring(0, fileUrl.lastIndexOf("."))));
            unzipFile = new File(file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")));
//            File unzipFile = ZipUtils.unzip(file);
            Map<String, String> dirMap = FileViewer.getCurrentPathNames(unzipFile.getAbsolutePath());
//            Map<String, String> dirMap = new HashMap<>();
//            dirMap.put("bubble", fileUrl.substring(0, fileUrl.lastIndexOf(".")));
            Objects.requireNonNull(dirMap).forEach((k, v) -> {
                // k 为bubble 或者 webassets 都是桶
                List<String> listFiles = FileViewer.getListFiles(v, "", true);
                Map<String, String> fileMap = listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, "", v), (k1, k2) -> k1));
                ost.makeSetPublicReadableBucketPolicy(k);
                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
                        entry -> {
                            final String key = entry.getKey();
                            final String value = entry.getValue();
                            ost.uploadObject(k, code + "/" + value + "/" + key.substring(key.lastIndexOf(File.separator) + 1), key);
                        }, EXECUTOR_SERVICE);
            });
        } catch (Exception e) {
            throw new DataMigrationException(String.format("租户【%s】文件资源迁移失败", tenantName), e);
        } finally {
            FileUtils.deleteQuietly(unzipFile);
            FileUtils.deleteQuietly(file);
        }
        log.info("迁移文件资源完成");
    }

    /**
     * 导入孪生体
     *
     * @param code
     * @param file
     */
    private void doImportTwinFile(String code, File file) {

    }

    /**
     * 导入模型
     *
     * @param code
     * @param file
     */
    private void doImportModelFile(String code, File file) {
        String tenantName = getTenantName(code);
        //上传文件到服务器
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        File unzipFile = null;
        try {
            unzipFile = new File(file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")));
//            ZipUtils.unzip(new File(fileUrl), new File(substring));
            ZipUtils.unzip(file.getAbsolutePath(), file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")), dataMigrateProperties.getPassword());
            // 模型上传，只有贴图和模型，直接定位到resource下一级
            File sourceRootPath = new File(unzipFile.getAbsolutePath(), ModelConfigProperties.getRootBucket());
            ost.makeSetPublicReadableBucketPolicy(ModelConfigProperties.getRootBucket());
            // 对resource下一级目录进行分组
            Map<String, String> sourceMap = FileViewer.getCurrentPathNames(sourceRootPath.getAbsolutePath());
            Objects.requireNonNull(sourceMap).forEach((k, v) -> {
                // k 为 model 或者 texture，v为路径
                List<String> listFiles = FileViewer.getListFiles(v, "", true);
                Map<String, String> fileMap = listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, "", v), (k1, k2) -> k1));
                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
                        entry -> {
                            final String key = entry.getKey();
                            final String value = entry.getValue();
                            ost.uploadObject(ModelConfigProperties.getRootBucket(), k + "/" + code + "/" + value + "/" + key.substring(key.lastIndexOf(File.separator) + 1), key);
                        }, EXECUTOR_SERVICE);
            });
        } catch (Exception e) {
            throw new DataMigrationException(String.format("租户【%s】模型文件资源迁移失败", tenantName), e);
        } finally {
            FileUtils.deleteQuietly(file);
            FileUtils.deleteQuietly(unzipFile);

        }
        log.info("迁移模型文件资源完成");
    }

    /**
     * 导入场景
     *
     * @param code
     * @param file
     */
    private void doImportSceneFile(String code, File file) {
        String tenantName = getTenantName(code);
        //上传文件到服务器
        File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
        if (!tempPath.exists()) {
            AssertUtils.isTrue(tempPath.mkdirs(), "文件创建失败");
        }
        File unzipFile = null;
        try {
            ZipUtils.unzip(file.getAbsolutePath(), file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")), dataMigrateProperties.getPassword());
            unzipFile = new File(file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")));
            String unzipFilePath = unzipFile.getAbsolutePath();
            // 场景上传，只有scene和effect，直接定位到resource下一级
            File sourceRootPath = new File(unzipFile.getAbsolutePath(), ModelConfigProperties.getRootBucket());
            ost.makeSetPublicReadableBucketPolicy(ModelConfigProperties.getRootBucket());
            // 对resource下一级目录进行分组
            Map<String, String> sourceMap = FileViewer.getCurrentPathNames(sourceRootPath.getAbsolutePath());
            Objects.requireNonNull(sourceMap).forEach((k, v) -> {
                // k 为 scene或者effect，v为路径
                List<String> listFiles = FileViewer.getListFiles(v, "", true);
                Map<String, String> fileMap = listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, "", v), (k1, k2) -> k1));
                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
                        entry -> {
                            final String key = entry.getKey();
                            final String value = entry.getValue();
                            ost.uploadObject(ModelConfigProperties.getRootBucket(), k + "/" + code + "/" + value + "/" + key.substring(key.lastIndexOf(File.separator) + 1), key);
                        }, EXECUTOR_SERVICE);
            });


//            Map<String, String> dirMap = FileViewer.getCurrentPathNames(unzipFile.getAbsolutePath());
//            Objects.requireNonNull(dirMap).forEach((k, v) -> {
//                List<String> listFiles = FileViewer.getListFiles(v, "", true);
//                Map<String, String> fileMap = listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, k, unzipFilePath), (k1, k2) -> k1));
//                ost.makeSetPublicReadableBucketPolicy(k);
//                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
//                        entry -> {
//                            final String key = entry.getKey();
//                            final String value = entry.getValue();
////                            System.out.println(k);
////                            System.out.println(value + "/" + key.substring(key.lastIndexOf(File.separator) + 1));
////                            System.out.println(key);
//                            ost.uploadObject(k, value + "/" + key.substring(key.lastIndexOf(File.separator) + 1), key);
//                        }, TenantMigrationService.EXECUTOR_SERVICE);
//            });

        } catch (Exception e) {
            throw new DataMigrationException(String.format("租户【%s】场景文件资源迁移失败", tenantName), e);
        } finally {
            FileUtils.deleteQuietly(file);
            FileUtils.deleteQuietly(unzipFile);
        }
        log.info("迁移场景文件资源完成");
    }

    @Override
    public String doExportFile(String tenantCode) {
        String tenantName = getTenantName(tenantCode);
        String zipFileName = DataSourceConstant.SYSTEM_X + "_" + tenantCode + "_" + DateUtils.nowDateFormatNoSeparator() + ".zip";
        FileAsyInfo fileAsyInfo = AsyncFileTask.setFileAsyInfo(zipFileName);
        String tenantTableName = SYSTEM_X + ".";
        if (!MASTER.equals(tenantCode)) {
            tenantTableName = tenantCode.toLowerCase() + "_" + tenantTableName;
        }
        String buildFileName = fileAsyInfo.getZipPath() + SEP + zipFileName;
        String tempPath = fileAsyInfo.getTempPath();
        try {
            // 迁移气泡，孪生体附件，试点动画等文件
            downloadFile(tempPath, tenantCode,tenantTableName);
            // 下载插件文件到临时目录
            //downloadPlugin(tempPath, tenantTableName);
            // 压缩这个文件夹
            ZipUtils.zip(new File(tempPath), new File(buildFileName));
//            ZipUtils.zipUsePass(tempPath, buildFileName, dataMigrateProperties.getPassword());

            String filmId = DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate());
            log.info("打包租户文件资源成功");
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            final UploadObjectResponse uploadObjectResponse = ost.uploadObject(DownloadProperties.getTempPath(), filmId + "/" + zipFileName, buildFileName);
            return uploadObjectResponse.getObject();

        } catch (Exception e) {
            throw new DataMigrationException(String.format("打包租户【%s】文件资源失败", tenantName), e);
        } finally {
            // 下载完成之后,删除临时目录
            try {
                FileUtils.deleteDirectory(fileAsyInfo.getAbsoluteFile());
            } catch (IOException e) {
                log.error("删除缓存目录失败：", e);
            }
            log.info("打包租户文件资源完成");
        }
    }


    /**
     * 下载插件文件到临时目录
     *
     * @param tempDir    临时目录
     * @param tenantName 租户名称
     * <AUTHOR>
     * @date 2022/11/10 16:34
     */
    public void downloadPlugin(String tempDir, String tenantName) {
//        List<String> codes = pluginInfoMapper.listPluginCodes(tenantName);
//        codes.forEach(code -> ost.downloadObjects(PluginConstant.BUCKET_NAME, String.format("%s/", code), tempDir));
    }


    public void downloadFile(String tempDir,String tenantCode, String tenantTableName) {
        //List<String> bucketList = new ArrayList<>();
        //bucketList.add(BubbleConstant.BUBBLE_BUCKET_NAME);
        //bucketList.add(AssetsConstant.BUCKET_NAME);
        LambdaQueryWrapper<SysFileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysFileInfo::getStatus, StatusEnum.ENABLE.getCode());
        // 查看这个租户下，所有的文件
        List<SysFileInfo> fileInfos = dataMigrationMapper.selectFileInfo(tenantTableName);
        //Map<String, String> fileIds = fileInfos.stream().collect(Collectors.toMap(SysFileInfo::getFileObjectName,e-> sysFileInfoApi.getBucketValueName(e.getFileBucket()), (k1, k2) -> k1));
        //if (Objects.isNull(fileIds)) {
        //    return;
        //}
        // 得到这个租户下用到的桶
        Set<String> buckets = fileInfos.stream().map(e -> e.getFileBucket()).collect(Collectors.toSet());
        // 工程包
        buckets.add(ProjectPackService.PROJECT_PACK);
        if (Objects.isNull(buckets)) {
            return;
        }
        buckets.forEach(bucket -> {
            ost.downloadObjects(bucket, String.format("%s/", tenantCode), tempDir + File.separator + bucket + File.separator);
        });
    }

    /**
     * 发送消息（如果当前用户不存在向超级管理员发送，否则不发送）
     *
     * @param userId     用户id
     * @param tenantName 租户名称
     * <AUTHOR>
     * @date 2022/07/13 18:02:37
     */
    private void sendMsg(Long userId, String tenantName) {
        try {
            SysUser userById = sysUserMapper.selectById(userId);
            if (Objects.isNull(userById)) {
                SysUser userByAccount = sysUserApi.getUserByAccount(IdentityContext.SUPERADMIN);
                if (Objects.nonNull(userByAccount)) {
                    realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userByAccount.getId(), String.format("迁移租户【%s】系统数据成功！", tenantName), null));
                }
            } else {
                realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(userId, String.format("迁移租户【%s】系统数据成功！", tenantName), null));
            }
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }


    /**
     * 操作临时数据库
     *
     * @param exportFilePath        导出的sql文件随机目录 data/export/uuid/
     * @param tempPath              导出的sql文件随机临时目录 data/export/uuid/temp
     * @param systemX               basex数据源
     * @param businessDataMessage   消息体
     * @param currentTag            当前版本
     * @param systemXIgnoreInfoList basex 忽略导出列表
     * @return 数据库schemaSql
     * <AUTHOR>
     * @date 2022/11/9 18:39
     */
    private SchemaSql operateTempDatabase(String tenantCode,
                                          String tag,
                                          Boolean businessData,
                                          String exportFilePath,
                                          String tempPath,
                                          TenantDataSource systemX,
                                          String businessDataMessage,
                                          String currentTag,
                                          List<IgnoreInfo> systemXIgnoreInfoList,
                                          List<File> appendFiles) throws Exception {
        tenantCode = tenantCode.toLowerCase();
        log.info("版本号【{}】当前租户在操作临时数据库", tag);
        final String systemXTmpSchema = SYSTEM_X + "_tmp";
        final String tenantSystemXTmpSchema = TenantConstant.MASTER.equals(tenantCode) ? systemXTmpSchema : tenantCode + "_" + systemXTmpSchema;
        try {
            // 创建临时库
            dataMigrationMapper.createDatabase(tenantSystemXTmpSchema);
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("打包租户系统数据%s%s版本失败，请稍后再试！", businessDataMessage, tag), e);
        }
        // 导出basex auto_api schema, 不忽略不加密,添加额外undo文件
        appendFiles.addAll(getAppendFile(businessData, tag, currentTag, exportFilePath, currentModules(flywayProperties)::contains));
        SqlUtils.exportSchema(systemX, systemX.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, false, appendFiles);
        final TenantDataSource basexTemp = DataSourceConfigurationUtils.newTenantDataSource(tenantCode, systemXTmpSchema);
        try (basexTemp) {
            // 导入到临时库,不解密
            final File tmpDir = new File(tempPath);
            final List<File> sqlFileList = sortAppendFile(tmpDir);
            SqlUtils.importSchema(basexTemp, sqlFileList, false, sql -> transToSchemaSql(sql, tenantSystemXTmpSchema, SYSTEM_X));
            // 移除原导出文件
            try {
                FileUtils.cleanDirectory(tmpDir);
            } catch (IOException e) {
                throw ThrowUtils.getThrow().internalServerError("移除原导出文件失败", e);
            }
            // 导出basex_tmp  schema 添加忽略并加密 不添加额外undo文件
            try {
                return SqlUtils.exportSchema(basexTemp, basexTemp.getSchema(), tempPath, MIGRATION_FILE_SQL_COUNT, true, true, null, systemXIgnoreInfoList.toArray(IgnoreInfo[]::new));
            } finally {
                dataMigrationMapper.dropDatabase(tenantSystemXTmpSchema);
            }
        }
    }

    /**
     * 获取动态表单列表
     *
     * @param tableSqlList TableSql列表
     * @return 动态表单列表
     * <AUTHOR>
     * @date 2022/10/18 16:32
     */
    private List<DynamicForm> getTableFormList(String tenantCode, List<TableSql> tableSqlList) {

        final String tenantDs = tenantCode + "_" + SYSTEM_X;
        return tableSqlList.stream().map(sqlMaker -> {
            final String table = sqlMaker.getName();
            final String tableName = StringUtils.strip(table, "`");
            final Optional<DynamicBusinessTable> dynamicBusinessTableOp = DynamicBusinessTable.nameOf(tableName);
            if (dynamicBusinessTableOp.isEmpty()) {
                log.warn("当前表不是动态表: {}", tableName);
                return null;
            }
            final DynamicBusinessTable dynamicBusinessTable = dynamicBusinessTableOp.get();
            final String businessTablePrefix = dynamicBusinessTable.getPrefix();
            final String businessTableName = dynamicBusinessTable.getName();
            final String code = tableName.replace(businessTablePrefix, "").toUpperCase();
            final String name;
            DynamicDataSourceContextHolder.push(tenantDs);
            try {
                name = dataMigrationMapper.selectDynamicName(businessTableName, code);
            } finally {
                DynamicDataSourceContextHolder.poll();
            }
            if (StringUtils.isBlank(name)) {
                log.warn("当前动态表没有业务记录: {}->{}", businessTableName, code);
                return null;
            }
            final String form;
            DynamicDataSourceContextHolder.push(tenantDs);
            try {
                form = dataMigrationMapper.selectDynamicForm(businessTableName, code);
            } finally {
                DynamicDataSourceContextHolder.poll();
            }
            final List<FormItem> formItems = FormUtils.parseListToForm(form);
            return new DynamicForm(name, code, formItems);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取数据合并差异信息列表
     *
     * @param originDynamicFormList 源动态表单列表
     * @param newDynamicFormList    新动态表单列表
     * @return 数据合并差异信息列表
     * <AUTHOR>
     * @date 2022/10/18 16:32
     */
    private List<DataMigrationDifferenceVo> getDataMigrationDifference
    (List<DynamicForm> originDynamicFormList, List<DynamicForm> newDynamicFormList) {

        final List<DataMigrationDifferenceVo> dataMigrationDifferenceVoList = new ArrayList<>();
        // 源表在新表里没有, 删除
        for (Iterator<DynamicForm> iterator = originDynamicFormList.iterator(); iterator.hasNext(); ) {
            DynamicForm originDynamicForm = iterator.next();
            if (newDynamicFormList.stream().noneMatch(df -> originDynamicForm.getCode().equals(df.getCode()))) {
                final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.DELETE);
                dataMigrationDifferenceVo.setTargetName(originDynamicForm.getName());
                dataMigrationDifferenceVo.setCode(originDynamicForm.getCode());
                final List<DataMigrationDifferenceDetailVo> migrationDetailVoList = originDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                dataMigrationDifferenceVo.setOrigin(migrationDetailVoList);
                dataMigrationDifferenceVoList.add(dataMigrationDifferenceVo);
                iterator.remove();
            }
        }
        if (CollectionUtils.isNotEmpty(originDynamicFormList)) {
            // 新表在源表里没有, 新增
            for (Iterator<DynamicForm> iterator = newDynamicFormList.iterator(); iterator.hasNext(); ) {
                DynamicForm newDynamicForm = iterator.next();
                if (originDynamicFormList.stream().noneMatch(df -> newDynamicForm.getCode().equals(df.getCode()))) {
                    final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                    dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.ADD);
                    dataMigrationDifferenceVo.setTargetName(newDynamicForm.getName());
                    dataMigrationDifferenceVo.setCode(newDynamicForm.getCode());
                    final List<DataMigrationDifferenceDetailVo> migrationDetailVoList = newDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                    dataMigrationDifferenceVo.setTarget(migrationDetailVoList);
                    dataMigrationDifferenceVoList.add(dataMigrationDifferenceVo);
                    iterator.remove();
                }
            }
            if (CollectionUtils.isNotEmpty(newDynamicFormList)) {
                // 剩下的就是编辑冲突
                final int size = Math.min(originDynamicFormList.size(), newDynamicFormList.size());
                for (int i = 0; i < size; i++) {

                    final DynamicForm originDynamicForm = originDynamicFormList.get(i);
                    final String originCode = originDynamicForm.getCode();
                    final List<FormItem> originFormItemList = originDynamicForm.getFormItemList();
                    final Optional<DataMigrationDifferenceVo> dataMigrationVoOp = newDynamicFormList.stream().filter(df -> {
                        // code一致
                        if (originCode.equals(df.getCode())) {

                            final List<FormItem> newFormItemList = df.getFormItemList();
                            // 表单控件数量不一致
                            if (originFormItemList.size() != newFormItemList.size()) {
                                return true;
                            }
                            for (int j = 0; j < originFormItemList.size(); j++) {

                                final FormItem originFormItem = originFormItemList.get(j);
                                final FormItem newFormItem = newFormItemList.get(j);
                                if (
                                    // 表单控件类型不一致
                                        !originFormItem.getType().equals(newFormItem.getType()) ||
                                                // 表单标签不一致
                                                !originFormItem.getLabel().equals(newFormItem.getLabel()) ||
                                                // 表单字段不一致
                                                !originFormItem.getModel().equals(newFormItem.getModel())) {
                                    return true;
                                }
                            }
                        }
                        return false;
                    }).findFirst().map(newDynamicForm -> {
                        final DataMigrationDifferenceVo dataMigrationDifferenceVo = new DataMigrationDifferenceVo();
                        // 源表详情
                        final List<DataMigrationDifferenceDetailVo> originMigrationDetailVoList = originDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                        // 新表详情
                        final List<DataMigrationDifferenceDetailVo> newMigrationDetailVoList = newDynamicForm.getFormItemList().stream().map(formItem -> new DataMigrationDifferenceDetailVo(formItem.getType(), formItem.getLabel(), formItem.getModel())).collect(Collectors.toList());
                        dataMigrationDifferenceVo.setOperation(DataMigrationDifferenceOperation.EDIT);
                        dataMigrationDifferenceVo.setOriginName(originDynamicForm.getName());
                        dataMigrationDifferenceVo.setTargetName(newDynamicForm.getName());
                        dataMigrationDifferenceVo.setCode(newDynamicForm.getCode());
                        dataMigrationDifferenceVo.setOrigin(originMigrationDetailVoList);
                        dataMigrationDifferenceVo.setTarget(newMigrationDetailVoList);
                        return dataMigrationDifferenceVo;
                    });
                    dataMigrationVoOp.ifPresent(dataMigrationDifferenceVoList::add);
                }
            }
        }

        return dataMigrationDifferenceVoList;
    }

    /**
     * 异步导出租户文件
     *
     * @param code          租户编号
     * @param saveDir       导出目录
     * @param cyclicBarrier 多线程通知api
     * <AUTHOR>
     * @date 2023/4/17 10:32
     */
    private boolean asyncExportFile(String serverName, String code, String saveDir, CyclicBarrier cyclicBarrier) {
        // 文件导出
        AsyncUtils.asyncExecutorBean(() -> {
            try {
                final String zipPath;
                if (SYSTEM_SERVER.equals(serverName)) {
                    zipPath = doExportFile(code);
                } else if (TWIN_SERVER.equals(serverName)) {
                    zipPath = null;
                } else {
                    DataMigrationApi serverApi = FeignUtils.createClient(DataMigrationApi.class, serverName);
                    zipPath = serverApi.doExportFile(code);
                }
                if (StringUtils.isNotBlank(zipPath)) {
//                    ost.downloadObjects(DownloadProperties.getTempPath(), zipPath, saveDir);
//                    downloadUtils.downloadHtml(saveDir,sampleOsrUrl + DownloadProperties.getTempPath(), zipPath);
                    ost.downloadObject(DownloadProperties.getTempPath(), zipPath, saveDir + File.separator + zipPath.substring(zipPath.indexOf("/") + 1));
                }
                cyclicBarrier.await();
            } catch (BrokenBarrierException | InterruptedException e) {
                e.printStackTrace();
            }
        }, ExecutorServiceFactory.createThreadPool(String.format("tenant-migration-downFile-%s-thread", code), 5, 10, new ArrayBlockingQueue<>(100)));
        return true;
    }

    /**
     * 异步导入租户文件
     *
     * @param code  租户编号
     * @param latch 多线程通知api
     * <AUTHOR>
     * @date 2023/4/17 10:32
     */
    private void asyncImportFile(String code, File file, CountDownLatch latch) {
        AsyncUtils.asyncExecutorBean(() -> {
            String tenantName = getTenantName(code);
            File tempPath = new File(PlacementDownloadUtils.DOWNLOAD_TEMP_PATH);
            if (!tempPath.exists()) {
                AssertUtils.isTrue(tempPath.mkdirs(), "目录创建失败");
            }
            File unzipFile = null;
            try {
                ZipUtils.unzip(file.getAbsolutePath(), file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")), dataMigrateProperties.getPassword());
                unzipFile = new File(file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".")));
                String prefixPath = unzipFile.getAbsolutePath();
                String level2Bucket = unzipFile.getName();
                // k 为bubble 或者 webassets 都是桶
                List<String> listFiles = FileViewer.getListFiles(unzipFile.getAbsolutePath(), "", true);
                Map<String, String> fileMap = listFiles.stream().collect(Collectors.toMap(s -> s, s -> PlacementDownloadUtils.getFilmId(s, "", prefixPath), (k1, k2) -> k1));
                ost.makeSetPublicReadableBucketPolicy(ModelConfigProperties.getRootBucket());
                AsyncUtils.asyncExecutor(new ArrayList<>(fileMap.entrySet()),
                        entry -> {
                            final String key = entry.getKey();
                            final String value = entry.getValue();
                            log.info("上传：" + key);
                            ost.uploadObject(ModelConfigProperties.getRootBucket(), code + "/" + level2Bucket + "/" + value + "/" + key.substring(key.lastIndexOf(File.separator) + 1), key);
                        }, EXECUTOR_SERVICE);
            } catch (Exception e) {
                throw new DataMigrationException(String.format("租户【%s】文件资源迁移失败", tenantName), e);
            } finally {
                latch.countDown();
                if(Objects.nonNull(unzipFile) && unzipFile.exists()){
                    FileUtils.deleteQuietly(unzipFile);
                }
                if (file.exists()){
                    FileUtils.deleteQuietly(file);
                }
            }
        }, ExecutorServiceFactory.createThreadPool(String.format("tenant-migration-upload-file-【%s】-thread", code), 5, 10, new ArrayBlockingQueue<>(100)));
    }

    /**
     * 异步导入租户文件
     *
     * @param code  租户编号
     * @param latch 多线程通知api
     * <AUTHOR>
     * @date 2023/4/17 10:32
     */
    private void asyncImportFile(String serverName, String code, File file, CountDownLatch latch) {
        //AsyncUtils.asyncExecutorBean(() -> {
        try {
            if (serverName.equals(DataSourceConstant.SYSTEM_X)) {
                this.doImportSystemFile(code, file);
            } else if (serverName.equals(DataSourceConstant.MODEL_X)) {
                this.doImportModelFile(code, file);
            } else if (serverName.equals(DataSourceConstant.SCENE_X)) {
                this.doImportSceneFile(code, file);
            } else if (serverName.equals(DataSourceConstant.TWIN_X)) {
                this.doImportTwinFile(code, file);
            } else {
                log.error("未定义的租户文件导入标识：{}", serverName);
            }

            //MultipartFile file = MultipartFileUtil.getMultipartFile(zip);
            //if (null == serverName) {
            //    // 1.system服务
            //    this.doImportFile(code, file, fileName);
            //} else {
            //    // 2.其他各个服务文件
            //    DataMigrationApi client = FeignUtils.createClient(DataMigrationApi.class, serverName);
            //    client.doImportFile(code, file, fileName);
            //}
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //latch.countDown();
        }
        //}, ExecutorServiceFactory.createThreadPool(String.format("tenant-migration-upload-file-【%s】-thread", code), 5, 10, new ArrayBlockingQueue<>(100)));
    }

//    private void asyncImportFiles(String serverName, String code, byte[] bytes, String fileName, CyclicBarrier cyclicBarrier) {
//        AsyncUtils.asyncExecutorBean(() -> {
//        if (null == serverName) {
//            this.doImportFile(code, bytes, fileName);
//        } else {
//            // 2.其他各个服务文件
//            DataMigrationApi client = FeignUtils.createClient(DataMigrationApi.class, serverName);
//            client.doImportFile(code, bytes, fileName);
//        }
//        }, ExecutorServiceFactory.createThreadPool(String.format("tenant-migration-upload-file-【%s】-thread", code), 5, 10, new ArrayBlockingQueue<>(100)));
//    }


    /**
     * 异步导出租户数据
     *
     * @param code          租户编号
     * @param saveDir       导出目录
     * <AUTHOR>
     * @date 2023/4/17 10:32
     */

    private void asyncExportData(String serverName, String code, String tag, Boolean businessData, String saveDir, Boolean flag) {
        final Map<String, String> feignConfig = moduleDataSourceProps.getFeignConfig();
        if (SYSTEM_SERVER.equals(serverName)) {
            final String path = doExportData(code, tag, businessData, flag);
            ost.downloadObject(DownloadProperties.getTempPath(), path, saveDir + File.separator + path.substring(path.indexOf("/") + 1));
            log.info("---------------------path: {}---------", path);
        } else {
            final String moduleName = serverName.substring(0, serverName.indexOf("-"));
            final String server = feignConfig.get(moduleName);
            if (StringUtils.isBlank(server)) {
                return;
            }
            final DataMigrationApi serverApi = FeignUtils.createClient(DataMigrationApi.class, server, DataMigrationApi.API_PATH + "/" + moduleName);
            final String path = serverApi.doExportData(code, tag, businessData, flag);
            ost.downloadObject(DownloadProperties.getTempPath(), path, saveDir + File.separator + path.substring(path.indexOf("/") + 1));
            log.info("---------------------serverName: {}---------path: {}-----------", serverName, path);
        }
    }

    /**
     * 异步导入租户文件
     *
     * @param code          租户编号
     * <AUTHOR>
     * @date 2023/4/17 10:32
     */
    private CompletableFuture<Object> asyncImportData(String serverName, String code, byte[] bytes, String fileName) {
        final String appName = SpringEnvUtils.getAppName();
        final Map<String, String> feignConfig = moduleDataSourceProps.getFeignConfig();
        return AsyncUtils.asyncExecutorBean(() -> {
            if (appName.equals(serverName)) {
                // 1.system服务
                doImportData(code, bytes, fileName);
            } else {
                // 2.其他服务
                final String moduleName = serverName.substring(0, serverName.indexOf("-"));
                final String server = feignConfig.get(moduleName);
                if (StringUtils.isBlank(server)) {
                    return null;
                }
                DataMigrationApi client = FeignUtils.createClient(DataMigrationApi.class, server, DataMigrationApi.API_PATH + "/" + moduleName);
                client.doImportData(code, bytes, fileName);
            }
            return null;
        }, DataMigrationServiceImpl.EXECUTOR_SERVICE);
    }

    private void downFileZipSendFile(FileAsyInfo fileAsyInfo, String zipFileName, String tenantName, Long id) throws Exception {
        // 重写压缩并上传压缩包
        String buildFileName = fileAsyInfo.getTempPath() + SEP + zipFileName;
        ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
        ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
        ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + "/" + zipFileName, buildFileName);
//        ost.uploadObject(DownloadProperties.getTempPath(), buildFileName, DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()));
        realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(id, java.lang.String.format("租户【%s】文件资源打包成功！请在%s日之前下载，过期后，文件将被删除", tenantName, DateTimeFormatter.ofPattern("yyyy-MM-dd").format(fileAsyInfo.getExpiredDate())), fileAsyInfo.getDownLoadPath()));
    }

    /**
     * 将文件打包到指定文件
     */
    private void feignDownFileZipSendFile(FileAsyInfo fileAsyInfo, String zipFileName, DataMigrationParam param) throws Exception {
        // 重写压缩并上传压缩包
        String buildFileName = param.getPackagePath() + SEP + zipFileName;
        Path paths = Paths.get(param.getPackagePath());
        Files.createDirectories(paths);
        ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
//        ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName, buildFileName);
//        return DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName;
    }

    private String feignDownDataZipSaveFile(FileAsyInfo fileAsyInfo, String zipFileName, String tag, DataMigrationParam param) throws Exception {
        // 5.压缩，兼容没有可选业务数据的低版本 tag.txt文件
        FileUtils.writeStringToFile(new File(fileAsyInfo.getZipPath() + SEP + "tag.txt"), tag, StandardCharsets.UTF_8, true);
        String buildFileName = param.getPackagePath() + SEP + zipFileName;
        Path paths = Paths.get(param.getPackagePath());
        Files.createDirectories(paths);
        ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
        Map<String, String> edtapMap = param.getEdtapMap();
        if (edtapMap != null && edtapMap.get("projectName") != null) {
            ZipUtils.zip(new File(param.getPackagePath()), new File(param.getPackagePath() + SEP + edtapMap.get("projectName")));
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + edtapMap.get("projectName"), param.getPackagePath() + SEP + edtapMap.get("projectName"));
            return DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + edtapMap.get("projectName");
        } else {
            ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
            ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName, param.getPackagePath() + SEP + zipFileName);
            return DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName;
        }

//        ZipUtils.zip(new File(param.getPackagePath()), new File(param.getPackagePath() + PlacementDownloadUtils.SEP + zipFileName));
//      return DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName;
    }

    private void downDataZipSaveFile(FileAsyInfo fileAsyInfo, String zipFileName, Long id, String tenantName, String tag, String businessDataMessage) throws Exception {
        // 5.压缩，兼容没有可选业务数据的低版本 tag.txt文件
        FileUtils.writeStringToFile(new File(fileAsyInfo.getZipPath() + SEP + "tag.txt"), tag, StandardCharsets.UTF_8, true);
        String buildFileName = fileAsyInfo.getTempPath() + SEP + zipFileName;
        ZipUtils.zipUsePass(fileAsyInfo.getZipPath(), buildFileName, dataMigrateProperties.getPassword());
        // 文件路径  桶名称  存放路径
        ost.makeSetPublicReadableBucketPolicy(DownloadProperties.getTempPath());
        ost.uploadObject(DownloadProperties.getTempPath(), DateTimeFormatter.ofPattern("yyyyMMdd").format(fileAsyInfo.getExpiredDate()) + SEP + zipFileName, buildFileName);
//        ost.uploadObject(DownloadProperties.getTempPath(), filmId + "/" + zipFileName, buildFileName);
//        new UploadObjectResponse(this.ossClient.putObject(bucket, object, file, objectMetadata), bucket, object);
        // 6.消息通知下载
        realTimeMessageService.saveMessage(SysRealTimeMessage.buildSysRealTimeMessage(id, java.lang.String.format("打包租户【%s】系统数据%s%s版本成功！请在%s日之前下载，过期后，文件将被删除", tenantName, businessDataMessage, tag, DateTimeFormatter.ofPattern("yyyy-MM-dd").format(fileAsyInfo.getExpiredDate())), fileAsyInfo.getDownLoadPath()));
        log.info("打包租户数据资源完成");
    }

    /**
     * 新建systemx数据源
     *
     * @param name          租户名称
     * @param code          租户编号
     * @param migrationType 类型
     * <AUTHOR>
     * @date 2023/4/13 15:55
     */
    @Override
    public void createDatabase(String name, String code, Integer migrationType) {
    }

    @Override
    public String generateDataMigrationFileUrl(@RequestParam(value = "code",required = false) String code){
        if (StringUtils.isEmpty(code)) {
            code = TenantUtils.getTenantByRequest();
        }
        return ost.getPresignedObjectUrl(ModelConfigProperties.getRootBucket(),code,(int)TimeUnit.DAYS.toSeconds(1));
    }
}

