<!--
 * @Description: 园区预览-顶部
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-12 19:41:51
 * @LastEditors: hasaiki
 * @LastEditTime: 2025-02-05 11:09:32
-->
<template>
  <div class="preview-header">
    <div class="campus-name" :title="props.name">
      <img :src="systemLogo" class="header-logo" alt="header-logo" />
      <div class="header-title">{{ props.name }}</div>
    </div>
    <div class="content-wrapper">
      <div class="content">
        <div v-for="item in funcList" :key="item.code" class="content-item" :class="item.code === activeFunc.code ? 'active' : ''" @click="handleClick(item)">
          <span class="name" :class="item.code === activeFunc.code ? 'active' : ''">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, markRaw } from 'vue';
import TwinsObjectPut from './funcModule/TwinsObjectPut.vue'; // 孪生对象摆点
import TwinsDataSearch from './funcModule/TwinsDataSearch.vue'; // 孪生数据查询
import AssetsDataPut from './funcModule/AssetsDataPut.vue'; // 资产数据摆点
import MmdPointTransform from './funcModule/MmdPointTransform.vue'; // 模模搭点位转换
import CadPointTransform from './funcModule/CadPointTransform.vue'; // cad点位转换
import PointCal from './funcModule/PointCal.vue'; // 点位校准
import { useSceneStore } from '@/store/scene';
import headerLogo from '@/assets/img/header/header-logo.svg';
import axios from 'axios';
const tenant = axios.defaults.headers.common['Tenant'];
// 系统logo
const systemLogo = ref(sessionStorage.getItem(`${tenant}_XI_TONG_LOGO`) || headerLogo);
// 系统标题
const systemName = ref(sessionStorage.getItem(`${tenant}_XI_TONG_BIAO_TI`) || '');
// 修改link-icon
let favicon = document.querySelector('link[rel="icon"]');
if (favicon) {
  // @ts-ignore
  favicon.href = systemLogo.value;
}
// 项目名称
const projectName = ref(sessionStorage.getItem('PROJECT_NAME') || '');
if (projectName.value) {
  document.title = `${systemName.value} - ${projectName.value}`;
} else {
  document.title = `${systemName.value}`;
}
const sceneStore = useSceneStore();
const emit = defineEmits(['currentFunc']);
const props = defineProps({
  name: {
    type: String,
    default: '',
  },
});
// 功能列表
const funcList = ref([
  {
    code: 'twinSearch',
    name: '孪生数据查询',
    component: markRaw(TwinsDataSearch),
  },
  {
    code: 'twinPut',
    name: '孪生对象摆点',
    component: markRaw(TwinsObjectPut),
  },
  {
    code: 'assetPut',
    name: '资产数据摆点',
    component: markRaw(AssetsDataPut),
  },
  {
    code: 'mmdPoint',
    name: '模模搭点位转换',
    component: markRaw(MmdPointTransform),
  },
  {
    code: 'cadPoint',
    name: 'CAD点位转换',
    component: markRaw(CadPointTransform),
  },
  {
    code: 'pointCal',
    name: '点位校准',
    component: markRaw(PointCal),
  },
]);
// 选中的功能
const activeFunc = ref<any>(funcList.value[1]);
// 默认选中-孪生对象摆点
emit('currentFunc', funcList.value[1]);
// 点击菜单-切换功能菜单
const handleClick = (item: any) => {
  // 场景未加载完毕，禁止点击
  if (!sceneStore.sceneLoaded) {
    return;
  }
  activeFunc.value = item;
  emit('currentFunc', item);
};
</script>
<style scoped lang="scss">
.preview-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-width: 1200px;
  height: 54px;
  overflow: auto hidden;
  background: var(--second-bg-color);
  border-bottom: 1px solid var(--header-border-color);

  .campus-name {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 280px;
    min-width: 280px;
    height: 54px;
    padding: 0 0 0 24px;
    overflow: hidden;

    .header-logo {
      width: auto;
      height: 26px;
      margin-right: 8px;
    }

    .header-title {
      flex: 1;
      height: 54px;
      overflow: hidden;
      font-family: 'PingFang Bold';
      font-size: 16px;
      font-weight: 500;
      line-height: 54px;
      color: var(--primary-text-color);
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .content-wrapper {
    flex: 1;
    height: 100%;
    margin-left: -280px;

    .content {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      margin: 0 auto;

      .content-item {
        position: relative;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        min-width: 60px;
        height: 100%;
        margin-right: 32px;
        overflow: hidden;
        cursor: pointer;

        &:hover {
          .name {
            color: var(--primary-text-color);
          }
        }

        &.active::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 3px;
          content: '';
          background: var(--theme-color);
        }

        &:last-child {
          margin-right: 0;
        }

        .name {
          color: var(--secnd-text-color);

          &.active {
            color: var(--primary-text-color);
          }
        }
      }
    }
  }
}
</style>
