2025-08-27 15:05:46.328 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.333 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-27 15:05:46.503 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.507 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-27 15:05:46.618 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.619 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-27 15:05:46.666 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-27 15:05:46.698 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-27 15:05:46.698 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-27 15:05:46.698 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-27 15:05:46.698 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-27 15:05:46.740 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.776 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-27 15:05:46.812 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.812 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-27 15:05:46.812 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-27 15:05:46.817 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.817 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-27 15:05:46.819 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.820 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-27 15:05:46.822 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.823 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-27 15:05:46.825 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.828 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.829 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-27 15:05:46.843 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.846 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.854 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.856 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.864 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.878 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:05:46.880 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-27 15:05:48.997 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-27 15:05:55.470 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation statistics: 
SqlTransformationEngine [MySQL-to-达梦] Statistics:
  Transformations: 0 (Success: 0, Failed: 0, Success Rate: 0.00%)
  Cache: Size=0, Hits=0, Misses=0, Hit Rate=100.00%
  Strategies: 10 registered
  Memory: Estimated Size=0 bytes
2025-08-27 15:05:55.574 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: sys_menu -> The current spring environment is unavailable
2025-08-27 15:05:55.720 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `sys_menu` (`id`, `pid`, `pids`, `name`, `code`, `type`, `icon`, `router`, `component`, `permission`, `application`, `open_type`, `visible`, `link`, `redirect`, `weight`, `sort`, `remark`, `default_cam_info`, `special`, `plugin_id`, `micro_app_id`, `sys_category_id`, `status`, `create_user`, `update_user`, `iframe_type`, `with_token`) VALUES (1960591841450586114, 1676546065498685442, '[0],[1676545876796948481],[1676546065498685442],', '孪生体查询用户映射', 'LSDX-GET-USERMAPPING', 2, 'icon-zuzhi', '/', '/', 'user:list-mapping', 'XIANG_MU_ZI_YUAN', 1, 'Y', '', '', 1, 100, NULL, NULL, 'normal', NULL, NULL, NULL, 0, 1, NULL, '0', '0');]
Result:   [MERGE INTO "sys_menu" t USING (SELECT 1960591841450586114 AS id, 1676546065498685442 AS pid, '[0],[1676545876796948481],[1676546065498685442],' AS pids, '孪生体查询用户映射' AS name, 'LSDX-GET-USERMAPPING' AS code, 2 AS type, 'icon-zuzhi' AS icon, '/' AS router, '/' AS component, 'user:list-mapping' AS permission, 'XIANG_MU_ZI_YUAN' AS application, 1 AS open_type, 'Y' AS visible, '' AS link, '' AS redirect, 1 AS weight, 100 AS sort, NULL AS remark, NULL AS default_cam_info, 'normal' AS special, NULL AS plugin_id, NULL AS micro_app_id, NULL AS sys_category_id, 0 AS status, 1 AS create_user, NULL AS update_user, '0' AS iframe_type, '0' AS with_token FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.pid = s.pid, t.pids = s.pids, t.name = s.name, t.code = s.code, t.type = s.type, t.icon = s.icon, t.router = s.router, t.component = s.component, t.permission = s.permission, t.application = s.application, t.open_type = s.open_type, t.visible = s.visible, t.link = s.link, t.redirect = s.redirect, t.weight = s.weight, t.sort = s.sort, t.remark = s.remark, t.default_cam_info = s.default_cam_info, t.special = s.special, t.plugin_id = s.plugin_id, t.micro_app_id = s.micro_app_id, t.sys_category_id = s.sys_category_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user, t.iframe_type = s.iframe_type, t.with_token = s.with_token WHEN NOT MATCHED THEN INSERT (id, pid, pids, name, code, type, icon, router, component, permission, application, open_type, visible, link, redirect, weight, sort, remark, default_cam_info, special, plugin_id, micro_app_id, sys_category_id, status, create_user, update_user, iframe_type, with_token) VALUES (s.id, s.pid, s.pids, s.name, s.code, s.type, s.icon, s.router, s.component, s.permission, s.application, s.open_type, s.visible, s.link, s.redirect, s.weight, s.sort, s.remark, s.default_cam_info, s.special, s.plugin_id, s.micro_app_id, s.sys_category_id, s.status, s.create_user, s.update_user, s.iframe_type, s.with_token);]
2025-08-27 15:05:55.763 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 643, 转换后SQL长度: 2002
2025-08-27 15:21:32.884 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: sys_role_menu -> The current spring environment is unavailable
2025-08-27 15:21:32.888 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `sys_role_menu` (`id`, `role_id`, `menu_id`, `status`, `create_user`, `update_user`)
        VALUES (1960600407145787394, 1738144969088643073, 1960591841450586114, 0, 1, NULL);]
Result:   [MERGE INTO "sys_role_menu" t USING (SELECT 1960600407145787394 AS id, 1738144969088643073 AS role_id, 1960591841450586114 AS menu_id, 0 AS status, 1 AS create_user, NULL AS update_user FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.role_id = s.role_id, t.menu_id = s.menu_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user WHEN NOT MATCHED THEN INSERT (id, role_id, menu_id, status, create_user, update_user) VALUES (s.id, s.role_id, s.menu_id, s.status, s.create_user, s.update_user);]
2025-08-27 15:21:32.888 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 189, 转换后SQL长度: 534
2025-08-27 15:22:41.846 [AWT-EventQueue-0] WARN  c.u.x.c.s.t.s.MergeIntoTransformationStrategy - Failed to retrieve column metadata for table: sys_role_menu -> The current spring environment is unavailable
2025-08-27 15:22:41.851 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation completed in engine [MySQL-to-达梦].
Original: [REPLACE INTO `sys_role_menu` (`id`, `role_id`, `menu_id`, `status`, `create_user`, `update_user`)
        VALUES (1960601581890646019, 1745640004528422914, 1960591841450586114, 0, 1, NULL);]
Result:   [MERGE INTO "sys_role_menu" t USING (SELECT 1960601581890646019 AS id, 1745640004528422914 AS role_id, 1960591841450586114 AS menu_id, 0 AS status, 1 AS create_user, NULL AS update_user FROM DUAL) s ON (t.id = s.id) WHEN MATCHED THEN UPDATE SET t.role_id = s.role_id, t.menu_id = s.menu_id, t.status = s.status, t.create_user = s.create_user, t.update_user = s.update_user WHEN NOT MATCHED THEN INSERT (id, role_id, menu_id, status, create_user, update_user) VALUES (s.id, s.role_id, s.menu_id, s.status, s.create_user, s.update_user);]
2025-08-27 15:22:41.851 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换完成，原始SQL长度: 189, 转换后SQL长度: 534
2025-08-27 15:37:14.626 [Thread-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.593 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.644 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.663 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-27 15:56:49.664 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.665 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.665 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-27 15:56:49.666 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-27 15:56:49.666 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-27 15:56:49.667 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.667 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-27 15:56:49.668 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.669 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-27 15:56:49.670 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.670 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-27 15:56:49.671 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.672 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.672 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-27 15:56:49.686 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.691 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.702 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.703 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.715 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.717 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-27 15:56:49.719 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-27 15:56:49.719 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
