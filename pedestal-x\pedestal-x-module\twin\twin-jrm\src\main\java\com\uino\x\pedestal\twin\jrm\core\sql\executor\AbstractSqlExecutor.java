package com.uino.x.pedestal.twin.jrm.core.sql.executor;


import com.uino.x.common.pojo.util.IdUtils;
import com.uino.x.common.tool.base.CollectionUtils;
import com.uino.x.common.tool.base.StringUtils;
import com.uino.x.common.tool.spring.SpringEnvUtils;
import com.uino.x.pedestal.twin.jrm.core.domain.JsonSql;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.jrm.core.exception.SqlErrorExpEnumFactory;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.AbstractSqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.definition.SqlDefinition;
import com.uino.x.pedestal.twin.jrm.core.sql.transaction.SqlTransaction;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抽象的sql执行器
 *
 * <AUTHOR>
 * @date 2021/4/21 12:01
 */
public abstract class AbstractSqlExecutor implements SqlExecutor {

    /**
     * 数据库连接缓存
     */
    public static final Map<String, Connection> CONNECTION_MAP = new ConcurrentHashMap<>();
    /**
     * 数据库执行缓存
     */
    public static final Map<String, Statement> STATEMENT_MAP = new ConcurrentHashMap<>();

    /**
     * 初始查询sql描述缓存
     */
    protected static final Map<RequestType, AbstractSqlDefinition> INIT_SELECT_MAP = new ConcurrentHashMap<>(8);

    /**
     * 初始化查询{@link JsonSql}
     */
    protected static final Map<RequestType, List<JsonSql>> INIT_SELECT_LIST_MAP = new ConcurrentHashMap<>(8);

    static {
        final List<JsonSql> creates = new ArrayList<>();
        final List<JsonSql> drops = new ArrayList<>();
        final List<JsonSql> alters = new ArrayList<>();
        if (SpringEnvUtils.isMysql()) {
            creates.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),

                    new JsonSql(IdUtils.getId(), "create", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/name", "`?`", 2, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column", "[]", 3, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/name", "`?`", 4, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/type", " ", 5, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/length", "(?)", 6, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/required", " ", 7, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/defaultValue", "default ?", 8, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/comment", "comment '?'", 9, 1),

                    new JsonSql(IdUtils.getId(), "create", "/index", "", 1, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/type", "", 2, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/name", "`?` on", 3, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/tableName", "`?`", 4, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/columnName", "(`?`)", 5, 1)
            ));
            drops.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),

                    new JsonSql(IdUtils.getId(), "drop", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "drop", "/table/name", "`?`", 2, 1),

                    new JsonSql(IdUtils.getId(), "drop", "/index", "index", 1, 1),
                    new JsonSql(IdUtils.getId(), "drop", "/index/name", "`?` on", 2, 1),
                    new JsonSql(IdUtils.getId(), "drop", "/index/tableName", "`?`", 3, 1)
            ));
            alters.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/name", "`?`", 2, 1),

//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index", "add", 3, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/type", " ", 4, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/name", " ", 5, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/column", "(`?`)", 6, 1),
////
//                    new JsonSql(IdUtils.getId(), "alter", "/table/-index", "drop index", 3, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/-index/name", " ", 4, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/+column", "add column", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/name", "`?`", 4, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/type", " ", 5, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/length", "(?)", 6, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/required", " ", 7, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/defaultValue", "default ?", 8, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/comment", "comment '?'", 9, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/-column", "drop column", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/-column/name", "`?`", 4, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/~column", "change column", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/old", "`?`", 4, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/name", "`?`", 5, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/type", " ", 6, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/length", "(?)", 7, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/required", " ", 8, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/defaultValue", "default ?", 9, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/comment", "comment '?'", 10, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/after", "after `?`", 11, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/newTable", "rename to `?`", 3, 1)));
        } else if (SpringEnvUtils.isDameng()) {
            creates.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),

                    new JsonSql(IdUtils.getId(), "create", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/name", "\"?\"", 2, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column", "[]", 3, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/name", "\"?\"", 4, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/type", " ", 5, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/length", "(?)", 6, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/required", " ", 7, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/defaultValue", "default ?", 8, 1),
                    new JsonSql(IdUtils.getId(), "create", "/table/column[]/comment", "comment '?'", 9, 1),

                    new JsonSql(IdUtils.getId(), "create", "/index", "", 1, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/type", "", 2, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/name", "\"?\" on", 3, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/tableName", "\"?\"", 4, 1),
                    new JsonSql(IdUtils.getId(), "create", "/index/columnName", "(\"?\")", 5, 1)
            ));
            drops.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),

                    new JsonSql(IdUtils.getId(), "drop", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "drop", "/table/name", "\"?\"", 2, 1),

                    new JsonSql(IdUtils.getId(), "drop", "/index", "index if exists", 1, 1),
                    new JsonSql(IdUtils.getId(), "drop", "/index/name", "\"?\"", 2, 1)
            ));
            alters.addAll(Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table", "table", 1, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/name", "\"?\"", 2, 1),

//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index", "add", 3, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/type", " ", 4, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/name", " ", 5, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/+index/column", "(\"?\")", 6, 1),
//
//                    new JsonSql(IdUtils.getId(), "alter", "/table/-index", "drop index", 3, 1),
//                    new JsonSql(IdUtils.getId(), "alter", "/table/-index/name", " ", 4, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/+column", "add column", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/name", "\"?\"", 4, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/type", " ", 5, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/length", "(?)", 6, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/required", " ", 7, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/defaultValue", "default ?", 8, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/+column/comment", ";comment on column \"{/table}\".\"{/kind/+column/name}\" is '?'", 9, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/-column", "drop column", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/-column/name", "\"?\"", 4, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/~column", "modify", 3, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/old", "\"?\"", 4, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/type", " ", 5, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/length", "(?)", 6, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/required", " ", 7, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/defaultValue", "default ?", 8, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/comment", ";comment on column \"{/table}\".\"{/kind/~column/old}\" is '?'", 9, 1),
                    new JsonSql(IdUtils.getId(), "alter", "/table/~column/name", ";alter table \"{/table}\" rename column \"{/kind/~column/old}\" to \"?\";", 10, 1),

                    new JsonSql(IdUtils.getId(), "alter", "/table/newTable", "rename to \"?\"", 3, 1)));
        }


        INIT_SELECT_LIST_MAP.put(RequestType.CREATE, creates);
        INIT_SELECT_LIST_MAP.put(RequestType.ALTER, alters);
        INIT_SELECT_LIST_MAP.put(RequestType.DROP, drops);
        INIT_SELECT_LIST_MAP.put(RequestType.TRUNCATE,
                Arrays.asList(new JsonSql(IdUtils.getId(), "base", "/db", " ", 0, 1),
                        new JsonSql(IdUtils.getId(), "base", "/table", " ", 0, 1)));
    }

    /**
     * sql事务
     */
    protected final SqlTransaction sqlTransaction;

    /**
     * sql描述
     */
    protected final SqlDefinition sqlDefinition;

    /**
     * 构造方法
     *
     * @param sqlDefinition sql描述
     * @throws SQLException sql异常
     * <AUTHOR>
     * @date 2021/4/21 12:01
     */
    public AbstractSqlExecutor(SqlDefinition sqlDefinition) throws SQLException {

        this.sqlDefinition = sqlDefinition;
        sqlTransaction = initSqlTransaction();
    }

    /**
     * 初始化{@link SqlTransaction}
     *
     * @return sql事务
     * @throws SQLException sql异常
     * <AUTHOR>
     * @date 2021/9/21 12:01
     */
    protected abstract SqlTransaction initSqlTransaction() throws SQLException;

    @Override
    public SqlTransaction sqlTransaction() {
        return sqlTransaction;
    }

    @Override
    public String update() {
        int count = 0;
        try {
            sqlTransaction.begin(Connection.TRANSACTION_READ_COMMITTED);
            final Statement statement = sqlTransaction.getStatement();
            final String sql = sqlDefinition.value();
            for (String s : sql.split(";")) {
                if (StringUtils.isNotBlank(s)) {
                    statement.addBatch(s);
                    count++;
                }
            }
            sqlTransaction.getStatement().executeBatch();
            sqlTransaction.commit();
        } catch (Exception e) {
            failureSqlExecutor(e);
            SqlErrorExpEnumFactory.doThrow(e);
        } finally {

            sqlTransaction.close();
        }

        return String.valueOf(count);
    }

    private void failureSqlExecutor(Exception e) {
        ((AbstractSqlDefinition) this.sqlDefinition).clearCache();
        this.sqlTransaction.rollback(e);
//        final DataSource dataSource = sqlDefinition.dataSource();
//        final JsonDefinition jsonDefinition = sqlDefinition.jsonDefinition();
//        final JsonSerialize serialize = jsonDefinition.serialize();
//        final RequestType identify = sqlDefinition.identify();
//        if (RequestType.ALTER.equals(identify)) {
//            // 新增字段
//            // 删除字段
//            // 修改字段名
//            // 修改字段顺序
//            final JsonDefinition jd = DefaultJsonDefinition.of("", serialize);
//            final SqlDefinition sd = SqlDefinitionFactory.get(dataSource, jd, RequestType.ALTER);
//            DefaultSqlExecutor.of(sd).run();
//        } else if (RequestType.CREATE.equals(identify)) {
//            // 删除对应表
//            final JsonDefinition jd = DefaultJsonDefinition.of("", serialize);
//            final SqlDefinition sd = SqlDefinitionFactory.get(dataSource, jd, RequestType.DROP);
//            DefaultSqlExecutor.of(sd).run();
//        } else if (RequestType.DROP.equals(identify)) {
//            // 创建对应表
//            // 查询原表
//            final JsonDefinition jd = DefaultJsonDefinition.of("", serialize);
//            final SqlDefinition sd = SqlDefinitionFactory.get(dataSource, jd, RequestType.CREATE);
//            DefaultSqlExecutor.of(sd).run();
//        }
    }

    @Override
    public List<Map<String, ?>> queryList() {

        ResultSet resultSet;
        List<Map<String, ?>> result = new ArrayList<>();
        final Connection connection = sqlTransaction.getConnection();
        try {

            sqlTransaction.begin(Connection.TRANSACTION_READ_COMMITTED);
            resultSet = sqlTransaction.getStatement().executeQuery(sqlDefinition.value());
            result = wrapperResultSet(resultSet);
            connection.commit();
        } catch (Exception e) {

            sqlTransaction.rollback(e);
        } finally {

            sqlTransaction.close();
        }

        return result;
    }

    @Override
    public String query() {

        return wrapperJsonResponse(sqlDefinition.jsonDefinition(), queryList());
    }

    @Override
    public List<Map<String, ?>> wrapperResultSet(ResultSet resultSet) throws SQLException {

        List<Map<String, ?>> list = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        while (resultSet.next()) {
            int rowSize = metaData.getColumnCount();
            Map<String, Object> map = new LinkedHashMap<>(8);
            for (int i = 1; i <= rowSize; i++) {
                String labelName = metaData.getColumnName(i);
                Object obj = resultSet.getObject(labelName);
                map.put(labelName, obj);
            }
            list.add(map);
        }

        return list;
    }

    @Override
    public PreparedStatement setArgument(PreparedStatement statement, int index, Object value) throws SQLException {

        if (value instanceof Boolean || value instanceof Number || value instanceof String) {

            statement.setObject(index + 1, value);
        } else {

            statement.setString(index + 1, value == null ? null : value.toString());
        }
        return statement;
    }


    @Override
    public PreparedStatement prepareHandle(Connection connection) throws SQLException {

        PreparedStatement prepareStatement;
        prepareStatement = connection.prepareStatement(sqlDefinition.value());
        List<?> valueList = sqlDefinition.prepareValueList();
        // 预编译处理
        if (!CollectionUtils.isEmpty(valueList)) {
            for (int i = 0; i < valueList.size(); i++) {

                prepareStatement = setArgument(prepareStatement, i, valueList.get(i));
            }
        }
        return prepareStatement;
    }

    @Override
    public String run() {

        return switch (sqlDefinition.identify()) {
            case ALTER, CREATE, DROP, TRUNCATE -> update();
            case SELECT -> query();
            default -> null;
        };
    }


    @Override
    public String wrapperJsonResponse(JsonDefinition jsonDefinition, Object result) {

        return jsonDefinition.serialize().toJson(result);
    }
}
