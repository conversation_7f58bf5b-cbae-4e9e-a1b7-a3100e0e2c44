package com.uino.x.pedestal.twin.jrm.json.serialize;


import com.google.gson.*;
import com.uino.x.pedestal.twin.jrm.core.json.definition.JsonDefinition;
import com.uino.x.pedestal.twin.jrm.core.json.serialize.JsonSerialize;
import com.uino.x.pedestal.twin.jrm.json.definition.DefaultJsonDefinition;
import org.springframework.lang.Nullable;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 基于{@link Gson}实现的json序列化
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/4/16 9:49
 */
public class GsonSerialize implements JsonSerialize {

    private final Gson gson = new GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
            .create();

    @Override
    public <E> Map<String, E> toJsonMap(JsonDefinition jsonDefinition) {

        @SuppressWarnings("unchecked") final Map<String, E> map = gson.fromJson(jsonDefinition.value(), LinkedHashMap.class);
        return map;
    }

    @Override
    public <E> JsonDefinition toJsonDefinition(Map<String, E> jsonMap) {

        return DefaultJsonDefinition.of(gson.toJson(jsonMap), this);
    }

    @Override
    public <T> T fromJson(String json, Class<T> typeClass) {

        return gson.fromJson(json, typeClass);
    }

    @Override
    public String toJson(Object obj) {

        return gson.toJson(obj);
    }

    @Nullable
    @Override
    public String firstKey(String json) {
        final JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        if (jsonObject.isEmpty()) {
            return null;
        }
        return jsonObject.keySet().iterator().next();
    }
}
