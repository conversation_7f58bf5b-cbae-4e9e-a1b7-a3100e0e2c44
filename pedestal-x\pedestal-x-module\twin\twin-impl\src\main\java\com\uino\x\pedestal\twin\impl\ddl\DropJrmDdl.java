package com.uino.x.pedestal.twin.impl.ddl;


import com.uino.x.pedestal.twin.common.enums.AutoTableTypeEnum;
import com.uino.x.pedestal.twin.jrm.JrmBean;
import com.uino.x.pedestal.twin.jrm.core.enums.RequestType;
import com.uino.x.pedestal.twin.pojo.domain.FormItem;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Drop ddl
 *
 * <AUTHOR>
 * @version 1.0.7
 * @date 2022/1/5 17:30
 */
@Slf4j
@Getter
public class DropJrmDdl extends AbstractJrmDdl implements JrmDdl {


    /**
     * 构造方法
     *
     * @param autoTableType
     * @param tableName       表名
     * @param formItem        表单列表
     * @param sourceStructure
     * <AUTHOR>
     * @date 2022/1/5 17:27
     */
    public DropJrmDdl(AutoTableTypeEnum autoTableType, String tableName, List<FormItem> formItem, String sourceStructure) {
        super(autoTableType, tableName, formItem, sourceStructure);
    }

    @Override
    public void invoke() {
        try {
            JrmBean.request(RequestType.DROP, this.sourceStructure);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
